// 忽略下面这行错误
// @ts-nocheck
import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 订阅管理入口
 * @steps 操作步骤
 * 1.点击项目组右侧icon，点击订阅管理
 * @expect 期望结果
 * 1.唤起新页面展示订阅管理展示管理tab，图表订阅，看板订阅，合并订阅
 */
test('subscribe-management-portal', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=16656&pageId=page_8kiogF9sf7&projectId=3',
  )
  await page.waitForTimeout(1000)
  await page.locator('.redbi-top-nav .nav-info .nav-sub-info .more > button').hover()
  await page.waitForTimeout(1000)
  const page1Promise = page.waitForEvent('popup')
  await page.getByText('订阅管理').click()
  const page1 = await page1Promise
  await expect(page1.locator('h6').filter({ hasText: '图表' })).toBeVisible()
  await expect(page1.locator('h6').filter({ hasText: '看板' })).toBeVisible()
  await expect(page1.locator('h6').filter({ hasText: '合并' })).toBeVisible()
  await expect(page1.locator('h6').filter({ hasText: '自助分析' })).toBeVisible()
  await expect(page1.locator('h6').filter({ hasText: '指标诊断' })).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 权限&入口
 * @steps 操作步骤
 * 1、权限用户（查看，编辑及以上权限）2、无权限用户
 * @expect 期望结果
 * 1、在看板的查看态，编辑态，预览态点击右上角“新建订阅”跳转至新建弹窗页2、跳转至权限申请页面
 */
test.describe('perm-and-entry', () => {
  test('with-permission', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=16656&projectId=3&pageId=page_8kiogF9sf7',
    )

    await page.getByRole('button', { name: '订阅' }).click()
    await expect(page.getByText('新建看板订阅')).toBeVisible()
  })

  test('no-permission', async ({ page, context }) => {
    await interceptor(context, '<EMAIL>')
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=1001&dashboardId=5487',
    )
    await page.waitForTimeout(2000)
    await expect(page.getByRole('button', { name: '订阅' })).toBeDisabled()
    await expect(page.getByText('暂无权限')).toBeVisible()
  })
})

/**
 * <AUTHOR>
 * @description 合并订阅-任务列表页
 * @steps 操作步骤
 * 0、权限管控1、搜索框2、筛选框3、列表字段4、点击订阅信息列表下拉框
 * @expect 期望结果
 * 0、项目管理员--展示此项目下所有看板产生的订阅任务其他用户--展示此项目下个人有权限查看的看板对应的订阅任务1、名称搜索-按照订阅名称或图表控件名称模糊搜索2、状态筛选默认展示全部状态-下拉选择订阅规则状态：已启用，未启用3、展示 订阅任务名称，订阅信息，推送方式，推送时间，最新推送时间，负责人，启用状态，操作项-推送记录，手动推送，编辑，删除4、展示该合并订阅合并的图表，点击图表可跳转至对应看板页条数大于50下方展示分页
 */
test('merge-subscribe-list', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/subscribe/list?projectId=3')
  // 名称搜索
  await page.getByRole('heading', { name: '合并' }).click()
  const searchText = '测试'
  await page.getByPlaceholder('搜索订阅关键词').fill(searchText)
  expect(await page.locator('tr').count()).toBeGreaterThanOrEqual(0)
  // 状态筛选
  // await page.waitForTimeout(1000)
  await page.locator('#app').getByText('全部状态').click()
  await page.getByText('未启用').click()
  expect(await page.locator('tr').count()).toBeGreaterThanOrEqual(0)
  if (await page.locator('tr').count()) {
    await expect(page.getByText('推送记录').first()).toBeVisible()
  }
  await page.getByRole('button', { name: '张图表' }).first().click()
  // await page.waitForTimeout(1000)
  const page1Promise = page.waitForEvent('popup')
  await page.locator('.chart-list-item').first().click()
  const page1 = await page1Promise
  await page.waitForTimeout(1000)
  await expect(page1.getByText('看板').nth(1)).toBeVisible()
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 图表订阅&看板订阅-任务列表页
 * @steps 操作步骤
 * 0、权限管控1、搜索框2、筛选框3、列表字段4、点击图表名列表下图表
 * @expect 期望结果
 * 0、项目管理员--展示此项目下所有看板产生的订阅任务其他用户--展示此项目下个人有权限查看的看板对应的订阅任务1、名称搜索-按照订阅名称或图表控件名称模糊搜索2、状态筛选默认展示全部状态-下拉选择订阅规则状态：已启用，未启用3、展示 订阅任务名称，图表名，推送方式，推送时间，最新推送时间，负责人，启用状态，操作项-推送记录，手动推送，编辑，删除4、跳转至对应看板页，图表会自动锚点至图表位置条数大于50下方展示分页
 */
test('dashboard-subscribe-list', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/subscribe/list?projectId=3')
  // 名称搜索
  await page.getByRole('heading', { name: '看板' }).click()
  const searchText = '测试'
  await page.getByPlaceholder('搜索订阅关键词').fill(searchText)
  expect(await page.locator('tr').count()).toBeGreaterThanOrEqual(0)
  // 状态筛选
  await page.waitForTimeout(1000)
  await page.locator('#app').getByText('全部状态').click()
  await page.getByText('未启用').click()
  expect(await page.locator('tr').count()).toBeGreaterThanOrEqual(0)
  if (await page.locator('tr').count()) {
    await expect(page.getByText('推送记录').first()).toBeVisible()
  }
  const page1Promise = page.waitForEvent('popup')
  await page.getByRole('row', { name: '推送记录' }).locator('path').nth(2).click()
  const page1 = await page1Promise
  await page.waitForTimeout(1000)
  await expect(page1.getByText('看板').nth(1)).toBeVisible()
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 新建合并订阅
 * @steps 操作步骤
 * 0、点击订阅管理-->合并订阅tab右上角“新建合并订阅”1、订阅任务名称-必填2、添加订阅图表3、推送时间-定时执行，默认每天10:004、推送方式-邮件5、收件人-默认创建人6、推送主题7、推送正文-默认正文内容：“您的订阅报告已生成，请查收。”，截图-不勾选，将图表数据作为表格附件-勾选8、是否启用9、订阅测试
 * @expect 期望结果
 * 0、唤起“新建合并订阅”弹窗1、限制50字符，超过-截断展示至50字2、双击手动搜索图表，复制粘贴图表名称，至多添加30张图表3、定时执行可手动更改频率每天，每周，每月，也可切换推送时间为“数据更新后”4、AN5、可单个添加收件人&点击右侧批量添加&添加邮件组6、可编辑主题，限制50字，超出无法输入7、可编辑正文内容，限制500字，超出无法输入，邮件展示数据附件；不勾选表格附件-邮件内容不展示表格附件8、默认不启用9、点击后可测试订阅效果，不展示在订阅列表
 */
test('create-merge-subscribe', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/subscribe/list?projectId=3&refType=2&redbi-screen-shot-token=test',
  )
  await page.getByRole('heading', { name: '合并' }).click()
  await page.getByRole('button', { name: '+ 新建合并订阅' }).click()
  const taskName = '测试1'
  await page.getByPlaceholder('请输入任务名称').fill(taskName)
  await page.getByPlaceholder('请添加图表').fill('看板')
  await page
    .locator('.base-item-info')
    // .filter({ hasText: /^看板$/ })
    .first()
    .click()
  await page.getByRole('button', { name: '添加', exact: true }).click()
  await page.waitForTimeout(500)
  await page.getByRole('button', { name: '批量添加' }).click()
  await page.waitForTimeout(500)
  await page.getByPlaceholder('请用,或者;进行分割').fill('河元,李松宇')
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  await page.waitForTimeout(500)
  await page.getByRole('button', { name: '订阅测试' }).click()
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.waitForTimeout(500)
  await expect(page.getByText(taskName).first()).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 新建看板订阅
 * @steps 操作步骤
 * 0、点击看板右上角“订阅”1、订阅任务名称-必填2、推送时间-定时执行，默认每天10:003、推送方式-默认企业微信消息4、收件人-默认创建人5、推送主题-默认看板名称6、推送正文默认正文内容：“您的订阅报告已生成，请查收。”，截图，链接全部勾选7、是否启用8、订阅测试9、点击确认
 * @expect 期望结果
 * 0、唤起“新建看板订阅”弹窗1、限制50字符，超过-截断展示至50字2、定时执行可手动更改频率每天，每周，每月3、可切换邮件，企业微信群-需要添加Webhook地址，推送正文显示@所有人4、可单个添加收件人&点击右侧批量添加，邮件可新增邮件组5、可编辑主题，限制50字，超出无法输入6、可编辑正文内容，限制500字，超出无法输入；取消勾选截图，链接-通知不展示截图，链接7、默认不启用8、点击后可测试订阅效果，不展示在订阅列表9、订阅消息根据配置的推送时间，和推送方式通知收件人
 */
test('create-dashboard-subscribe', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=3&dashboardId=16656&pageId=page_8kiogF9sf7&redbi-screen-shot-token=test',
  )
  await page.getByRole('button', { name: '订阅' }).click()
  const taskName = '测试'
  await page.getByPlaceholder('请输入任务名称').fill(taskName)
  await page.locator('div:nth-child(3) > .d-icon > svg').first().click()
  await page
    .locator('div')
    .filter({ hasText: /^每周$/ })
    .nth(3)
    .click()
  await page.getByText('频率每周').click()
  await page
    .locator('div')
    .filter({ hasText: /^每天$/ })
    .nth(3)
    .click()
  await page
    .locator('div')
    .filter({ hasText: /^REDcity群\/企微群$/ })
    .locator('span')
    .nth(1)
    .click()
  await page
    .getByPlaceholder('请输入webHook地址')
    .fill('https://qyapi.weixin.qq.com/cgi-bin/webhook/send??key=xxxxxxxx')
  await page.getByPlaceholder('您的订阅报告已生成，请查收。').fill('@所有人')
  await page
    .locator('div')
    .filter({ hasText: /^邮件$/ })
    .locator('span')
    .nth(1)
    .click()
  await page.getByRole('button', { name: '批量添加' }).click()
  await page.getByPlaceholder('请用,或者;进行分割').fill('河元,李松宇')
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  await page.getByPlaceholder('您的订阅报告已生成，请查收。').fill('您的订阅报告已生成，请查收')
  await page
    .locator('div')
    .filter({ hasText: /^截图$/ })
    .getByRole('img')
    .click()
  await page.getByRole('button', { name: '订阅测试' }).click()
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  const page1Promise = page.waitForEvent('popup')
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  const page1 = await page1Promise
  await page.waitForTimeout(2000)
  await expect(page1.getByText(taskName).first()).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 新建图表订阅
 * @steps 操作步骤
 * 0、点击图表控件右上角…，点击订阅1、订阅任务名称-必填2、推送时间-定时执行，默认每天10:003、推送方式-默认企业微信消息4、收件人-默认创建人5、推送主题-默认图表名称6、推送正文-默认正文内容：“您的订阅报告已生成，请查收。”，截图，链接全部勾选7、是否启用8、订阅测试9、点击确定
 * @expect 期望结果
 * 0、唤起“新建图表订阅”弹窗 1、限制50字符，超过-截断展示至50字2、定时执行可手动更改频率每天，每周，每月，也可切换推送时间为“数据更新后”3、可切换邮件，企业微信群-需要添加Webhook地址，推送正文显示@所有人4、可单个添加收件人&点击右侧批量添加，邮件可新增邮件组5、可编辑主题，限制50字，超出无法输入6、可编辑正文内容，限制500字，超出无法输入；取消勾选截图，链接-通知不展示截图，链接7、默认不启用8、点击后可测试订阅效果，不展示在订阅列表9、订阅消息根据配置的推送时间，和推送方式通知收件人
 */
// test('create-char-subscribe', async ({ page }) => {
//   await page.goto(
//     'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=16656&pageId=page_8kiogF9sf7&redbi-screen-shot-token=test',
//   )
//   await page.locator('.edit-dashboard-chart-wrapper').hover()
//   await page.locator('.edit-dashboard-chart-wrapper .operation').hover()
//   if (await page.getByText('我知道了').count() > 0) {
//     await page.getByText('我知道了').click()
//   }
//   await page.getByText('订阅').click()
//   const taskName = '测试'
//   await page.getByPlaceholder('请输入任务名称').fill(taskName)
//   await page
//     .locator('.radio-sub-select > div > div > .d-select > .d-grid > .d-select-content')
//     .first()
//     .click()
//   await page
//     .locator('div')
//     .filter({ hasText: /^每周$/ })
//     .nth(3)
//     .click()
//   await page
//     .locator('.radio-sub-select > div > div > .d-select > .d-grid > .d-select-content')
//     .first()
//     .click()
//   await page
//     .locator('div')
//     .filter({ hasText: /^每天$/ })
//     .nth(3)
//     .click()
//   await page
//     .locator('div')
//     .filter({ hasText: /^数据更新后$/ })
//     .locator('span')
//     .nth(1)
//     .click()
//   await page
//     .locator('div')
//     .filter({ hasText: /^定时执行$/ })
//     .locator('span')
//     .nth(1)
//     .click()
//   await page
//     .locator('div')
//     .filter({ hasText: /^企业微信群$/ })
//     .locator('span')
//     .nth(1)
//     .click()
//   await page
//     .getByPlaceholder('请输入webHook地址')
//     .fill('https://qyapi.weixin.qq.com/cgi-bin/webhook/send??key=xxxxxxxx')
//   await page.getByPlaceholder('您的订阅报告已生成，请查收。').fill('@所有人')
//   await page
//     .locator('div')
//     .filter({ hasText: /^邮件$/ })
//     .locator('span')
//     .nth(1)
//     .click()
//   await page.getByRole('button', { name: '批量添加' }).click()
//   await page.getByPlaceholder('请用,或者;进行分割').fill('梅新成，河元')
//   await page.getByRole('button', { name: '确定' }).nth(2).click()
//   await page
//     .locator('div')
//     .filter({ hasText: /^截图$/ })
//     .getByRole('img')
//     .click()
//   // await page.waitForTimeout(1000)
//   await page
//     .locator('div')
//     .filter({ hasText: /^链接$/ })
//     .locator('path')
//     .click()
//   await page
//     .locator('div:nth-child(3) > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator')
//     .first()
//     .click()
//   await page.getByRole('button', { name: '订阅测试' }).click()
//   await page.getByRole('button', { name: '确定' }).nth(2).click()
//   const page1Promise = page.waitForEvent('popup')
//   await page.getByRole('button', { name: '确定' }).nth(1).click()
//   const page1 = await page1Promise
//   await page1.waitForTimeout(2000)
//   await page1.waitForSelector('.d-v-loading', { state: 'detached' })
//   await expect(page1.getByText(taskName).first()).toBeVisible()
// })
