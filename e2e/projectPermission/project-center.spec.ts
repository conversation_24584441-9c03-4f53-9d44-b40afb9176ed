import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 编辑项目、子项目
 * @steps 操作步骤
 * 1. 1. 项目管理员
 * @expect 期望结果
 * 1. 项目管理员不可编辑项目管理员
 */
test('edit-project', async ({ page, context }) => {
  await interceptor(context, '<EMAIL>')
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/project/center/manage?projectId=3')
  await page.waitForTimeout(1000)
  await page.getByRole('button', { name: '编辑' }).click()
  // 不可编辑时，有该类名的P标签出现
  await expect(page.locator('.form-item-has-no-input-box')).toBeVisible()
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 用户权限标签
 * @steps 操作步骤
 * 1. 手动配置
2. 从数据表导入
 * @expect 期望结果
 * 完成配置后，在看板行权限配置能正常选择
 */
test.describe('user-permission-tag', () => {
  const userPermissionTagImport = 'mxc标签'
  const userPermissionTagHand = 'mxc标签2'
  test('add-tag', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/project/center/auth/authtag?projectId=3',
    )
    await page.waitForTimeout(1000)
    // 导入配置
    await page.getByRole('button', { name: '添加标签' }).click()
    await page.getByPlaceholder('请输入', { exact: true }).click()
    await page.getByPlaceholder('请输入', { exact: true }).fill(userPermissionTagImport)
    await page.getByPlaceholder('请选择数据源').click()
    await page
      .locator(
        '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
      )
      .click()
    await page.getByPlaceholder('请选择数据表，输入表名搜索').click()
    await page
      .locator(
        '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
      )
      .click()
    await page.getByPlaceholder('请选择').first().click()
    // await page.getByPlaceholder('请选择').first().fill('age')
    await page
      .locator(
        '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
      )
      .click()
    await page.getByPlaceholder('请选择').click()
    // await page.getByPlaceholder('请选择').fill('age')
    await page
      .locator(
        '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
      )
      .click()
    // await page.locator('.d-modal-footer .d-pagination').waitFor('detached')
    // 等待加载完成，隐藏了class为d-table-loading的元素
    await page.waitForSelector('.d-table-loading', { state: 'detached' })
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('创建成功').first()).toBeVisible()
    await page.waitForTimeout(2000)
    // 手动配置
    await page.getByRole('button', { name: '添加标签' }).click()
    await page.getByPlaceholder('请输入', { exact: true }).click()
    await page.getByPlaceholder('请输入', { exact: true }).fill(userPermissionTagHand)
    await page
      .locator('form div')
      .filter({ hasText: /^手动配置$/ })
      .locator('span')
      .nth(1)
      .click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('创建成功').first()).toBeVisible()
    await page.waitForTimeout(2000)
  })
  test('expect-add-tag', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17297&pageId=page_xq7uJETkxc&projectId=3',
    )
    await page.waitForTimeout(2000)
    await page.getByRole('button', { name: '我知道了' }).click()
    await page.locator('.d-space > button:nth-child(2)').click()
    await page
      .locator('div').filter({ hasText: /^行权限控制$/ }).first().click()
    await page.getByRole('button', { name: '添加规则' }).click()
    await page
      .locator(
        '.d-form-item__wrapper > .d-select-wrapper > .d-select > .d-grid > .d-select-content',
      )
      .click()
    await page
      .locator('.d-dropdown-wrapper > .d-input-wrapper > .d-input > .d-text')
      .first()
      .click()
    await page
      .locator('.d-dropdown-wrapper > .d-input-wrapper > .d-input > .d-text')
      .first()
      .fill(userPermissionTagImport)
    await expect(
      page.locator('.d-popover:visible').filter({ hasText: userPermissionTagImport }),
    ).toBeVisible()
    await expect(
      page.locator('.d-popover:visible').filter({ hasText: userPermissionTagHand }),
    ).toBeVisible()
    await page.waitForTimeout(1000)
  })
  test('del-tag', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/project/center/auth/authtag?projectId=3',
    )
    await page.waitForTimeout(1000)
    await page.getByPlaceholder('请输入名称搜索').click()
    await page.getByPlaceholder('请输入名称搜索').fill(userPermissionTagImport)
    await page.getByText('删除').first().click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('删除成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
    await page.getByText('删除').first().click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('删除成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
  })
})

/**
 * <AUTHOR>
 * @description 用户组管理
 * @steps 操作步骤
 * 1. 新增用户组
2. 编辑用户组成员、资源
 * @expect 期望结果
 * 1.能在授权时选择到
2.用户获得相应权限
 */
test.describe('user-group-manage', () => {
  const userGroupName = '自动测试mxc'
  test('add-user-group', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/project/center/auth/user?projectId=3',
    )
    await page.waitForTimeout(1000)
    await page.locator('#app').getByRole('button').nth(3).click()
    await page.getByPlaceholder('请输入', { exact: true }).click()
    await page.getByPlaceholder('请输入', { exact: true }).fill(userGroupName)
    await page.getByRole('button', { name: '添加成员' }).click()
    await page.getByPlaceholder('搜索薯名、姓名，支持批量输入邮箱（以英文分号分隔）').click()
    await page.getByPlaceholder('搜索薯名、姓名，支持批量输入邮箱（以英文分号分隔）').fill('河元')
    await page.locator('.item > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
    await page.getByText('添加用户如何给企微群开权限').click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '添加' }).first().click()
    await page.getByRole('heading', { name: '资源列表' }).click()
    await page.getByRole('button', { name: '添加资源' }).click()
    await page
      .locator(
        'div:nth-child(3) > .d-form-item__content > .d-form-item__wrapper > div > .d-select-wrapper > .d-select > .d-grid',
      )
      .click()
    await page.getByText('Hive自动化回归测试—勿动').click()
    await page
      .locator(
        'div:nth-child(2) > div > div:nth-child(2) > div > .d-space > .d-tree-node-checkbox-layout > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator',
      )
      .first()
      .click()
    await page
      .locator('div')
      .filter({ hasText: /^编辑查看$/ })
      .nth(1)
      .click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await page.getByRole('button', { name: '提交' }).click()
    await expect(page.getByText('创建成功').first()).toBeVisible()
    await page.waitForTimeout(2000)
  })
  test('expect-add-user-group-manage', async ({ page, context }) => {
    await page.waitForTimeout(1000)
    await interceptor(context, '<EMAIL>')
    await page.waitForTimeout(1000)
    await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24283&tab=字段配置')
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await expect(page.getByRole('button', { name: '编辑' })).toBeEnabled()
    await page.waitForTimeout(1000)
  })
  test('del-add-user-group', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/project/center/auth/user?projectId=3',
    )
    await page.waitForTimeout(1000)
    await page.locator('.list-item').filter({ hasText: userGroupName }).hover()
    await page.locator('.list-item').filter({ hasText: userGroupName }).locator('.d-button').click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('删除用户组成功').first()).toBeVisible()
    await page.waitForTimeout(2000)
  })
})
/**
 * <AUTHOR>
 * @description 权限申请
 * @steps 操作步骤
 * 1.无项目权限
2.无项目权限+无查看权限
3.无项目权限+无编辑权限
4.有项目权限+无查看权限
5.有项目权限+无编辑权限
6.有项目权限+无管理权限
7.有项目权限+无删除权限
 * @expect 期望结果
 * 点击看板/数据集进入页面&URL链接进入
1-3.页面展示白色无权限页面显示“请先申请项目权限”
4.页面右侧展示权限申请页
5.hover至右上角编辑按钮处，显示无权限编辑，可点此“申请权限”
6.页面正常展示，点击查看页右上角授权按钮或列表下授权管理--按钮置灰提示联系所有者添加“管理权限”
7.页面正常展示，正常编辑，点击删除时置灰提示仅所有者可操作
 */
test.describe('permission-application', () => {
  test('no-project-permission', async ({ page, context }) => {
    await interceptor(context, '<EMAIL>')
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=1004&dashboardId=14943&pageId=page_wkfEtdalsJ',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByText('申请内容').first()).toBeVisible()
    await expect(page.getByText('加入项目').first()).toBeVisible()
    await page.waitForTimeout(1000)
  })
  test('have-project-no-see', async ({ page, context }) => {
    await interceptor(context, '<EMAIL>')

    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=1001&dashboardId=6024',
      // 有权限类型，申请对象
    )
    await page.waitForTimeout(1000)
    await expect(page.getByText('权限类型').first()).toBeVisible()
    await expect(page.getByText('申请对象').first()).toBeVisible()
    await page.waitForTimeout(1000)
  })
  test('have-project-no-edit', async ({ page, context }) => {
    await interceptor(context, '<EMAIL>')
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=1001&dashboardId=17887',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByRole('button', { name: '编辑' })).toBeDisabled()
    await page.waitForTimeout(1000)
  })
  test('have-project-no-manage', async ({ page, context }) => {
    await interceptor(context, '<EMAIL>')
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=1001&dashboardId=1241',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByRole('button', { name: '授权' })).toBeDisabled()
    await page.waitForTimeout(1000)
  })
  test('have-project-no-del', async ({ page, context }) => {
    await interceptor(context, '<EMAIL>')
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=1001&dashboardId=17888',
    )
    await page.waitForTimeout(1000)
    await page
      .locator('.d-virtual-tree-node.bi-virtual-tree-node.d-virtual-tree-node-selected')
      .hover()
    await page
      .locator(
        '.d-virtual-tree-node.bi-virtual-tree-node.d-virtual-tree-node-selected .d-text.icon-wrap.ellipsis-icon',
      )
      .hover()
    await page.waitForTimeout(1000)
    await page
      .locator(
        '.d-popover:visible .d-options-wrapper .d-grid.d-options > div:nth-child(19) > .d-grid > .d-option-description > .d-option-name',
      )
      .hover()
    await page.waitForTimeout(1000)
    await expect(page.getByText('无删除权限，仅看板所有者').first()).toBeVisible()
  })
})
/**
 * <AUTHOR>
 * @description 授权他人
 * @steps 操作步骤
 * 1. 点击点击看板目录树的授权管理、看板右上角授权--编辑者、查看者：授权用户/用户组
2. 管理者：授权用户
 * @expect 期望结果
 * 用户获得相应权限
 */
test.describe('authorize-others-group', () => {
  test('authorize-others-see', async ({ page, context }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&projectId=3',
    )
    await page.waitForTimeout(1000)
    await page.getByText('订阅 授权 编辑').click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '授权' }).click()
    await page
      .getByPlaceholder('搜索薯名、姓名、部门或用户组，支持批量输入邮箱（以英文分号分隔）')
      .click()
    await page
      .getByPlaceholder('搜索薯名、姓名、部门或用户组，支持批量输入邮箱（以英文分号分隔）')
      .fill('朱维娜')
    await page.locator('.item > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
    await page.locator('div').filter({ hasText: /^授权管理$/ }).first().click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('操作成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
    await page.waitForTimeout(1000)
    await interceptor(context, '<EMAIL>')
    await page.waitForTimeout(1000)
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&pageId=page_xq7uJETkxc&projectId=3',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByRole('button', { name: '编辑' })).toBeDisabled()
    await expect(page.getByRole('button', { name: '授权' })).toBeDisabled()
    await page.waitForTimeout(1000)
  })
  test('authorize-others-edit', async ({ page, context }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&projectId=3',
    )
    await page.waitForTimeout(1000)
    await page.getByText('订阅 授权 编辑').click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '授权' }).click()
    await page.getByText('编辑者').click()
    await page
      .getByPlaceholder('搜索薯名、姓名、部门或用户组，支持批量输入邮箱（以英文分号分隔）')
      .click()
    await page
      .getByPlaceholder('搜索薯名、姓名、部门或用户组，支持批量输入邮箱（以英文分号分隔）')
      .fill('朱维娜')
    await page.locator('.item > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
    await page.locator('div').filter({ hasText: /^授权管理$/ }).first().click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('操作成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
    await page.waitForTimeout(1000)
    await interceptor(context, '<EMAIL>')
    await page.waitForTimeout(1000)
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&pageId=page_xq7uJETkxc&projectId=3',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByRole('button', { name: '编辑' })).toBeEnabled()
    await expect(page.getByRole('button', { name: '授权' })).toBeDisabled()
    await page.waitForTimeout(1000)
  })
  test('authorize-others-manage', async ({ page, context }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&projectId=3',
    )
    await page.waitForTimeout(1000)
    await page.getByText('订阅 授权 编辑').click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '授权' }).click()
    await page.getByText('管理员', { exact: true }).click()
    await page.getByPlaceholder('搜索薯名、姓名，支持批量输入邮箱（以英文分号分隔）').click()
    await page.getByPlaceholder('搜索薯名、姓名，支持批量输入邮箱（以英文分号分隔）').fill('朱维娜')
    await page.locator('.item > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
    await page.locator('div').filter({ hasText: /^授权管理$/ }).first().click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('操作成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
    await page.waitForTimeout(1000)
    await interceptor(context, '<EMAIL>')
    await page.waitForTimeout(1000)
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&pageId=page_xq7uJETkxc&projectId=3',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByRole('button', { name: '编辑' })).toBeEnabled()
    await expect(page.getByRole('button', { name: '授权' })).toBeEnabled()
    await page.waitForTimeout(1000)
  })
  test('del-authorize-others', async ({ page, context }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&projectId=3',
    )
    await page.waitForTimeout(1000)
    await page.getByText('订阅 授权 编辑').click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '授权' }).click()
    await page.locator('div').filter({ hasText: /^苏苏\(朱维娜\)$/ }).first().locator('svg')
      .click()
    await page.getByText('编辑者').click()
    await page.locator('div').filter({ hasText: /^苏苏\(朱维娜\)$/ }).first().locator('svg')
      .click()
    await page.getByText('管理员', { exact: true }).click()
    await page.locator('div').filter({ hasText: /^苏苏\(朱维娜\)$/ }).first().locator('svg')
      .click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('操作成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
    await interceptor(context, '<EMAIL>')
    await page.waitForTimeout(1000)
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17995&pageId=page_xq7uJETkxc&projectId=3',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByText('权限类型').first()).toBeVisible()
    await expect(page.getByText('申请对象').first()).toBeVisible()
    await page.waitForTimeout(1000)
  })
})
/**
 * <AUTHOR>
 * @description 转让所有者
 * @steps 操作步骤
 * 1.点击看板目录树的授权管理、看板右上角授权选择其他用户转让
 * @expect 期望结果
 * 新用户变为所有者，所有者自动成为管理员且不可删除
 */

test('transfer-owner', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17881&pageId=page_2aujeniTww&projectId=3',
  )
  await page.waitForTimeout(1000)
  await page.getByText('订阅 授权 编辑').click()
  await page.waitForTimeout(1000)
  await page.getByRole('button', { name: '授权' }).click()
  // await page.waitForTimeout(2000)
  // await page.waitForTimeout(1000)
  // await page.getByRole('button', { name: '授权' }).click();
  await page.locator('div:nth-child(2) > .d-select > .d-grid > .d-select-content').click()
  await page.getByRole('textbox').nth(4).click()
  await page.getByRole('textbox').nth(4).fill('河元')
  await page.locator('.user-select-option').click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('操作成功').first()).toBeVisible()

  await page.getByRole('button', { name: '授权' }).click()
  await page.getByText('管理员', { exact: true }).click()

  await expect(
    page.locator('.common-user-select-wraper .select-user-info').filter({ hasText: '河元' }),
  ).toBeVisible()
  // 判断tag只有两个span，没有删除按钮
  await expect(
    page
      .locator('.common-user-select-wraper .select-user-info')
      .filter({ hasText: '河元' })
      .locator('span'),
  ).toHaveCount(2)
  // await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 行权限配置
 * @steps 操作步骤
 * 1. 配置者：选择用户标签、数据字段，完成行权限配置 先做第一个
2. 看板消费者：仅能看到有权限的数据行，仅能下载有权限的数据行
 * @expect 期望结果
 * 看板数据正确展示，用户看不到权限外数据
 */
test.describe('row-permission-config', () => {
  // 用户权限标签为dbType,标签值为doris，无权限值为hive,总共40条数据
  // 1先删除对方查看权限，2设置行权限 ，3给对方加查看权限，4对方check，5删除行权限
  const userPermissionTag = 'dbType'
  let total = ''
  test('cancel-authorize-others', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?projectId=3&dashboardId=17898&disableCache=true',
    )
    await page.waitForTimeout(1000)
    await page.getByText('订阅 授权 编辑').click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '授权' }).click()
    await page.locator('div').filter({ hasText: /^苏苏\(朱维娜\)$/ }).first().locator('svg')
      .click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('操作成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
    // 查数据总条数
    const text = await page.locator('.d-pagination-total').innerText()
    const arr = text.match(/共\s*(\d+)\s*条/) || []
    total = arr[1]
  })
  test('config-row-permission', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17898&projectId=3&disableCache=true',
    )
    await page.waitForTimeout(1000)
    await page.locator('.d-space > button:nth-child(2)').click()
    await page.getByText('行权限控制').click()
    await page.getByRole('button', { name: '添加规则' }).click()
    await page.locator('.d-form-item__wrapper > .d-select-wrapper > .d-select > .d-grid > .d-select-content').click()
    await page.locator('.d-dropdown-wrapper > .d-input-wrapper > .d-input > .d-text').first().click()
    await page.locator('.d-dropdown-wrapper > .d-input-wrapper > .d-input > .d-text').first().fill(userPermissionTag)
    await page.locator('div:nth-child(2) > .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name').click()
    await page.locator('.d-space > div > .d-checkbox-simulator > .d-checkbox-indicator').click()
    await page.locator('div').filter({ hasText: /^请选择$/ }).nth(3).click()
    await page.getByPlaceholder('请搜索').click()
    await page.getByPlaceholder('请搜索').fill('db_type')
    await page.getByText('db_type').click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await page.waitForTimeout(1000)
  })
  test('authorize-others', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17898&projectId=3&disableCache=true',
    )
    await page.waitForTimeout(1000)
    await page.getByText('订阅 授权 编辑').click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '授权' }).click()
    await page
      .getByPlaceholder('搜索薯名、姓名、部门或用户组，支持批量输入邮箱（以英文分号分隔）')
      .click()
    await page
      .getByPlaceholder('搜索薯名、姓名、部门或用户组，支持批量输入邮箱（以英文分号分隔）')
      .fill('朱维娜')
    await page.locator('.item > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
    await page.locator('div').filter({ hasText: /^授权管理$/ }).first().click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await expect(page.getByText('操作成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
  })
  test('others-check', async ({ page, context }) => {
    await page.waitForTimeout(1000)
    await interceptor(context, '<EMAIL>')
    await page.waitForTimeout(1000)
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17898&projectId=3&disableCache=true',
    )
    await page.waitForTimeout(3000)
    // 设置行权限后，查到的数据条数应该小于等于总数
    const text = await page.locator('.d-pagination-total').innerText()
    const arr = text.match(/共\s*(\d+)\s*条/) || []
    const count = arr[1]
    await expect((parseInt(count, 10))).toBeLessThanOrEqual(parseInt(total, 10))
    await page.waitForTimeout(2000)
  })
  test('del-row-permission', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=17898&projectId=3&disableCache=true',
    )
    await page.waitForTimeout(1000)
    await page.locator('.d-space > button:nth-child(2)').click()
    await page.getByText('行权限控制').click()
    await page.getByText('删除').click()
    await page.getByRole('button', { name: '删除' }).click()
    await page.waitForTimeout(1000)
  })
})
