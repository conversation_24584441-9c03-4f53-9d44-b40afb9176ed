// 忽略下面这行错误
// @ts-nocheck
import { test, expect } from '@playwright/test'
import * as path from 'path'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 新建hive加速数据 集
 * @steps 操作步骤
 * 1.点击右上角[+新建数据集]，点击[在线数据集]，数据源选择hive，编辑数据集名称，选择保存位置，编辑表述，点击“下一步”
 * 2.选择数据表，编辑sql语句点击运行
 * 3. 点击加速设置，配置频率设置、依赖设置、资源设置、高级-运行参数，点击右上角提交，填写批量重跑范围
 * @expect 期望结果
 * 1.进入sql画布页，展示配置项
 * 2.运行成功，数据展示至数据预览tab @note 数据查询时间较长，只检测进度图是否出现
 * 3.成功新建数据集，页面展示所建数据集详情 @note 权限问题，新建操作无法完整执行，只检查提交校验
 */
test('create-hive-dataset', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=5560&redbi-screen-shot-token=test',
  )
  await page
    .getByRole('button', {
      name: '新建数据集',
    })
    .click()
  await page.getByText('在线数据集').click()
  await page.getByPlaceholder('请选择数据源').click()
  await page.getByText(/^Hive$/).click()
  await page.getByPlaceholder('请输入数据集名称，最多70个字符').dblclick()
  await page.getByPlaceholder('请输入数据集名称，最多70个字符').fill('自动化测试数据集')
  await page.waitForTimeout(1000)
  await page.getByRole('button', { name: '下一步' }).click()
  await page.getByRole('button', { name: '我知道了' }).click()
  await page.waitForTimeout(1000)
  await page.locator('.dataset-edit-datasource-table .d-tree-node-content-right').first().click({
    button: 'right',
  })
  await page.getByText('生成sql').click()
  await page.waitForTimeout(300)
  await page.getByRole('button', { name: '运行' }).click()
  await page.getByText('Query Progress')
  await page.getByText('加速设置').click()
  await page.getByPlaceholder('请选择项目').click()
  await page
    .locator(
      'div:nth-child(2) > .d-collapse-handler > .d-collapse-handler-main > .d-collapse-title',
    )
    .click()
  await page.getByRole('button', { name: '提交' }).click()
  // 由于一些权限问题，以及SQL查询十分耗时，新建操作无法完整执行，这里判断一下新建的校验错误能否正常显示
  await expect(
    page.getByText(/(加速设置 - 资源设置 - 项目不可为空)|(请先输入有效SQL并运行成功)/),
  ).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 新建文件数据集
 * @steps 操作步骤
 * 1.点击右上角[+新建数据集]，点击[文件数据集]
 * 2.点击上传文件选择上传1-3个excel，csv文件，每个大小不超过30M3.点击确定
 * @expect 期望结果
 * 1.页面弹出“新建文件数据集”弹窗
 * 2.弹出本地系统窗口，展示上传状态：上传中-上传成功-文件解析中-文件解析完成展示sheet
 * 3.成功新建文件数据集，页面展示所创建数据集详情
 */
test('create-file-dataset', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=5560&redbi-screen-shot-token=test',
  )
  await page
    .getByRole('button', {
      name: '新建数据集',
    })
    .click()
  await page.getByText('文件数据集').click()
  await page
    .locator('.d-upload__input')
    .setInputFiles(path.join(__dirname, './数据集新建测试数据.xlsx'))

  const name = `自动化_${new Date().getTime()}`
  await page
    .locator('.d-modal')
    .locator('.d-new-form-item')
    .nth(0)
    .getByPlaceholder('请输入')
    .fill(name)
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.waitForTimeout(3000)
  await expect(page.getByText(name).first()).toBeVisible()
  await page.close()
})

/**
 * <AUTHOR>
 * @description 文件数据集【追加数据】
 * @steps 操作步骤
 * 1.点击右上角[追加数据集]2.点击上传文件选择追加的文件，选择追加目标sheet3.点击预览4.点击确定
 * @expect 期望结果
 * 1.页面弹出“追加数据”弹窗2.成功上传文件，展示所选sheet数据3.可查看原始数据与追加数据，追加数据打标展示4.追加数据成功保存，页面弹窗关闭，展示至该数据预览页
 */
/**
 * <AUTHOR>
 * @description 文件数据集【替换数据】
 * @steps 操作步骤
 * 1.点击权限分配右侧追加数据集下拉框选择替换数据2.点击上传文件，选择替换文件3.点击预览4.点击确定
 * @expect 期望结果
 * 1.页面弹出“替换数据”弹窗2.自动填充相同字段名到替换字段列，无法匹配成功则默认为空，（未被匹配到的字段展示为新增列数据过多时页面展示“查看新增”）新增字段操作列展示删除按钮3.展示替换数据和新增数据4.追加数据成功保存，关闭弹窗页展示该数据预览页
 */
test.describe('file-dataset-operate-data', () => {
  // const value = 'sheet1测试'
  test('file-dataset-add-data', async ({ page }) => {
    await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24291&redbi-screen-shot-token=test')
    await page.getByRole('heading', { name: '数据预览' }).click()
    await page.getByRole('button', { name: '追加数据' }).click()
    await page
      .locator('.d-upload__input')
      .setInputFiles(path.join(__dirname, './数据集追加测试数据.xlsx'))
    await page.getByRole('button', { name: '预览' }).click()
    await page.getByRole('button', { name: '返回编辑' }).click()
    await page.getByRole('button', { name: '追加', exact: true }).click()
    // await page.locator('.d-table__header>tr>th').first().hover()
    // await page.locator('.d-table__th-cell-sort > svg > path').first().click()
    // await expect(page.getByText(value).first()).toBeVisible()
    await expect(page.getByText('追加数据成功').first()).toBeVisible()
    await page.waitForTimeout(1000)
  })

  test('file-dataset-replace-data', async ({ page }) => {
    await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24291&redbi-screen-shot-token=test')
    await page.locator('.wrap > button:nth-child(2)').click()
    await page.getByText('替换数据').click()
    await page
      .locator('.d-upload__input')
      .setInputFiles(path.join(__dirname, './数据集新建测试数据.xlsx'))
    await page.getByRole('button', { name: '预览' }).click()
    await page.getByRole('button', { name: '替换' }).click()
    // await page.locator('.d-table__header>tr>th').first().hover()
    // await page.locator('.d-table__th-cell-sort > svg > path').first().click()
    // await expect(page.getByText(value).first()).not.toBeVisible()
    // 接口有可能报错，所以用提示语判断
    await expect(page.getByText('数据替换成功').first()).toBeVisible()
  })
})

/**
 * <AUTHOR>
 * @description 切换数据集
 * @steps 操作步骤
 * 1.从看板（自助分析）点击右上角选择其他数据集2.确认切换数据集
 * @expect 期望结果
 * 1.看板和自助分析中已配置的图表字段被清空2.看板和自助分析中已配置的样式（总计设置、条件格式）被清空3.看板和自助分析中已配置的分析-联动的数据集字段映射关系信息被清空4.看板和自助分析中已配置的分析栏字段相关信息被清空
 */
test.describe('change-dataset', () => {
  test('analysis-change-dataset', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=3&analysisId=9226&redbi-screen-shot-token=test',
    )
    await page.getByText('Hive自动化回归测试—勿动').click()
    await page.getByText('我有权限的').click()
    await page.getByPlaceholder('Hive自动化回归测试').fill('ck自动化回归')
    await page.waitForTimeout(1000)
    await page.getByText('ck自动化回归').nth(1).click()
    await page.waitForTimeout(1000)
    await expect(
      page.locator('.right-top .field-drop-area').first().locator('.draggable-wrapper'),
    ).toHaveCount(0)
    await expect(
      page.locator('.right-top .field-drop-area').nth(2).locator('.draggable-wrapper'),
    ).toHaveCount(0)
  })

  test('dashboard-change-dataset', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_xq7uJETkxc&redbi-screen-shot-token=test',
    )
    await page
      .locator('div')
      .filter({ hasText: /^HIve自动回归$/ })
      .first()
      .click()
    if (await page.getByText('我知道了').count() > 0) {
      await page.getByText('我知道了').click()
    }
    await page.locator('#app').getByText('Hive自动化回归测试').click()
    await page.getByText('ck自动化回归').click()
    await page.waitForTimeout(1000)
    await page.getByText('替换数据集').nth(1).click()
    await page.waitForTimeout(1000)
    await expect(
      page.locator('.gui-panel .content-wrapper').first().locator('.draggable-wrapper'),
    ).toHaveCount(0)
    await expect(
      page.locator('.gui-panel .content-wrapper').nth(2).locator('.draggable-wrapper'),
    ).toHaveCount(0)
  })

  test('dashboard-replace-dataset', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_xq7uJETkxc&redbi-screen-shot-token=test',
    )
    await page
      .locator('div')
      .filter({ hasText: /^HIve自动回归$/ })
      .first()
      .click()
    if (await page.getByText('我知道了').count() > 0) {
      await page.getByText('我知道了').click()
    }
    await page.locator('#app').getByText('Hive自动化回归测试').click()
    await page.getByText('ck自动化回归').click()
    await page.waitForTimeout(1000)
    // await page.locator('a').filter({ hasText: '替换数据集' }).click()
    await page.getByText('替换数据集').nth(1).click()
    await page.waitForTimeout(1000)
    await expect(page.locator('.d-modal').filter({ hasText: '替换数据集' })).toBeVisible()
  })
})

/**
 * <AUTHOR>
 * @description 修改hive加速数据模型
 * @steps 操作步骤
 * 1. 调整sql内容2. 调整加速设置
 * @expect 期望结果
 * 1.数据预览数据为新sql数据，若所变更的字段或数据表无权限则弹出申请弹窗2-3.成功调整，所引用看板数据对应显示变更提示
 */
test.describe('edit-hive-modal-flow', () => {
  // test.setTimeout(60000)
  test('del-edit-hive-modal', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24283&redbi-screen-shot-token=test&tab=数据模型',
    )
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '数据模型' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '数据模型' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    await page.getByRole('button', { name: '编辑' }).click()
    await page.getByRole('button', { name: '我知道了' }).click()
    await page.waitForTimeout(1000)

    // 判断是否有scene字段，有则删除,防止删除成功，断言失败，再次执行，再删除scene字段找不到而报错
    if (await page.getByRole('code').locator('div').filter({ hasText: 'scene,' }).nth(4)
      .count()) {
      await page.getByRole('code').locator('div').filter({ hasText: 'scene,' }).nth(4)
        .click()
      await page.getByLabel('Editor content;Press Alt+F1').press('ControlOrMeta+x')
      await page.getByRole('button', { name: '运行' }).click()
      // await page.waitForTimeout(10000)
      // await expect(
      //   page.locator('.dataset-edit-preview>.d-table-v2.dataset-preview-table').first(),
      // ).toBeVisible()
      // await page.locator('.dataset-edit-preview>.d-table-v2.dataset-preview-table').waitFor()
      await page.waitForSelector('.progress-loading', { state: 'detached' })
      await page.getByRole('button', { name: '提交' }).click()
      await page.waitForTimeout(2000)

      if (await page.getByText('确定').count() > 1) {
        await page.getByRole('button', { name: '确定' }).nth(1).click()
      }

      await page.getByPlaceholder('请选择日期').nth(0).click()
      await page.locator('.d-datepicker-calendar .--color-primary').nth(0).click()
      await page.waitForTimeout(500)
      await page.locator('.d-datepicker-calendar .--color-primary').nth(0).click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
    }
  })
  test('expect-del-edit-hive-modal', async ({ page }) => {
    await page.waitForTimeout(1000)
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_xq7uJETkxc&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    await expect(page.getByText('HIve自动回归', { exact: true })).toBeVisible()
    await page.waitForTimeout(500)
  })
  test('add-edit-hive-modal', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24283&redbi-screen-shot-token=test&tab=数据模型',
    )
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '数据模型' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '数据模型' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    await page.getByRole('button', { name: '编辑' }).click()
    await page.getByRole('button', { name: '我知道了' }).click()
    await page.waitForTimeout(1000)
    await page.locator('div').filter({ hasText: /^select$/ }).dblclick()
    await page.getByLabel('Editor content;Press Alt+F1').type('select scene,')
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '格式化' }).click()
    await page.waitForTimeout(1000)
    await page.locator('.d-popover span').filter({ hasText: 'standard' }).first().click()
    await page.getByRole('button', { name: '运行' }).click()
    // await page.locator('.dataset-edit-preview>.d-table-v2.dataset-preview-table').waitFor()
    await page.waitForSelector('.progress-loading', { state: 'detached' })
    await page.getByRole('button', { name: '提交' }).click()
    await page.waitForTimeout(500)
    await page.getByPlaceholder('请选择日期').nth(0).click()
    await page.locator('.d-datepicker-calendar .--color-primary').nth(0).click()
    await page.waitForTimeout(500)
    await page.locator('.d-datepicker-calendar .--color-primary').nth(0).click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await page.waitForTimeout(1000)
  })
  test('expect-add-edit-hive-modal', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_xq7uJETkxc&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    // await page.getByText(/请更新字段后重试/).first().click()
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await page.locator('.gui-field-list > div').filter({ hasText: 'scene' }).hover()
    await page.waitForTimeout(1000)
    await page
      .locator('.gui-field-list > div')
      .filter({ hasText: 'scene' })
      .locator('.remove')
      .click()
    if (await page.getByText('我知道了').count() > 0) {
      await page.getByText('我知道了').click()
    }
    await page.getByPlaceholder('搜索字段').click()
    await page.getByPlaceholder('搜索字段').fill('scene')
    await page.locator('#dimension-wrapper div').filter({ hasText: 'scene' }).nth(2).dblclick()
    await page.getByRole('button', { name: '查询数据' }).click()
    // 判断分页器是否正常展示
    await expect(page.getByText('跳至').first()).toBeVisible()
    await page.getByRole('button', { name: '保存' }).nth(1).click()
    await page.waitForTimeout(1000)
  })
})
