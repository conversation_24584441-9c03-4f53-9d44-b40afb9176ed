// 忽略下面这行错误
// @ts-nocheck
import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})
/**
 * <AUTHOR>
 * @description 编辑字段配置
 * @steps 操作步骤
 * 1. 新增计算字段、分组字段2. 新增层级结构3. 新增枚举值映射，注意null、空值的配置4. 修改字段属性和类型
 * @expect 期望结果
 * 1. 正确创建相应内容2. 在看板、自助分析页面正确展示更新内容
 */
test.describe('hive-edit-field-config-flow', () => {
  const computedField = 'test33'
  const groupField = 'fenzutest'
  const cengjiField = 'cengjitest'
  // let cengjifirst = ''
  test('edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24283&redbi-screen-shot-token=test&tab=字段配置',
    )
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    // 如果不存在指定的计算字段和分组字段，则新增，防止重复新增而重名报错
    if (!(await page.getByText(computedField).first().count())) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page.getByRole('button', { name: '新增计算字段' }).click()
      await page.getByPlaceholder('请输入字段名称').fill(computedField)
      await page.locator('div:nth-child(3) > .pill-space').first().dblclick()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '提交', exact: true }).click()
      await page.getByRole('button', { name: '新增分组字段' }).click()
      await page.getByPlaceholder('请输入字段名称').fill(groupField)
      await page
        .locator('form div')
        .filter({ hasText: /^请选择$/ })
        .nth(3)
        .click()
      await page.waitForTimeout(2000)
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.getByRole('button', { name: '新增层级结构' }).click()
      await page.getByPlaceholder('请输入层级结构名称').fill(cengjiField)
      await page.getByPlaceholder('请选择').first().click()
      // 选列表中的第一行数据作为第一层级，解除的时候同样选择第一行数据
      // cengjifirst = await page
      //   .locator(
      //     '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
      //   ).innerText()
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByPlaceholder('请选择').click()
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      // 给第一行设置枚举值映射
      await page.locator('.d-table__cell--fixed-right > .d-space > a').first().click()
      // await page.locator('tbody > tr:nth-child(1) > td:nth-child(2) > div > div > input').fill('33')
      await page.getByRole('button', { name: '添加' }).click()
      await page
        .getByRole('row', { name: '/200 删除' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test')
      await page
        .getByRole('cell', { name: '/200' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test1')
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).toHaveText(computedField)
      await expect(page.getByText(groupField).first()).toHaveText(groupField)
      await page.waitForTimeout(1000)
    }
  })
  test('except-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_xq7uJETkxc&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    // await page.getByText('HIve自动回归已选择字段').click()
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await expect(page.getByText(computedField).first()).toBeVisible()
    await expect(page.getByText(groupField).first()).toBeVisible()
    await page.waitForTimeout(1000)
    // //and
    // await page
    //   .locator('#dimension-wrapper div')
    //   .filter({ hasText: computedField })
    //   .nth(2)
    //   .dblclick()
    // await page.getByRole('button', { name: '查询数据' }).click()
    // await page.getByRole('button', { name: '保存' }).click()
    // await page.waitForTimeout(1000)
  })
  test('delete-edit-field-config', async ({ page }) => {
    await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24283&tab=字段配置')
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    // 如果存在指定的计算字段和分组字段，则删除
    if (await page.getByText(computedField).first().count()) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page
        .getByRole('row', { name: `${computedField}` })
        .locator('a').filter({ hasText: '删除' })
        .click()
      await page
        .getByRole('row', { name: `${groupField}` })
        .locator('a').filter({ hasText: '删除' })
        .click()
      // 删除计算字段和分组字段后，页面只剩一个删除，即枚举映射
      await page
        .getByRole('row', { name: '-映射值' })
        .locator('a').filter({ hasText: '删除' })
        .click()
      // await page.getByText('删除').click()
      // 解除第一行的层级结构
      await page.waitForTimeout(1000)
      // await page
      //   .locator('.d-table__cell--fixed-right > .d-space')
      //   .first()
      //   .getByRole('button')
      //   .click()
      // 定位包含 analysis_type 的行
      const analysisTypeRow = page.locator('tr', { hasText: 'analysis_type' })

      // 在该行中定位 .d-table__cell--fixed-right > .d-space
      const fixedRightSpace = analysisTypeRow.locator('.d-table__cell--fixed-right > .d-space')

      // 在 .d-space 下找到按钮
      const button = fixedRightSpace.locator('button')

      // 点击按钮
      await button.click()
      await page.getByText('编辑层级').click()
      await page.getByRole('button', { name: '解除层级' }).click()
      await page.getByRole('button', { name: '确定' }).nth(2).click()
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).not.toBeVisible()
      await expect(page.getByText(groupField).first()).not.toBeVisible()
    }
  })
  test('except-delete-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_xq7uJETkxc&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    // await page.getByText('HIve自动回归已选择字段').click()
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    // await expect(page.getByText(computedField).first()).not.toBeVisible()
    // await expect(page.getByText(groupField).first()).not.toBeVisible()
    await expect(page.locator('.group.dimension-group')).not.toContainText(computedField)
    await expect(page.locator('.group.dimension-group')).not.toContainText(groupField)
    await page.waitForTimeout(1000)
    // //timeout after or
    // await expect(page.getByText('等字段已失效，请更新字段后重试').first()).toBeVisible()
    // await page.getByText('等字段已失效，请更新字段后重试').click()
    // await page.locator('.draggable-wrapper').filter({ hasText: computedField }).hover()
    // await page.waitForTimeout(1000)
    // await page
    //   .locator('div:nth-child(4) > .field-pill > span:nth-child(4) > svg > path:nth-child(3)')
    //   .click()
    // await expect(page.getByText(computedField).first()).not.toBeVisible()
    // await expect(page.getByText(groupField).first()).not.toBeVisible()
    // await page.getByRole('button', { name: '保存' }).click()
    // await page.waitForTimeout(1000)
  })
})

/**
 * <AUTHOR>
 * @description ck编辑字段配置
 * @steps 操作步骤
 * 1. 新增计算字段、分组字段2. 新增层级结构3. 新增枚举值映射，注意null、空值的配置4. 修改字段属性和类型
 * @expect 期望结果
 * 1. 正确创建相应内容2. 在看板、自助分析页面正确展示更新内容
 */
test.describe('ck-edit-field-config-flow', () => {
  const computedField = 'test33'
  const groupField = 'fenzutest'
  const cengjiField = 'cengjitest'
  test('edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24287&redbi-screen-shot-token=test&tab=字段配置',
    )
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    if (!(await page.getByText(computedField).first().count())) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page.getByRole('button', { name: '新增计算字段' }).click()
      await page.waitForTimeout(1000)
      await page.getByPlaceholder('请输入字段名称').fill(computedField)
      await page.locator('div:nth-child(3) > .pill-space').first().dblclick()
      // 检查页面中是否存在“我知道了”按钮，并点击
      const knowButton = page.getByRole('button', { name: '我知道了' }).first()
      if (await knowButton.isVisible()) {
        await knowButton.click()
      }

      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '提交', exact: true }).click()
      await page.getByRole('button', { name: '新增分组字段' }).click()
      await page.getByPlaceholder('请输入字段名称').fill(groupField)
      // 检查页面中是否存在“我知道了”按钮，并点击
      const knowButton2 = page.getByRole('button', { name: '我知道了' }).first()
      if (await knowButton2.isVisible()) {
        await knowButton2.click()
      }
      await page.getByPlaceholder('请选择开始日期').click()
      await page.locator('div:nth-child(2) > div:nth-child(4) > .d-datepicker-cell-center > .d-datepicker-cell-main').first().click()
      await page.locator('div:nth-child(3) > .d-datepicker-dates-wrapper > .d-datepicker-dates > div:nth-child(2) > div:nth-child(4) > .d-datepicker-cell-center > .d-datepicker-cell-main').click()
      // return
      // await page.waitForTimeout(1000)
      // await page.getByText('9', { exact: true }).nth(1).click()
      // await page.getByText('14', { exact: true }).nth(2).click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.getByRole('button', { name: '新增层级结构' }).click()
      await page.getByPlaceholder('请输入层级结构名称').fill(cengjiField)
      await page.getByPlaceholder('请选择').first().click()
      // 选列表中的第一行数据作为第一层级，解除的时候同样选择第一行数据
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByPlaceholder('请选择').click()
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      // 给第一行设置枚举值映射
      await page.locator('.d-table__cell--fixed-right > .d-space > a').first().click()
      await page.waitForTimeout(1000)
      const deleteCell = page.locator('#dict-modal-table-con').getByRole('cell', { name: '删除' })
      if (await deleteCell.count() > 0) {
        await deleteCell.click()
      }

      await page.getByRole('button', { name: '添加' }).click()
      await page
        .getByRole('row', { name: '/200 删除' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test')
      await page
        .getByRole('cell', { name: '/200' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test1')

      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).toHaveText(computedField)
      await expect(page.getByText(groupField).first()).toHaveText(groupField)
      await page.waitForTimeout(1000)
    }
  })
  test('except-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_m6WmYkh0pj&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    // await page.getByText('CK自动回归已选择字段').click()
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await expect(page.getByText(computedField).first()).toBeVisible()
    await expect(page.getByText(groupField).first()).toBeVisible()
  })
  test('delete-edit-field-config', async ({ page }) => {
    await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24287&tab=字段配置')
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    if (await page.getByText(computedField).first().count()) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page
        .getByRole('row', { name: `${computedField}` })
        .locator('a').filter({ hasText: '删除' })
        .click()
      await page
        .getByRole('row', { name: `${groupField}` })
        .locator('a').filter({ hasText: '删除' })
        .click()
      // 删除计算字段和分组字段后，页面只剩一个删除，即枚举映射
      await page.getByText('删除').first().click()
      // 解除第一行的层级结构
      await page.waitForTimeout(1000)
      await page
        .locator('.d-table__cell--fixed-right > .d-space')
        .first()
        .getByRole('button')
        .click()
      const analysisTypeRow = page.locator('tr', { hasText: 'dtm' })
      const fixedRightSpace = analysisTypeRow.locator('.d-table__cell--fixed-right > .d-space')
      const button = fixedRightSpace.locator('button')
      // 点击按钮
      await button.click()
      await page.getByText('编辑层级').click()
      await page.getByRole('button', { name: '解除层级' }).click()
      await page.getByRole('button', { name: '确定' }).nth(2).click()
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).not.toBeVisible()
      await expect(page.getByText(groupField).first()).not.toBeVisible()
    }
  })
  test('except-delete-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_m6WmYkh0pj&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await expect(page.locator('.group.dimension-group')).not.toContainText(computedField)
    await expect(page.locator('.group.dimension-group')).not.toContainText(groupField)
    await page.waitForTimeout(1000)
  })
})

/**
 * <AUTHOR>
 * @description doris编辑字段配置
 * @steps 操作步骤
 * 1. 新增计算字段、分组字段2. 新增层级结构3. 新增枚举值映射，注意null、空值的配置4. 修改字段属性和类型
 * @expect 期望结果
 * 1. 正确创建相应内容2. 在看板、自助分析页面正确展示更新内容
 */
test.describe('doris-edit-field-config-flow', () => {
  const computedField = 'test33'
  const groupField = 'fenzutest'
  const cengjiField = 'cengjitest'
  test('edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24288&redbi-screen-shot-token=test&tab=字段配置',
    )
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    if (!(await page.getByText(computedField).first().count())) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page.getByRole('button', { name: '新增计算字段' }).click()
      await page.getByPlaceholder('请输入字段名称').fill(computedField)
      await page.locator('.view-lines').click()
      await page.locator('.view-lines').click()
      await page.getByRole('textbox', { name: 'Editor content;Press Alt+F1' }).fill('COUNT(dtm)')
      await page.locator('div').filter({ hasText: /^COUNT\(dtm\)$/ }).first().click()

      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '提交', exact: true }).click()
      await page.getByRole('button', { name: '新增分组字段' }).click()
      await page.getByPlaceholder('请输入字段名称').fill(groupField)
      // 检查页面中是否存在“我知道了”按钮，并点击
      const knowButton = page.getByRole('button', { name: '我知道了' }).first()
      if (await knowButton.isVisible()) {
        await knowButton.click()
      }
      await page.getByPlaceholder('请选择开始日期').click()
      await page.locator('div:nth-child(2) > div:nth-child(4) > .d-datepicker-cell-center > .d-datepicker-cell-main').first().click()
      await page.locator('div:nth-child(3) > .d-datepicker-dates-wrapper > .d-datepicker-dates > div:nth-child(2) > div:nth-child(4) > .d-datepicker-cell-center > .d-datepicker-cell-main').click()
      // await page.getByText('9', { exact: true }).nth(1).click()
      // await page.getByText('14', { exact: true }).nth(2).click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.getByRole('button', { name: '新增层级结构' }).click()
      await page.getByPlaceholder('请输入层级结构名称').fill(cengjiField)
      await page.getByPlaceholder('请选择').first().click()
      // 选列表中的第一行数据作为第一层级，解除的时候同样选择第一行数据
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByPlaceholder('请选择').click()
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      // 给第一行设置枚举值映射
      await page.locator('.d-table__cell--fixed-right > .d-space > a').first().click()

      // 如果不存在 'test' 文本，则点击 "添加" 按钮
      const deleteCell = page.locator('#dict-modal-table-con').getByRole('cell', { name: '删除' })
      if (await deleteCell.count() > 0) {
        await deleteCell.click()
      }
      await page.getByRole('button', { name: '添加' }).click()
      await page
        .getByRole('row', { name: '/200 删除' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test')
      await page
        .getByRole('cell', { name: '/200' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test1')

      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).toHaveText(computedField)
      await expect(page.getByText(groupField).first()).toHaveText(groupField)
      await page.waitForTimeout(1000)
    }
  })
  test('except-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_cwDkBCYSRD&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    // await page.getByText('CK自动回归已选择字段').click()
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await expect(page.getByText(computedField).first()).toBeVisible()
    await expect(page.getByText(groupField).first()).toBeVisible()
  })
  test('delete-edit-field-config', async ({ page }) => {
    await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24288&tab=字段配置')
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    if (await page.getByText(computedField).first().count()) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page
        .getByRole('row', { name: `${computedField}` })
        .locator('a').filter({ hasText: '删除' })
        .click()
      await page
        .getByRole('row', { name: `${groupField}` })
        .locator('a').filter({ hasText: '删除' })
        .click()
      // 删除计算字段和分组字段后，页面只剩一个删除，即枚举映射
      await page.getByText('删除').first().click()
      // 解除第一行的层级结构
      const analysisTypeRow = page.locator('tr', { hasText: 'date_key' })
      const fixedRightSpace = analysisTypeRow.locator('.d-table__cell--fixed-right > .d-space')
      const button = fixedRightSpace.locator('button').nth(1)
      // 点击按钮
      await button.click()
      await page.getByText('编辑层级').click()
      await page.getByRole('button', { name: '解除层级' }).click()
      await page.getByRole('button', { name: '确定' }).nth(2).click()
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).not.toBeVisible()
      await expect(page.getByText(groupField).first()).not.toBeVisible()
    }
  })
  test('except-delete-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_cwDkBCYSRD&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await expect(page.locator('.group.dimension-group')).not.toContainText(computedField)
    await expect(page.locator('.group.dimension-group')).not.toContainText(groupField)
    await page.waitForTimeout(1000)
  })
})
/**
 * <AUTHOR>
 * @description BI编辑字段配置
 * @steps 操作步骤
 * 1. 新增计算字段、分组字段2. 新增层级结构3. 新增枚举值映射，注意null、空值的配置4. 修改字段属性和类型
 * @expect 期望结果
 * 1. 正确创建相应内容2. 在看板、自助分析页面正确展示更新内容
 */
test.describe('bi-edit-field-config-flow', () => {
  const computedField = 'test33'
  const groupField = 'fenzutest'
  const cengjiField = 'cengjitest'
  test('edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24290&redbi-screen-shot-token=test&tab=字段配置',
    )
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    await page.waitForSelector('button:has-text("编辑")')
    if (!(await page.getByText(computedField).first().count())) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page.waitForTimeout(1000)
      // 检查页面中是否存在“我知道了”按钮，并点击
      const knowButton = page.getByRole('button', { name: '我知道了' }).first()
      if (await knowButton.isVisible()) {
        await knowButton.click()
      }
      await page.getByRole('button', { name: '新增计算字段' }).click()
      await page.getByPlaceholder('请输入字段名称').fill(computedField)
      await page.locator('div:nth-child(1) > .pill-space').first().dblclick()

      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '提交', exact: true }).click()
      await page.getByRole('button', { name: '新增分组字段' }).click()
      await page.getByPlaceholder('请输入字段名称').fill(groupField)
      await page
        .locator('form div')
        .filter({ hasText: /^请选择$/ })
        .nth(3)
        .click()
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.getByRole('button', { name: '新增层级结构' }).click()
      await page.getByPlaceholder('请输入层级结构名称').fill(cengjiField)
      await page.getByPlaceholder('请选择').first().click()
      // 选列表中的第一行数据作为第一层级，解除的时候同样选择第一行数据
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByPlaceholder('请选择').click()
      await page
        .locator(
          '.d-popover:visible .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name',
        )
        .click()
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      // 给第一行设置枚举值映射
      await page.locator('.d-table__cell--fixed-right > .d-space > a').first().click()
      await page.getByRole('button', { name: '添加' }).click()
      await page
        .getByRole('row', { name: '/200 删除' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test')
      await page
        .getByRole('cell', { name: '/200' })
        .getByPlaceholder('请输入')
        .first()
        .fill('test1')
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).toHaveText(computedField)
      await expect(page.getByText(groupField).first()).toHaveText(groupField)
      await page.waitForTimeout(1000)
    }
  })
  test('except-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_Y13rg08oD0&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await expect(page.getByText(computedField).first()).toBeVisible()
    await expect(page.getByText(groupField).first()).toBeVisible()
  })
  test('delete-edit-field-config', async ({ page }) => {
    await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24290&tab=字段配置')
    await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()
    // await page.waitForTimeout(1000)
    // await page.getByRole('heading', { name: '字段配置' }).click()

    await page.waitForSelector('button:has-text("编辑")')
    if (await page.getByText(computedField).first().count()) {
      await page.getByRole('button', { name: '编辑' }).click()
      await page
        .getByRole('row', { name: `${computedField}` })
        .locator('a').filter({ hasText: '删除' }).first()
        .click()
      await page
        .getByRole('row', { name: `${groupField}` })
        .locator('a').filter({ hasText: '删除' })
        .click()
      // 删除计算字段和分组字段后，页面只剩一个删除，即枚举映射
      // await page.getByText('删除').click()
      await page
        .getByRole('row', { name: '映射值' })
        .locator('a').filter({ hasText: '删除' })
        .click()
      // 解除第一行的层级结构
      await page.waitForTimeout(1000)
      await page
        .locator('.d-table__cell--fixed-right > .d-space')
        .first()
        .getByRole('button')
        .click()
      await page.getByText('编辑层级').click()
      await page.getByRole('button', { name: '解除层级' }).click()
      await page.getByRole('button', { name: '确定' }).nth(2).click()
      await page.getByRole('button', { name: '保存' }).click()
      await page.waitForTimeout(1000)
      await page.getByRole('button', { name: '确定' }).nth(1).click()
      await page.waitForTimeout(1000)
      await expect(page.getByText(computedField).first()).not.toBeVisible()
      await expect(page.getByText(groupField).first()).not.toBeVisible()
    }
  })
  test('except-delete-edit-field-config', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?projectId=3&dashboardId=17297&pageId=page_Y13rg08oD0&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(1000)
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await expect(page.locator('.group.dimension-group')).not.toContainText(computedField)
    await expect(page.locator('.group.dimension-group')).not.toContainText(groupField)
    await page.waitForTimeout(1000)
  })
})
