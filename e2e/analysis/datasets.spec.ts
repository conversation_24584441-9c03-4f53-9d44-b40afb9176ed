// 忽略下面这行错误
// @ts-nocheck
import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 测试筛选
 * @setp 操作步骤
 * 1. 点击数据集市
 * 2. 点击第一个分类「不限」
 * 3. 点击「社区」分类
 * 4. 点击「交易」分类
 * 5. 点击「商业化」分类
 * 6. 点击页面右上角 select 组件切换到数据平台部门
 * @expect 期望结果
 * 1. 选择社区分类后有数据
 * 2. 选择交易分类后有数据
 * 3. 选择商业化分类后有数据
 * 4. 切换部门后数据正常显示
 */
test('test filter', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/analysis/edit?menu=dataset')
  await page.locator('.menu-nav div').filter({ hasText: /^数据集市$/ }).click()
  await page.getByRole('button', { name: '不限' }).first().click()
  await page.getByRole('button', { name: '社区' }).click()
  await page.locator('.d-meta-wrapper').nth(1).waitFor()
  // 搜索结果大于0
  expect(await page.locator('.d-meta-wrapper').count()).toBeGreaterThan(0)

  await page.getByRole('button', { name: '交易' }).click()
  await page.locator('.d-meta-wrapper').nth(1).waitFor()
  // 搜索结果大于0
  expect(await page.locator('.d-meta-wrapper').count()).toBeGreaterThan(0)

  await page.getByRole('button', { name: '商业化' }).click()
  await page.locator('.d-meta-wrapper').nth(1).waitFor()
  // 搜索结果大于0
  expect(await page.locator('.d-meta-wrapper').count()).toBeGreaterThan(0)

  // 切换项目
  await page.locator('.project-select >.d-select').click()
  await page.getByText('数据平台部数据平台部项目').click()

  await page.locator('.menu-nav div').filter({ hasText: /^数据集市$/ }).click()
  await page.locator('.d-meta-wrapper').nth(1).waitFor()
  // 搜索结果大于0
  expect(await page.locator('.d-meta-wrapper').count()).toBeGreaterThan(0)
})

/**
 * <AUTHOR>
 * @description 测试搜索
 * @setp 操作步骤
 * 1. 点击数据集市
 * 2. 点击点一个分类「不限」
 * 3. 在搜索框中输入「全量」并回车
 * @expect 期望结果
 * 数据正常显示且搜索文案被高亮显示
 */
test('test search', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?menu=dataset&redbi-screen-shot-token=test',
  )
  await page.locator('.menu-nav div').filter({ hasText: /^数据集市$/ }).click()
  await page.getByRole('button', { name: '不限' }).first().click()

  const searchText = '全量'
  await page.getByPlaceholder('可输入字段名称/数据集名称/分析名称/描述/负责人搜索').fill(searchText)
  await page.getByPlaceholder('可输入字段名称/数据集名称/分析名称/描述/负责人搜索').press('Enter')
  await page.locator('.d-meta-wrapper').nth(1).waitFor()
  // 搜索结果大于 0
  expect(await page.locator('.d-meta-wrapper').count()).toBeGreaterThan(0)
  // 搜索文案高亮
  expect(await page.locator('span', { hasText: searchText }).count()).toBeGreaterThan(0)
})

/**
 * <AUTHOR>
 * @description 测试我的收藏
 * @setp 操作步骤
 * 1. 点击数据集市
 * 2. 点击第一个分类「不限」
 * 3. 点击按钮「仅看我搜藏的」
 * @expect 期望结果
 * 数据正常显示
 */
test('test fav', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?menu=dataset&redbi-screen-shot-token=test',
  )
  await page.locator('.menu-nav div').filter({ hasText: /^数据集市$/ }).click()
  await page.getByRole('button', { name: '不限' }).first().click()
  await page.getByText('仅看我收藏的').click()
  await page.waitForTimeout(500)
  await page.locator('.d-meta-wrapper').first().waitFor()
  const count = await page.locator('.d-meta-wrapper').count()
  // 结果大于 0
  expect(count).toBeGreaterThan(0)
})

/**
 * <AUTHOR>
 * @description 测试数据集权限
 * @setp 操作步骤
 * 1. 点击数据集市
 * 2. hover 第一个数据集的模板
 * 3. 在弹出层中点击第一条数据中的立即使用按钮
 * @expect 期望结果
 * 正常跳转至分析详情页面
 */
test('test dataset permission', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?menu=dataset&redbi-screen-shot-token=test',
  )
  await page.locator('.menu-nav div').filter({ hasText: /^数据集市$/ }).click()
  await page.getByRole('button', { name: '不限' }).first().click()
  await page.locator('.d-meta-wrapper').first().getByRole('button').hover()
  await page.getByRole('button', { name: '立即使用' }).first().waitFor()
  await page.getByRole('button', { name: '立即使用' }).first().click()
  await expect(page).toHaveURL(/projectId=\d+&analysisId=\d+/)
})
