// 数据集分析模板
import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 修改数据集字段后, 数据字段失效
 * @step 操作步骤
 * 1. 打开BI自动化测试数据集页面
 * 2. 点击右上角+自助分析按钮
 * 3. 点击字段配置tab
 * 4. 点击编辑按钮
 * 5. 点击新增计算字段
 * 6. 在表单中依次填写以下内容字段名称: test1,字段表达式: [id],字段属性选择 维度
 * 7. 提交并继续新建
 * 8. 在表单中依次填入以下内容字段名称: test2,字段表达式: [id],字段属性选择 度量
 * 9. 点击提交按钮
 * 10. 点击编辑按钮
 * 11. 点击删除按钮删除 test1 字段
 * 12. 点击删除按钮删除 test2 字段
 * @expect 期望结果
 * 1. 在打开的自助分析页面中有新增的字段
 * 2. 在打开的自助分析页面中没有新增的字段
 */
test('test dataset fields', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?projectId=3&id=24291')
  const page1Promise = page.waitForEvent('popup')
  await page.getByRole('button', { name: '自助分析' }).click()
  const page1 = await page1Promise

  await page.bringToFront()
  await page.getByRole('heading', { name: '字段配置' }).click()
  if (!(await page.getByText('test000').first().count())) {
    await page.getByRole('button', { name: '编辑' }).click()
    await page.getByRole('button', { name: '新增计算字段' }).click()
    await page.getByPlaceholder('请输入字段名称').fill('test000')
    await page.locator('.monaco-editor textarea').fill('[trace_id]')
    await page.waitForTimeout(1000)
    // 检查页面中是否存在“我知道了”按钮，并点击
    const knowButton = page.getByRole('button', { name: '我知道了' }).first()
    if (await knowButton.isVisible()) {
      await knowButton.click()
    }
    // 检查页面中是否存在“我知道了”按钮，并点击
    const knowButton2 = page.getByRole('button', { name: '我知道了' }).first()
    if (await knowButton2.isVisible()) {
      await knowButton2.click()
    }
    await page
      .locator('div')
      .filter({ hasText: /^维度$/ })
      .nth(1)
      .click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '提交并继续新建' }).click()
    // 等待 test1 字段保存完
    // eslint-disable-next-line
  await page.getByPlaceholder('请输入字段名称').inputValue() === ''
    await page.waitForTimeout(1000)

    await page.getByPlaceholder('请输入字段名称').fill('test2')
    await page.locator('.monaco-editor textarea').fill('[trace_id]')
    await page
      .locator('div')
      .filter({ hasText: /^维度$/ })
      .nth(1)
      .click()
    await page.waitForTimeout(500)
    await page.getByRole('button', { name: '提交', exact: true }).click()
    await page.getByRole('button', { name: '保存' }).click()
    await page.locator('.d-drawer').waitFor({ state: 'hidden' })
  }

  await page1.bringToFront()
  // 手动触发 visibilitychange 事件
  await page1.evaluate(() => document.dispatchEvent(new Event('visibilitychange')))
  await page1.waitForTimeout(1000)
  expect((await page1.getByText('test000').count()) > 0)
  expect((await page1.getByText('test2').count()) > 0)

  await page1.getByText('test000').dblclick()
  await page1.getByText('test2').dblclick()

  await page.bringToFront()
  await page.getByRole('button', { name: '编辑' }).click()
  await page.getByRole('row', { name: 'test000' }).locator('a').last().click()
  await page.getByRole('row', { name: 'test2' }).locator('a').last().click()
  await page.getByRole('button', { name: '保存' }).click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()

  await page1.bringToFront()
  // 手动触发 visibilitychange 事件
  await page1.evaluate(() => document.dispatchEvent(new Event('visibilitychange')))
  await page1.waitForTimeout(1000)
  expect((await page1.getByText('test000').count()) === 0)
  expect((await page1.getByText('test2').count()) === 0)
})

/**
 * <AUTHOR>
 * @description 验证用户在 「数据集」中对特定字段进行设置,维度类型: 设定为必填写维度维度类型: 设定工位必须筛选维度类型: 设定为「仅用于筛选」日期类型: 设定最大查询天数
 * @step 操作步骤
 * 1. 打开BI自动化测试数据集页面
 * 2. 点击右上角+自助分析按钮
 * @expect 期望结果
 * 1. 设定维度为 {必填维度} 的维度出现在 「自助分析」模板的 「维度栏」中
 * 2. 设定维度为 {必须筛选} 的维度出现 「自助分析」模板的 「筛选栏」 中
 * 3. 设定为 {仅用于筛选} 的维度出现在 「自助分析」模板的 「筛选栏」中, 且该状态与 {必填维度} 彼此互斥
 */
test('test specific dataset fields', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/dataset/list?id=212&projectId=3')
  const page1Promise = page.waitForEvent('popup')
  await page.getByRole('button', { name: '自助分析' }).click()
  const page1 = await page1Promise

  const dimension = page1.locator('.field-drop-zone').nth(0)
  const filter = page1.locator('.field-drop-zone').nth(2)

  await page1.waitForSelector('.field-drop-zone')
  await page1.waitForTimeout(2000)

  expect(await dimension.getByText('三级部门').count()).toBeGreaterThan(0)
  expect(await filter.getByText('一级部门').count()).toBeGreaterThan(0)
  expect(await filter.getByText('三级部门').count()).toBeGreaterThan(0)
  expect(await filter.getByText('employeetype').count()).toBeGreaterThan(0)
})
