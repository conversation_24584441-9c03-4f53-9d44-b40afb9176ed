// 分析模板
import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'
import { websocket } from '../utils/websocket'
import { matchYearDoubleMonth, matchYearQuarter, waitSearchApiRequestDone } from '../utils'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 左侧数据集模块
 * @step 操作步骤
 * 1. 打开自助分析页面 https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=410070
 * 2. 点击数据集下拉框选择一个新的数据集“创作者表”
 * 3. 点击公共模板
 * 4. 搜索框中输入「日期」
 * 5. 清空搜索框
 * 6. 双击维度字段「用户ID」
 * 7. 双击指标字段「当天新增粉丝数」
 * 8. 拖动维度字段「用户昵称」到维度、指标、筛选区域
 * 9. 拖动指标字段「近7天新增粉丝数」到维度、指标、筛选区域
 * @expect 期望结果
 * 1. 右侧字段数据清空
 * 2. 展示对应数据集所有的公共模版
 * 3. 搜索到对应字段
 * 4. 维度字段被添加到维度栏
 * 5. 指标字段被添加到指标栏
 * 6. 维度字段可以被拖拽到维度、指标、筛选区域
 * 7. 指标字段可以被拖拽到指标、筛选区域，拖拽到维度区域时会有弹窗提示
 */
test('test left dataset', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=410070',
  )
  await page.locator('.dataset-select-container').click()
  await page.getByText('我有权限的').click()
  await page.getByText('创作者表').click()
  // return
  const dimension = page.locator('.field-drop-zone .analysis-field-list').nth(0)
  const measure = page.locator('.field-drop-zone').nth(1)
  const filter = page.locator('.field-drop-zone').nth(2)

  const dimensionFields = dimension.locator('.draggable-wrapper')
  const measureFields = measure.locator('.draggable-wrapper')
  const filterFields = filter.locator('.draggable-wrapper')

  expect((await dimensionFields.count()) === 0)
  expect((await measureFields.count()) === 0)

  await page.locator('a').filter({ hasText: '公共模板' }).click()
  expect((await page.locator('.template-item').count()) > 0)
  await page.locator('a').filter({ hasText: '公共模板' }).click()

  const searchText = '用户'
  await page.getByPlaceholder('搜索字段').fill(searchText)

  expect((await page.locator('.analysis-field-pill', { hasText: searchText }).count()) > 0)

  await page.getByPlaceholder('搜索字段').fill('')

  const dimensionField1 = '用户ID'
  await page.locator('.analysis-field-pill', { hasText: dimensionField1 }).dblclick()

  expect((await dimensionFields.last().getByText(dimensionField1).count()) > 0)

  const measureField1 = '当天新增粉丝数'
  await page.locator('#measure-wrapper').getByText('社交指标').click()
  await page.locator('.analysis-field-pill', { hasText: measureField1 }).dblclick()

  expect((await measureFields.last().getByText(measureField1).count()) > 0)

  // 拖拽
  const dimensionField2 = '用户昵称'
  // 第一次拖拽不生效，另外拖拽顺序也不能改
  await page
    .locator('.left-container .analysis-field-pill', { hasText: dimensionField2 })
    .dragTo(dimension)
  await page
    .locator('.left-container .analysis-field-pill', { hasText: dimensionField2 })
    .dragTo(measure)
  await page
    .locator('.left-container .analysis-field-pill', { hasText: dimensionField2 })
    .dragTo(dimension)
  await page
    .locator('.left-container .analysis-field-pill', { hasText: dimensionField2 })
    .dragTo(filter)

  expect((await dimensionFields.last().getByText(dimensionField2, { exact: true }).count()) > 0)
  expect((await measureFields.last().getByText(dimensionField2, { exact: true }).count()) > 0)
  expect((await filterFields.last().getByText(dimensionField2, { exact: true }).count()) > 0)

  const measureField2 = '近7天新增粉丝数'
  await page
    .locator('.left-container .analysis-field-pill', { hasText: measureField2 })
    .dragTo(dimension)
  if ((await page.getByRole('button', { name: '应用' }).count()) > 0) {
    await page.getByRole('button', { name: '应用' }).first().click()
  }
  // await page.getByRole('button', { name: '取消' }).click()
  if ((await page.getByRole('button', { name: '取消' }).count()) > 0) {
    await page.getByRole('button', { name: '取消' }).click()
  }
  // await page.getByRole('button', { name: '取消' }).click()
  // 确认弹窗，点击配置为维度
  await page.getByRole('button', { name: '配置为“维度”' }).click()
  await page
    .locator('.left-container .analysis-field-pill', { hasText: measureField2 })
    .dragTo(measure)
  await page
    .locator('.left-container .analysis-field-pill', { hasText: measureField2 })
    .dragTo(filter)

  expect((await dimensionFields.last().getByText(measureField2).count()) > 0)
  expect((await measureFields.last().getByText(measureField2).count()) > 0)
  expect((await filterFields.last().getByText(measureField2).count()) > 0)
})

/**
 * <AUTHOR>
 * @description 模板保存/另存为
 * @step 操作步骤
 * 1. 打开自己的模板链接
 * 2. 点击查询且查询数据结果成功返回 可以点击保存/另存为
 * 3. 打开他人的模板链接
 * 4. 点击查询且查询数据结果成功返回
 * @expect 期望结果
 * 1. 自己的模板可以点击保存/另存为
 * 2. 他人的模板只能点击另存为
 */
test('test save/save as', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=410070',
  )
  await waitSearchApiRequestDone(page)
  expect((await page.getByRole('button', { name: '另存为' }).count())).toBeGreaterThanOrEqual(0)
  expect((await page.getByRole('button', { name: '保存' }).count())).toBeGreaterThanOrEqual(0)

  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=3926&projectId=4&shortcutId=2948964',
  )
  await waitSearchApiRequestDone(page)
  expect((await page.getByRole('button', { name: '另存为' }).count())).toBeGreaterThanOrEqual(0)
  expect((await page.getByRole('button', { name: '保存' }).count())).toBeGreaterThanOrEqual(0)
})

/**
 * <AUTHOR>
 * @description 筛选区字段配置
 * @step 操作步骤
 * 1. 打开模板链接
 * 2. 日期筛选选择动态 // todo
 * 3. 日期筛选选择固定
 * 4. 字符串筛选（精确匹配、模糊匹配、不为空）
 * 5. 数字筛选（无聚合/聚合）
 * 6. 带聚合的字段筛选
 * 7. 层级结构的树形筛选
 * 8. 组合筛选
 * 9. 人群包筛选
 * 10. 笔记内容筛选
 * @expect
 * 1. 查询返回的条数等于预设的值
 */
test('筛选区字段配置', async () => {
  // const { waitForApiResponseWithTimeout } = websocket(page)
  // // 固定日期
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=17125&shortcutId=3050299&projectId=4',
  // )
  // await waitSearchApiRequestDone(page)
  // await page.locator('.d-daterangepicker-input-filter > .d-text').first().click()
  // await page.getByText('固定日期').click()
  // await page.getByText('固定日期').click({ force: true })
  // await page.getByRole('heading', { name: '年' }).first().click()
  // await page.locator('.--space-p-extra-small').first().click()
  // await page.getByText('2024', { exact: true }).click()
  // await page.getByRole('heading', { name: '月' }).first().click()
  // await page.getByText('11月').click()
  // await page
  //   .locator('div:nth-child(5) > .d-datepicker-cell-center > .d-datepicker-cell-main')
  //   .first()
  //   .click()
  // await page
  //   .locator('div:nth-child(5) > .d-datepicker-cell-center > .d-datepicker-cell-main')
  //   .first()
  //   .click()
  // await page.getByRole('button', { name: '应用' }).click()
  // await page.getByRole('button', { name: '查询', exact: true }).click()
  // const res = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/data', 10000)
  // if (res?.data?.dataList) {
  //   expect([101, 500]).toContain(res?.data?.dataList?.length)
  // }
  // //   await page.close()
  // // const newPage2 = await context.newPage()
  // //   //  // 模糊匹配
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=17125&shortcutId=3050027&projectId=4',
  // )
  // await waitSearchApiRequestDone(page)
  // if (await page.getByText('我知道了').count() > 0) {
  //   await page.getByText('我知道了').click()
  // }
  // await page.locator('div:nth-child(8) > .slot-wrapper > .filter-selector').click()
  // await page.locator('.separator > div > .d-select > .d-grid').first().click()
  // await page.locator('div').filter({ hasText: /^包含\(模糊匹配\)$/ }).nth(3).click()
  // // await page.getByPlaceholder('可使用换行符、制表符(\\t').fill('我')
  // await page.getByRole('button', { name: '应用' }).click()
  // await page.getByRole('button', { name: '查询', exact: true }).click()

  // const res2 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/data', 10000)
  // if (res2?.data?.dataList) {
  //   expect(res2.data.dataList.length).toBe(101)
  // }

  // // 不为空
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=17125&shortcutId=3050027&projectId=4',
  // )
  // await waitSearchApiRequestDone(page)
  // if (await page.getByText('我知道了').count() > 0) {
  //   await page.getByText('我知道了').click()
  // }
  // await page.locator('div:nth-child(8) > .slot-wrapper > .filter-selector').click()
  // await page.locator('.separator > div > .d-select > .d-grid').first().click()
  // await page.locator('div').filter({ hasText: /^不为空$/ }).nth(3).click()
  // await page.getByRole('button', { name: '应用' }).click()
  // await page.getByRole('button', { name: '查询', exact: true }).click()
  // const res3 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/data', 10000)
  // if (res3?.data?.dataList) {
  //   expect(res3.data.dataList.length).toBe(101)
  // }

  // // // 数字筛选
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=17221&shortcutId=2970879&projectId=3',
  // )
  // const res4 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  // if (res4?.data?.dataList) {
  //   expect([4, 0]).toContain(res4?.data?.dataList?.length)
  // }
  // // 层级结构树形筛洗
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=776820&shortcutId=3051222&projectId=3',
  // )
  // const res5 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/data', 10000)
  // if (res5?.data?.dataList) {
  //   expect([1, 0]).toContain(res5?.data?.dataList?.length)
  // }

  // // // 笔记内容
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=17270&shortcutId=2971123&projectId=4',
  // )
  // const res6 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/data', 10000)
  // if (res6?.data?.dataList) {
  //   expect(res6.data.dataList.length).toBe(1)
  // }
})

/**
 * <AUTHOR>
 * @description 验证 「自助分析」「维度栏」 中的日期类型的维度值设置为年-双月, 年-季度 后展示信息生效.
 * @step 操作步骤
 * 1. 打开模板链接
 * 2. 将日期分区设置为年-双月
 * 3. 将日期分区设置为年-季度
 * @expect
 * 1. 查询返回的值等于预设的值
 */
test('维度栏日期类型的维度值', async ({ page }) => {
  const { waitForApiResponseWithTimeout } = websocket(page)

  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=3&analysisId=17183&shortcutId=5017295',
  )

  await waitSearchApiRequestDone(page)
  await page.locator('.placement').first().click()
  await page.locator('div').filter({ hasText: /^日期类型$/ }).nth(1).click()
  await page.locator('div').filter({ hasText: /^年-双月例：2023年6-7月$/ }).nth(1).click()
  await page.getByRole('button', { name: '查询', exact: true }).click()
  const res = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res?.data?.dataList) {
    expect(res.data.dataList.slice(0, 10).every(v => matchYearDoubleMonth(v['DlmzR-CNT-YEAR-BIMONTH']))).toBe(true)
  }

  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=3&analysisId=17183&shortcutId=5017295',
  )
  await waitSearchApiRequestDone(page)
  await page.locator('.placement').first().click()
  await page.locator('div').filter({ hasText: /^日期类型$/ }).nth(1).click()
  await page.locator('div').filter({ hasText: /^年-季度例：2023Q2$/ }).nth(1).click()
  await page.getByRole('button', { name: '查询', exact: true }).click()
  let res2
  let attempts = 0
  const maxAttempts = 100
  while (attempts < maxAttempts) {
    // eslint-disable-next-line
    res2 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query',10000)
    if (res2?.data?.dataList) {
      break
    }
    // eslint-disable-next-line
    attempts++
  }
  expect(res2?.data?.dataList?.slice(0, 10).every(v => matchYearQuarter(v['DlmzR-CNT-YEAR-QUARTER']))).toBe(true)
})

/**
 * <AUTHOR>
 * @description 列占比计算数值
 * @step 操作步骤
 * 1. 选择指标栏中的 属性字段 将指标栏中的指标设定不同的「聚合方式」后, 配合 「高级计算」中的 「按列」 「占比」 进行计算.
 * @expect
 * 1. 查询返回的值等于预设的值
 */
test('列占比计算数值', async () => {
  // const { waitForApiResponse } = websocket(page)

  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=17125&shortcutId=2966831&projectId=4',
  // )
  // const res = await waitForApiResponse(request => request.api === '/dashboard/dashboard/pivot-convert-query')
  // if (res?.data?.dataList) {
  //   expect(res.data.dataList[0]['MxPy8-SUM']).not.toBeUndefined()
  //   expect(res.data.dataList[1]['MHBPs-SUM']).not.toBeUndefined()
  // }
})

/**
 * <AUTHOR>
 * @description 自定义占比计算数值
 * @step 操作步骤
 * 设置 『自定义』占比计算之后, 数值展示正确, 且设置不同 『聚合方式』的数值设置占比之后, 数值结果保持不变.
 * @expect
 * 1. 查询返回的值等于预设的值
 */
test('自定义占比计算数值', async () => {
  // const { waitForApiResponse } = websocket(page)
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=17125&shortcutId=130574',
  // )
  // const res = await waitForApiResponse(request => request.api === '/dashboard/dashboard/pivot-convert-query')
  // if (res?.data?.dataList) {
  //   expect(res.data.dataList.length).toBeGreaterThan(0)
  // }
})

/**
 * <AUTHOR>
 * @description 集成测试: 开启总计开启不同「聚合方式」 下的按列, 自定义占比同时开启 「日期对比」
 * @step 操作步骤
 * 打开指定的分析页面
 * @expect
 * 1. 查询返回的值等于预设的值
 */
test('集成测试', async ({ page }) => {
  const { waitForApiResponseWithTimeout } = websocket(page)
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=17125&shortcutId=130574',
  )
  const res = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 1000)
  if (res?.data?.dataList) {
    expect(res.data.dataList.length).toBeGreaterThan(0)
  }
})

/**
 * <AUTHOR>
 * @description 日期对比专项
 * @step 操作步骤
 * 打开指定的分析页面
 * @expect
 * 1. 查询返回的值等于预设的值
 */
test('日期对比专项', async ({ page, context }) => {
  await interceptor(context, '<EMAIL>')
  const { waitForApiResponseWithTimeout } = websocket(page)
  // 年-季度
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=825273&projectId=3&shortcutId=3221947',
  )
  const res = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res?.data?.dataList) {
    expect(res?.data?.dataList?.length).toBe(2)
  }

  // 年-双月
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=3&analysisId=825278&shortcutId=3221868',
  )
  const res2 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res2?.data?.dataList) {
    expect(res2?.data?.dataList?.length).toBe(2)
  }

  // 年-周
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=825300&projectId=3&shortcutId=3222071',
  )
  const res3 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res3?.data?.dataList) {
    expect(res3?.data?.dataList?.length).toBe(10)
  }

  // 年-月-日
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=825313&projectId=3&shortcutId=3222166',
  )
  const res4 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res4?.data?.dataList) {
    expect(res4?.data?.dataList?.length).toBe(70)
  }

  // 季度
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=825389&shortcutId=3222293&projectId=3',
  )
  const res5 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res5?.data?.dataList) {
    expect(res5?.data?.dataList?.length).toBe(2)
  }

  // 月
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=825401&projectId=3&shortcutId=3222395',
  )
  const res6 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res6?.data?.dataList) {
    expect(res6?.data?.dataList?.length).toBe(4)
  }

  // 周
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=825410&projectId=3&shortcutId=3222453',
  )
  const res7 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res7?.data?.dataList) {
    expect(res7?.data?.dataList?.length).toBe(10)
  }

  // 日
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=825415&projectId=3&shortcutId=3222496',
  )
  const res8 = await waitForApiResponseWithTimeout(request => request.api === '/dashboard/dashboard/pivot-convert-query', 10000)
  if (res8?.data?.dataList) {
    expect(res8?.data?.dataList?.length).toBe(31)
  }
})
