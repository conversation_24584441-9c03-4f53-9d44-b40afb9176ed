import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'
import { websocket } from '../utils/websocket'
import { isNumberInAscendingOrder } from '../utils/index'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 维度区字段配置
 * @setp 操作步骤
 * 1、文字和数字数据类型的字段可配置：显示为（图片、链接、文本）2、日期类型字段可配置：日期类型3、排序方式（不排序，升序，降序）
 * @expect 期望结果
 * 1、显示对应的显示效果2、显示对应的日期类型，字段胶囊展示日期类型3、展示对应的排序方式，字段胶囊展示排序方式
 */
test('dimension-field-config', async () => {
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=775714&projectId=3&redbi-screen-shot-token=test',
  // )
  // await page.locator('.placement').first().click()
  // await page.getByText('显示为').click()
  // await page.getByText('链接').click()
  // await page.getByRole('button', { name: '确定' }).nth(1).click()
  // await page.locator('div').filter({ hasText: /^dtm \(年-月-日\)$/ }).first().click()
  // await page.getByText('日期类型').click()
  // await page.getByText('年', { exact: true }).click()
  // await page.locator('div').filter({ hasText: /^dtm \(年\)$/ }).first().click()
  // await page.getByText('排序', { exact: true }).click()
  // await page.getByText('升序').click()
  // await page.getByRole('button', { name: '查询' }).click()

  // await expect(page.locator('div').filter({ hasText: /^dtm \(年\)$/ }).first()).toContainText(/年/)
  // // 有排序类名展示
  // await expect(page.locator('div').filter({ hasText: /^dtm \(年\)$/ }).first().locator('div span')
  //   .first()).toHaveClass(/icon-order/)
  // await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 指标区字段配置
 * @setp 操作步骤
 * 1、聚合方式2、高级计算-占比3、配置好数据或百分比的数据格式4、排序
 * @expect 期望结果
 * 1、单选，选择后字段胶囊展示聚合方式，数据展示对应的聚合数据2、选择后字段胶囊展示占比文案，数据展示正确3、展示对应的数据格式4、展示对应的排序方式，字段胶囊展示排序方式
 */
test('indicator-field-config', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=3&analysisId=775029&redbi-screen-shot-token=test',
  )
  const { waitForApiResponse } = websocket(page)
  await expect(page.locator('div').filter({ hasText: /^query_second \(求和\)$/ }).first()).toContainText('求和')
  await expect(page.locator('div').filter({ hasText: /^aaa \(求和- 占比\)$/ }).first()).toContainText('占比')
  // 有排序类名展示
  await expect(page.locator('div').filter({ hasText: /^aaa \(求和- 占比\)$/ }).first().locator('div span')
    .first()).toHaveClass(/icon-order/)
  const res = await waitForApiResponse(request => request.api === '/dashboard/dashboard/pivot-convert-query')
  const data = res.data.dataList.filter((item:any) => item['Mq7wD-SUM']).map((item: any) => item['Mq7wD-SUM'])
  // 判断升序
  expect(isNumberInAscendingOrder(data)).toBe(true)
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 日期对比
 * @setp 操作步骤
 * 1、分析指标：选择指标区内的字段
2、对比日期：选择维度区内的时间字段，不同日期粒度（日/周/月）的日期对比
3、对比类型，同期/非同期的4、数据设置5、显示位置6、点击确定
 * @expect 期望结果
 * 1、多选，对比所选择的指标字段2、单选，对比所选择的日期维度3、对比所选则的对比类型，和是否同期4、多选，展示所选择的数据5、单选，展示所选择的位置6、直接发起查询
 */
test('compare-date', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?analysisId=775676&projectId=3&redbi-screen-shot-token=test',
  )
  const { waitForApiResponse } = websocket(page)
  await page.waitForTimeout(2000)

  await page.getByText('图表', { exact: true }).click()
  await page.locator('.chart-icon').first().click()
  // return
  await page.getByRole('button', { name: '日期对比' }).click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await waitForApiResponse(request => request.api === '/dashboard/dashboard/pivot-convert-query')
  // expect(res.data.dataList.length).toBe(2)
  if (await page.getByText('我知道了').count() > 0) {
    await page.getByText('我知道了').click()
  }
  // expect([2, 0]).toContain(res?.data?.dataList?.length)
  await page.waitForTimeout(1000)
})
