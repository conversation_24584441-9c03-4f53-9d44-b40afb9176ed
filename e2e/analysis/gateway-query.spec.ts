import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, 'cheng<PERSON><EMAIL>')
})

/**
 * <AUTHOR>
 * @description 网关-问题回归: 查询参数错误
 * @setp 操作步骤
 * 1. 访问查询模版
 * @expect 期望结果
 * 1. 直接复用问题模板链接, 错误提示信息正常弹出 && 展示即可.
 */
test('gateway query error', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=8205&shortcutId=2468363&disableCache=true&redbi-screen-shot-token=test',
  )

  await expect(page.getByText(/字段类型有误/)).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 网关-问题回归: 查字段失效问题
 * @setp 操作步骤
 * 1. 访问查询模版
 * @expect 期望结果
 * 1. 直接复用问题模板链接, 错误提示信息正常弹出 && 展示即可.
 */
test('gateway field invaild', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=8151&shortcutId=38113&disableCache=true&redbi-screen-shot-token=test',
  )
  await expect(page.getByText(/字段已失效/)).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 网关-问题回归: 数据查询超时、预估执行时间太久
 * @setp 操作步骤
 * 1. 访问查询模版
 * @expect 期望结果
 * 1. 直接复用问题模板链接, 错误提示信息正常弹出 && 展示即可.
 */
// 比较消耗资源先注释
// test('gateway time too long', async ({ page }) => {
//   await page.goto(
//     'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&disableCache=true&analysisId=8386&shortcutId=124318&disableCache=true&redbi-screen-shot-token=test',
//   )
//   await expect(page.getByText(/(预计至少用时)|(查询量较大)|(排队中)|(数据异常)/)).toBeVisible({ timeout: 5.5 * 60 * 1000 })
// })

/**
 * <AUTHOR>
 * @description 网关-问题回归: 网关-问题回归: AYZ0025-函数不存在--用户表达式问题
 * @setp 操作步骤
 * 1. 访问查询模版
 * @expect 期望结果
 * 1. 直接复用问题模板链接, 错误提示信息正常弹出 && 展示即可.
 */
test('gateway error expression', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=8395&shortcutId=2469349&disableCache=true&redbi-screen-shot-token=test',
  )
  await expect(page.getByText(/(查询失败)|(数据异常)|(语法错误)|(请刷新重试)|(反馈异常)/).first()).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 网关-问题回归: 网关-问题回归: AYZ0026-用户错误使用计算函数
 * @setp 操作步骤
 * 1. 访问查询模版
 * @expect 期望结果
 * 1. 直接复用问题模板链接, 错误提示信息正常弹出 && 展示即可.
 */
test('gateway error func', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=8406&shortcutId=2469400&disableCache=true&redbi-screen-shot-token=test',
  )
  await expect(page.getByText(/(查询失败)|(数据异常)|(语法错误)|(请刷新重试)|(反馈异常)/).first()).toBeVisible()
})
