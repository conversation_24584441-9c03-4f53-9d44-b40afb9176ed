import { test, expect } from '@playwright/test'
// import dayjs from 'dayjs'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 创集UI自动化测试分析副本-xxx
 * @step 操作步骤
 * 1. 打开页面左侧UI自动化目录，点击「测试分析」进入分析详情页面
 * 2. 点击页面右上角「另存为」按钮
 * 3. 在弹出的对话框中把名称改为 “测试分析副本-xxx”，并点击确定按钮
 * 4. 点击「返回自助中心」返回到自助分析首页
 * @expect 期望结果
 * 成功创建标题为“测试分析副本-xxx”的分析模版
 */
// const analysisName = `副本-${dayjs().format('YYYYMMDDHHmm')}`
// const analysisName = '副本-202412071520'
test('create analysis replicator', async () => {
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/analysis/edit?projectId=4&analysisId=17125&shortcutId=3050299',
  // )
  // // await page
  // //   .locator('div')
  // //   .filter({ hasText: /^测试分析$/ })
  // //   .nth(1)
  // //   .click()
  // await page.waitForSelector('button:has-text("另存为")')
  // await page.getByRole('button', { name: '另存为' }).click()
  // await page.locator('form input[type="text"]').click()
  // await page.locator('form input[type="text"]').fill(analysisName)
  // await page.getByRole('button', { name: '确定' }).nth(1).click()

  // const duplicateNameSelector = page.getByText('分析名称重复')
  // try {
  //   await duplicateNameSelector.waitFor({ state: 'visible', timeout: 5000 })
  //   const isDuplicateNameVisible = await duplicateNameSelector.isVisible()
  //   if (isDuplicateNameVisible) {
  //     return
  //   }
  // } catch (error) {
  //   console.error('分析名称重复提示未出现')
  // }
  // await page.locator('#app').getByText('返回自助中心').click()
  // await page.locator('.d-menu-item__title', { hasText: '我的分析' }).waitFor()
  // await page.waitForTimeout(500)
  // await page.locator('.d-menu-item__title', { hasText: '我的分析' }).click()
  // if (await page.getByText('我知道了').count() > 0) {
  //   await page.getByText('我知道了').click()
  // }
  // await page.locator('.d-virtual-tree-node', { hasText: 'UI自动化' }).click()
  // await page.waitForTimeout(500)
  // expect((await page.getByText(analysisName).count())).toBeGreaterThan(0)
})

/**
 * <AUTHOR>
 * @description 删除UI自动化测试分析副本-xxx
 * @step 操作步骤
 * 1. 鼠标移动到测试分析副本-xxx标题上
 * 2. 点击右侧的删除 icon
 * 3. 在确认框点击确定
 * @expect 期望结果
 * 成功删除标题为“测试分析副本-xxx”的分析模版
 */
test('delete analysis replicator', async () => {
  // await page.goto('https://redbi.devops.beta.xiaohongshu.com/analysis/edit?menu=template&projectId=4')
  // const target = page
  //   .locator('.recommend-list >div')
  //   .filter({ has: page.getByRole('heading', { name: analysisName, exact: true }) })
  //   // .filter({ has: page.getByRole('heading', { name: '副本-20241128210637', exact: true }) })

  // // await page.locator('.recommend-list >div', { has: page.getByRole('heading', { name: '副本-20241128210119' }) })
  // // await page.locator('.recommend-list >div',{ has: page.getByRole('heading', { name: '副本-20241128210637'})})
  // await page.locator('.d-menu-item__title', { hasText: '我的分析' }).waitFor()
  // await page.locator('.d-menu-item__title', { hasText: '我的分析' }).click()
  // if (await page.getByText('我知道了').count() > 0) {
  //   await page.getByText('我知道了').click()
  // }
  // await page.waitForTimeout(500)
  // await page.locator('.d-virtual-tree-node', { hasText: 'UI自动化' }).click()
  // await target.hover()
  // await target.getByRole('button').nth(1).click()
  // await page.getByRole('button', { name: '确定' }).nth(1).click()
  // expect((await page.getByText(analysisName, { exact: true }).count()) === 0)
})

/**
 * todo: 无法实现
 * <AUTHOR>
 * @description 从页面左侧入口编辑UI自动化分析模板
 * @step
 * 1. 在页面左侧树形目录中找到测试分析副本模板，将鼠标移动至标题右侧三个点
 * 2. 点击设置按钮
 * 3. 把名称改为测试分析副本2
 * 4. 把描述改为测试分析副本2描述
 * 5. 点击确定
 * @expect
 * 成功修改上述内容
 */
test('edit analysis replicator via directory tree', async () => {
  // await page.goto('https://redbi.devops.xiaohongshu.com/analysis/edit?menu=dataset')
  // await page.locator('测试分析副本').hover()
  // await page.locator('.d-virtual-tree-node-title').last().hover({ force: true })
  // await page.locator('.node-title-wrap').last().hover({ force: true })
  // await page.locator('.d-virtual-tree-node').last().hover({ force: true })
  // await page.getByText('设置').last().click()
  // if(await page.getByTitle('测试分析副本').isVisible()){
  // }
  // await page.locator('.vue-recycle-scroller__item-view span').last().hover({ force: true })
  // await page.locator('.vue-recycle-scroller__item-view span[title="测试分析副本"]').hover()
  // await page
  //   .locator('.vue-recycle-scroller__item-view span', { hasText: '...' })
  //   .last()
  //   .hover({ force: true })
  // await page
  //   .locator('.vue-recycle-scroller__item-view', { hasText: '测试分析副本' })
  //   .locator('span', { hasText: '...' })
  //   .click({ force: true })
  // await page
  //   .locator('.vue-recycle-scroller__item-view', { hasText: '测试分析副本' })
  //   .getByRole('button', { name: '设置' })
  //   .click()
  // await page.locator('form .d-new-form-item').nth(0).fill('测试分析副本2')
  // await page.locator('form .d-new-form-item').nth(1).fill('测试分析副本2描述')
  // await page.getByRole('button', { name: '确定' }).nth(1).click()
  // expect(page.getByText('测试分析副本2')).not.toBeNull()
  // expect(page.getByText('测试分析副本2描述')).not.toBeNull()
})

/**
 * <AUTHOR>
 * @description 从页面右侧入口编辑UI自动化分析模板
 * @step
 * 1. 在页面右侧找到测试分析副本模板，点击标题右侧的“编辑”图标
 * 2. 把名称改为测试分析副本2
 * 3. 把描述改为描述2
 * 4. 点击确定
 * 5. 重复上述步骤，把名称还原为测试分析副本，描述还原为描述
 * @expect
 * 1. 成功修改上述内容
 * 2. 成功还原上述内容
 */
test('edit analysis replicator via right content', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/analysis/edit?menu=template&projectId=4')
  let target = page
    .locator('.recommend-list >div')
    .filter({ has: page.getByRole('heading', { name: /^测试分析副本$/ }) })
  await page.locator('.d-menu-item__title', { hasText: '我的分析' }).waitFor()
  await page.waitForTimeout(500)
  await page.locator('.d-menu-item__title', { hasText: '我的分析' }).click()

  await target.hover({ force: true })
  await target.getByRole('button').nth(0).click()

  await page.getByText('/20').click()
  await page.locator('form input[type="text"]').fill('测试分析副本2')
  await page.locator('div').filter({ hasText: /^描述2\/100$/ }).getByPlaceholder('请输入').click()
  await page.locator('div').filter({ hasText: /^描述2\/100$/ }).getByPlaceholder('请输入').fill('描述2')

  await page.getByRole('button', { name: '确定' }).nth(1).click()
  expect((await page.getByText('测试分析副本2', { exact: true }).count()) > 0)
  expect((await page.getByText('描述2', { exact: true }).count()) > 0)

  target = page
    .locator('.recommend-list >div')
    .filter({ has: page.getByRole('heading', { name: /^测试分析副本2$/ }) })
  await target.hover({ force: true })
  await target.getByRole('button').nth(0).click()

  await page.getByText('/20').click()
  await page.locator('form input[type="text"]').fill('测试分析副本')
  await page.locator('div').filter({ hasText: /^描述3\/100$/ }).getByPlaceholder('请输入').click()
  await page.locator('div').filter({ hasText: /^描述3\/100$/ }).getByPlaceholder('请输入').fill('描述')
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  expect((await page.getByText('测试分析副本', { exact: true }).count()) > 0)
  expect((await page.getByText('描述', { exact: true }).count()) > 0)
})

/**
 * <AUTHOR>
 * @description 筛选分析模板
 * @step 操作步骤
 * 1. 在右上角搜索框中输入测试
 * @expect
 * 搜索结果中展示标题带有“测试”的模板
 */
test('test search', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/analysis/edit?menu=template')
  await page.locator('.d-menu-item__title', { hasText: '我的分析' }).waitFor()
  await page.waitForTimeout(500)
  await page.locator('.d-menu-item__title', { hasText: '我的分析' }).click()
  await page.getByPlaceholder('可输入分析名称/数据集名称/描述搜索').fill('测试')
  expect((await page.getByRole('heading', { name: '测试' }).count()) > 0)
})
