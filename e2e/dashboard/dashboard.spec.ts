import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'
import { getYesterdayDate, isDateInAscendingOrder, isNumberInAscendingOrder } from '../utils/index'
import {
  filterDataList, advancedComputing4DataList, aggregationMethodDataList1, aggregationMethodDataList2, aggregationMethodDataList3, aggregationMethodDataList4, aggregationMethodDataList5, aggregationMethodDataList6,
} from './chatData'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})
// 从筛选器开始
/**
 * <AUTHOR>
 * @description 看板引用数据集存在日期字段,
 * @step 操作步骤
 * 1.选择最新数据日期
2.动态值/固定值
 * @expect 期望结果
 * 1.正确读取数据集最新数据日期
2.筛选结果符合预期
 */
test('filter-date', async ({ page }) => {
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_ZHeLbeADQb__32379'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25551&pageId=page_8kiogF9sf__32379&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  await page.waitForTimeout(3000)
  const dataList = privotJson.data.dataList
  await expect(JSON.stringify(dataList)).toBe(JSON.stringify(filterDataList))
  await page
    .locator('div')
    .filter({ hasText: /^未命名筛选项至--查询重置$/ })
    .nth(2)
    .hover()
  await page.locator('.group-wrapper-menu > .d-button').hover()
  await page.getByText('编辑').click()
  await page
    .locator('div:nth-child(5) > .d-select-wrapper > .d-select > .d-grid > .d-select-content').nth(0)
    .click()
  await page
    .locator('div')
    .filter({ hasText: /^动态日期$/ })
    .nth(3)
    .click()
  await page.getByText('最新数据日期').nth(0).click()
  await page.getByText('昨天').first().click()
  const responsePromisePrivot2 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_ZHeLbeADQb__32379'),
  )
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.waitForTimeout(2000)
  const privotResponse2 = await responsePromisePrivot2
  const privotJson2 = await privotResponse2.json()
  const dataList2 = privotJson2.data.dataList
  await expect(JSON.stringify(dataList2).includes(getYesterdayDate())).toBe(true)
})

/**
 * <AUTHOR>
 * @description 看板内存在可用图表,
 * @step 操作步骤
 * 1. 选择筛选器，自动解析选项、从数据集选择字段
2.过滤、自定义排序
 * @expect 期望结果
 * 1. 展示正确的筛选枚举值、能搜索到枚举值
2. 在选项列表中按已过滤的内容、已排序的结果正确展示
 */
test('filter-select', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25552&pageId=page_8rTWcBihpK_32380&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  await page.getByText('自定义配置').click()
  await page
    .locator('div')
    .filter({ hasText: /^筛选控件$/ })
    .getByRole('button')
    .click()
  await page.getByText('选择筛选器').click()
  await page
    .locator(
      'div:nth-child(2) > div > div:nth-child(2) > .d-tree-node > div > .d-tree-node-checkbox-layout > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator',
    )
    .click()
  await page.getByPlaceholder('请选择字段').click()
  await page.getByPlaceholder('请选择字段').fill('设备类型')
  await page.locator('.d-option-name').filter({ hasText: /^设备类型$/ }).click()
  await page.getByRole('button', { name: '筛选', exact: true }).click()
  // await page.locator('div:nth-child(3) > .d-select > .d-grid > .d-select-content').click();
  await page.getByPlaceholder('请输入').click()
  await page.getByPlaceholder('请输入').fill('er')
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  await page
    .locator('div')
    .filter({ hasText: /^下拉框$/ })
    .nth(3)
    .click()
  await page.getByText('单选按钮').click()
  await page
    .locator('div')
    .filter({ hasText: /^显示“全部”选项$/ })
    .locator('path')
    .first()
    .click()
  // await page.getByRole('button', { name: '确定' }).nth(1).click();
  await page.getByRole('button', { name: '排序 自定义' }).click()
  await page.getByText('自定义').nth(2).click()
  await page.waitForTimeout(3000)
  await page.locator('.drag-item').filter({ hasText: 'others' }).locator('button').click()
  await page.getByRole('button', { name: '确定' }).nth(2).click()

  const responsePromisePrivot = page.waitForResponse(response => response.url().includes('/api/dataset/field/dimensions/data'))
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const positions = privotJson.data.positions
  await page.waitForTimeout(2000)
  await expect(page.getByText('others').first()).toBeVisible()
  await expect(positions[0]).toBe('others')
})

/**
 * <AUTHOR>
 * @description 看板内存在可用图表,
 * @step 操作步骤
 * 1.设置多层结构（3-5级）
2.设置层级结构
 * @expect 期望结果
 * 能正确展示枚举值
 */
test('filter-tree', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25552&pageId=page_8rTWcBihpK_32380&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  await page.getByText('自定义配置').click()
  await page
    .locator('div')
    .filter({ hasText: /^筛选控件$/ })
    .getByRole('button')
    .click()
  await page.getByText('树形筛选器').click()
  await page
    .locator(
      'div:nth-child(2) > div > div:nth-child(2) > .d-tree-node > div > .d-tree-node-checkbox-layout > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator',
    )
    .click()
  await page.getByRole('button', { name: '添加层级' }).click()
  await page.getByPlaceholder('请选择字段').click()
  await page.getByPlaceholder('请选择字段').fill('设备类型')
  // await page.locator('.d-option-name').click()
  await page.locator('.d-option-name').filter({ hasText: /^设备类型$/ }).click()
  await page
    .locator('div')
    .filter({ hasText: /^层级 1设备类型$/ })
    .getByRole('button')
    .nth(1)
    .click()
  await page.getByPlaceholder('请选择字段').click()
  // await page.getByPlaceholder('请选择字段').fill('访问用户id');
  await page.getByText('访问用户id').nth(1).click()
  // return
  // await page.locator('.d-option-name').click();
  // await page.locator('div:nth-child(14) > .d-dropdown-wrapper > .d-dropdown-content > .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name').click();
  await page
    .locator('div')
    .filter({ hasText: /^层级 2访问用户id$/ })
    .getByRole('button')
    .nth(1)
    .click()
  await page.getByPlaceholder('请选择字段').click()
  await page.getByText('浏览器ua').nth(2).click()
  // await page.getByPlaceholder('请选择字段').fill('浏览器ua');
  // await page.locator('div:nth-child(15) > .d-dropdown-wrapper > .d-dropdown-content > .d-options-wrapper > div > div:nth-child(3) > .d-grid > .d-option-description > .d-option-name').click();
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.locator('.d-new-cascader__labelWrapper').click()
  await page.waitForTimeout(1000)
  await expect(page.getByText('Mac').first()).toBeVisible()
  await expect(page.getByText('Windows').first()).toBeVisible()
  await page.getByText('Mac', { exact: true }).click()

  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_pTKI16JJfW'),
  )
  await page.getByRole('button', { name: '查询' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList
  await page.waitForTimeout(1000)
  await expect(JSON.stringify(dataList).includes('Mac')).toBe(true)
})

/**
 * <AUTHOR>
 * @description 看板内存在可用图表,
 * @step 操作步骤
 * 1. 数值类型聚合、非聚合均可筛选
2.配置不同的聚合条件
 * @expect 期望结果
 * 1.关注不同字段的默认聚合方式，已聚合的字段不可再切换
2.筛选结果符合预期
 */
test('filter-aggregation', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25553&pageId=page_Uy47QzJPqn__32381&projectId=3&redbi-screen-shot-token=test',
  )
  await page.getByPlaceholder('请输入').first().fill('1')
  await page.getByPlaceholder('请输入').nth(1).fill('1000')
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('subScene=edit&chartId=chart_TPTShrPZqZ__32381'),
  )
  await page.getByRole('button', { name: '查询' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList
  // 判断每一个 MVwAu-SUM 是否都在 1 到 1000 之间
  const isValid = dataList.every(item => item['MVwAu-SUM'] > 1 && item['MVwAu-SUM'] < 1000)
  await expect(isValid).toBe(true)
  await page.waitForTimeout(1000)
  await page.locator('.group-wrapper-menu > .d-button').hover()
  await page.getByText('编辑').click()
  await page.locator('div').filter({ hasText: /^可选条件在区间$/ }).getByRole('img').first()
    .click()
  await page.getByText('大于', { exact: true }).click()
  await page.locator('div').filter({ hasText: /^默认值大于$/ }).getByPlaceholder('请输入').click()
  await page.locator('div').filter({ hasText: /^默认值大于$/ }).getByPlaceholder('请输入').fill('1000')
  const responsePromisePrivot2 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_TPTShrPZqZ__32381'),
  )
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  const privotResponse2 = await responsePromisePrivot2
  const privotJson2 = await privotResponse2.json()
  const dataList2 = privotJson2.data.dataList
  // 判断每一个 MVwAu-SUM 都大于1000
  const isValid2 = dataList2.every(item => item['MVwAu-SUM'] > 1000)
  await expect(isValid2).toBe(true)
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 看板内存在可用图表,
 * @step 操作步骤
 * 设置多级联动（3级）
 * @expect 期望结果
 * 联动筛选能正确展示枚举值
 * 筛选结果符合预期
 */
test('filter-cascade', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25554&pageId=page_ESOJJKjrM_32382&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(500)
  const responsePromiseDimensions = page.waitForResponse(
    response => response.url().includes('/api/dataset/field/dimensions/data'),
    // && response.url().includes('subScene=edit&chartId=chart_pTKI16JJfW'),
  )
  await page.getByText('全部').click()
  const dimensionsResponse = await responsePromiseDimensions
  const dimensionsJson = await dimensionsResponse.json()
  const positions = dimensionsJson.data.positions
  await expect(JSON.stringify(positions).includes('800_600')).toBe(true)
  await page
    .locator('div')
    .filter({ hasText: /^800_600$/ })
    .nth(3)
    .click()
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_9I6RYcEeRr_32382'),
  )
  await page.getByRole('button', { name: '查询' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList
  await expect(JSON.stringify(dataList).includes('800_600')).toBe(true)
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 看板内存在可用图表,
 * @step 操作步骤
 * 1. 数值类型聚合、非聚合均可筛选
2.配置不同的聚合条件
 * @expect 期望结果
 * 1.关注不同字段的默认聚合方式，已聚合的字段不可再切换
2.筛选结果符合预期
 */
test('filterBox-aggregation', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=18703&pageId=page_JFYor48uH5&projectId=3&redbi-screen-shot-token=test',
  )
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(5) > .placement').click()
  await page.locator('form').getByText('等于').click()
  await page.getByText('在区间', { exact: true }).click()
  await page.getByPlaceholder('请输入').first().click()
  await page.getByPlaceholder('请输入').first().fill('1')
  await page.getByPlaceholder('请输入').nth(1).click()
  await page.getByPlaceholder('请输入').nth(1).fill('1000')
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('subScene=edit&chartId=chart_CA9p9DaUFJ'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList
  // 判断每一个 MVwAu-SUM 是否都在 1 到 1000 之间
  const isValid = dataList.every(item => item['MSZHP-SUM'] > 1 && item['MSZHP-SUM'] < 1000)
  await expect(isValid).toBe(true)
  await page.waitForTimeout(1000)
  await page.locator('div:nth-child(5) > .placement').click()
  await page.locator('form').getByText('在区间').click()
  await page.getByText('大于', { exact: true }).click()
  await page.getByPlaceholder('请输入').click()
  await page.getByPlaceholder('请输入').fill('1000')
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  const responsePromisePrivot2 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('subScene=edit&chartId=chart_CA9p9DaUFJ'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse2 = await responsePromisePrivot2
  const privotJson2 = await privotResponse2.json()
  const dataList2 = privotJson2.data.dataList
  // 判断每一个 MVwAu-SUM 都大于1000
  const isValid2 = dataList2.every(item => item['MSZHP-SUM'] > 1000)
  await expect(isValid2).toBe(true)
  await page.waitForTimeout(1000)
})
/**
 * <AUTHOR>
 * @description 看板内存在可用图表,
 * @step 操作步骤
 * 1.按列表选择，
过滤选项，排除、批量粘贴选项
2. 按条件选择，不为空、不等于等条件设置
 * @expect 期望结果
 * 筛选结果符合预期
 */
test('filter-selectByList', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25555&pageId=page_zMqbadkO_32383&projectId=3&redbi-screen-shot-token=test',
  )
  // 按列表筛选
  await page.getByText('选择设备类型全部--查询重置').hover
  await page.locator('.group-wrapper-menu > .d-button').hover()
  await page.getByText('编辑').click()
  await page.getByRole('button', { name: '筛选', exact: true }).click()
  await page
    .locator('div')
    .filter({ hasText: /^按列表筛选$/ })
    .locator('span')
    .nth(1)
    .click()
  await page
    .locator('.select > .d-select-wrapper > .d-select > .d-grid > .d-select-content')
    .click()
  await page.getByText('Windows').nth(1).click()
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  await page.waitForTimeout(500)
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.waitForTimeout(500)
  const responsePromiseDimensions = page.waitForResponse(
    response => response.url().includes('/api/dataset/field/dimensions/data'),
    // && response.url().includes('subScene=edit&chartId=chart_pTKI16JJfW'),
  )
  // await page.getByText('全部').click()
  await page.locator('.scrollable-content .d-select-wrapper.d-inline-block.filter-select').first().click()
  const dimensionsResponse = await responsePromiseDimensions
  const dimensionsJson = await dimensionsResponse.json()
  const positions = dimensionsJson.data.positions
  await expect(JSON.stringify(positions)).toBe(JSON.stringify(['Windows']))
  await page.waitForTimeout(1000)
  // return
  // 按列表筛选-排除
  await page.getByText('选择设备类型全部--查询重置').hover
  await page.locator('.group-wrapper-menu > .d-button').hover()
  await page.getByText('编辑').click()
  await page.getByRole('button', { name: '筛选', exact: true }).click()
  await page
    .locator('.select > .d-select-wrapper > .d-select > .d-grid > .d-select-content')
    .click()
  await page
    .locator('.select > .d-select-wrapper > .d-select > .d-grid > .d-select-content')
    .click()
  await page
    .locator('.bottom > div:nth-child(3) > .d-checkbox-simulator > .d-checkbox-indicator')
    .click()
  // await page.waitForTimeout(1000)
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  await page.waitForTimeout(500)
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.waitForTimeout(500)
  const responsePromiseDimensions2 = page.waitForResponse(
    response => response.url().includes('/api/dataset/field/dimensions/data'),
    // && response.url().includes('subScene=edit&chartId=chart_pTKI16JJfW'),
  )
  // await page.getByText('全部').click()
  await page.locator('.scrollable-content .d-select-wrapper.d-inline-block.filter-select').first().click()
  const dimensionsResponse2 = await responsePromiseDimensions2
  const dimensionsJson2 = await dimensionsResponse2.json()
  const positions2 = dimensionsJson2.data.positions
  await expect(JSON.stringify(positions2).includes('Windows')).toBe(false)
  await page.waitForTimeout(500)
  // 按条件筛选-不等于精确匹配
  await page.getByText('选择设备类型全部--查询重置').hover
  await page.locator('.group-wrapper-menu > .d-button').hover()
  await page.getByText('编辑').click()
  await page.getByRole('button', { name: '筛选', exact: true }).click()
  await page
    .locator('div')
    .filter({ hasText: /^按条件筛选$/ })
    .locator('span')
    .nth(1)
    .click()
  await page.waitForTimeout(500)
  await page.getByText('包含(模糊匹配)').first().click()
  await page.getByText('不等于(精确匹配)').click()
  await page.getByPlaceholder('请输入').click()
  await page.getByPlaceholder('请输入').fill('others')
  await page.getByRole('button', { name: '确定' }).nth(2).click()
  // await page.waitForTimeout(2000)
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.waitForTimeout(500)
  const responsePromiseDimensions3 = page.waitForResponse(
    response => response.url().includes('/api/dataset/field/dimensions/data'),
    // && response.url().includes('subScene=edit&chartId=chart_pTKI16JJfW'),
  )
  // await page.getByText('全部').click()
  await page.locator('.scrollable-content .d-select-wrapper.d-inline-block.filter-select').first().click()
  const dimensionsResponse3 = await responsePromiseDimensions3
  const dimensionsJson3 = await dimensionsResponse3.json()
  const positions3 = dimensionsJson3.data.positions
  await expect(JSON.stringify(positions3).includes('others')).toBe(false)
  await page.waitForTimeout(500)
})

/**
 * <AUTHOR>
 * @description 看板引用数据集存在日期字段
 * @step 操作步骤
 * 1.选择最新数据日期
2.动态值/固定值
2. 按条件选择，不为空、不等于等条件设置
 * @expect 期望结果
 * 1.正确读取数据集最新数据日期
2.筛选结果符合预期
 */
test('filterBox-selectNewDate', async ({ page }) => {
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_DFKHXbyo8Z'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=18703&pageId=page_Vkc2Gq02E2&projectId=3&redbi-screen-shot-token=test',
  )
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  await page.waitForTimeout(1000)
  const dataList = privotJson.data.dataList
  await expect(JSON.stringify(dataList).includes(getYesterdayDate())).toBe(true)
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(7) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.locator('div').filter({ hasText: /^固定日期$/ }).locator('span').nth(1)
    .click()
  await page.getByPlaceholder('请选择日期').click()
  await page.getByPlaceholder('请选择日期').fill(getYesterdayDate())
  const responsePromisePrivot2 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_DFKHXbyo8Z'),
  )
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  const privotResponse2 = await responsePromisePrivot2
  const privotJson2 = await privotResponse2.json()
  await page.waitForTimeout(1000)
  const dataList2 = privotJson2.data.dataList
  await expect(JSON.stringify(dataList2).includes(getYesterdayDate())).toBe(true)
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 高级计算
 * @step 操作步骤
 * 1. 按行、按列设置占比
2. 按自定义维度占比
 * @expect 期望结果
 * 正确展示占比，重点关注透视表、折线图
 */
test('advanced-computing1', async ({ page }) => {
  const responsePromisePrivotPageLoad = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_oULpOTJYYR__32384'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25556&pageId=page_HJ1GIA8Vj__32384&projectId=3&redbi-screen-shot-token=test',
  )
  await responsePromisePrivotPageLoad
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.getByText('高级计算', { exact: true }).click()
  await page.getByText('占比', { exact: true }).click()
  await page.getByText('按行', { exact: true }).click()
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_oULpOTJYYR__32384'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList
  await expect(dataList[0]['M55H7-SUM']).toBeLessThanOrEqual(1)
  await page.waitForTimeout(1000)
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.getByText('高级计算', { exact: true }).click()
  await page.getByText('占比', { exact: true }).click()
  await page.getByText('按列', { exact: true }).click()
  const responsePromisePrivot2 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_oULpOTJYYR__32384'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse2 = await responsePromisePrivot2
  const privotJson2 = await privotResponse2.json()
  const dataList2 = privotJson2.data.dataList
  await expect(dataList2[0]['M55H7-SUM']).toBeLessThanOrEqual(1)
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 高级计算
 * @step 操作步骤
 * 展示时间区间内最新有数据日期的数据
 * @expect 期望结果
 * 正确展示期末值，重点关注趋势分析/维度趋势表、透视表
 */
test('advanced-computing2', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=18703&pageId=page_9AFAODfhXF&projectId=3&redbi-screen-shot-token=test',
  )
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.getByText('高级计算', { exact: true }).click()
  await page.getByText('期末值(最后一天)', { exact: true }).click()
  await page.locator('span').filter({ hasText: /^日期$/ }).click()
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_OROq4zYv4l'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  await page.waitForTimeout(1000)
  const dataList = privotJson.data.dataList
  await expect(JSON.stringify(dataList).includes(getYesterdayDate())).toBe(true)
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 高级计算
 * @step 操作步骤
 * 展示时间区间内最新有数据日期的数据
 * @expect 期望结果
 * 正确展示期末值，重点关注趋势分析/维度趋势表、透视表
 */
test('advanced-computing3', async ({ page }) => {
  const responsePromisePrivotPageLoad = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_nImJsuoEO6'),
  )

  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=18703&pageId=page_ucuBbf7jLp&projectId=3&redbi-screen-shot-token=test',
  )
  await responsePromisePrivotPageLoad
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.getByText('高级计算', { exact: true }).click()
  await page.getByText('Top N', { exact: true }).click()
  await page.getByPlaceholder('请输入top项数').click()
  await page.getByPlaceholder('请输入top项数').fill('2')
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  // return
  // await page.locator('span').filter({ hasText: /^日期$/ }).click();
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_nImJsuoEO6'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList
  await expect(dataList.length).toBe(4)
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 高级计算 10.14-10.20
 * @step 1. 配置同比、环比
2.自定义同环比
 * @expect 期望结果
 * 正确展示同环比数据
 */
test('advanced-computing4', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=18703&pageId=page_5Rhe1bN2Ps&projectId=3&redbi-screen-shot-token=test',
  )
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.getByText('高级计算', { exact: true }).click()
  await page.getByText('同环比', { exact: true }).click()
  await page.getByText('周同比', { exact: true }).click()
  await page.getByText('变化值').click()
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_huG0QIcqGd'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  await page.waitForTimeout(1000)
  const dataList = privotJson.data.dataList
  await expect(JSON.stringify(dataList)).toBe(JSON.stringify(advancedComputing4DataList))
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 高级计算 10.1-10.6
 * @step 1. 配置同比、环比
2.自定义同环比
 * @expect 期望结果
 * 正确展示同环比数据
 */
test('aggregation-method', async ({ page }) => {
  // 求和
  const responsePromisePrivot1 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_L6epe1ndtV_32388'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25560&pageId=page_wPruvScX3D_32388&projectId=3&redbi-screen-shot-token=test',
  )
  const privotResponse1 = await responsePromisePrivot1
  const privotJson1 = await privotResponse1.json()
  await page.waitForTimeout(1000)
  const dataList1 = privotJson1.data.dataList
  // 聚合方式数据对比
  await expect(JSON.stringify(dataList1)).toEqual(JSON.stringify(aggregationMethodDataList1))
  await page.waitForTimeout(1000)
  // 平均值
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.locator('span').filter({ hasText: '聚合方式' }).click()
  await page.getByText('平均值').click()
  const responsePromisePrivot2 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_L6epe1ndtV_32388'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse2 = await responsePromisePrivot2
  const privotJson2 = await privotResponse2.json()
  await page.waitForTimeout(1000)
  const dataList2 = privotJson2.data.dataList
  await expect(dataList2.length).toBe(aggregationMethodDataList2.length)
  await page.waitForTimeout(1000)
  // 计数
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.locator('span').filter({ hasText: '聚合方式' }).click()
  await page.getByText('计数', { exact: true }).click()
  const responsePromisePrivot3 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_L6epe1ndtV_32388'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse3 = await responsePromisePrivot3
  const privotJson3 = await privotResponse3.json()
  await page.waitForTimeout(1000)
  const dataList3 = privotJson3.data.dataList
  await expect(JSON.stringify(dataList3.length)).toBe(JSON.stringify(aggregationMethodDataList3.length))
  await page.waitForTimeout(1000)
  // 去重计数
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.locator('span').filter({ hasText: '聚合方式' }).click()
  await page.getByText('去重计数', { exact: true }).click()
  const responsePromisePrivot4 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_L6epe1ndtV_32388'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse4 = await responsePromisePrivot4
  const privotJson4 = await privotResponse4.json()
  await page.waitForTimeout(1000)
  const dataList4 = privotJson4.data.dataList
  await expect(JSON.stringify(dataList4.length)).toBe(JSON.stringify(aggregationMethodDataList4.length))
  await page.waitForTimeout(1000)
  // 最大值
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.locator('span').filter({ hasText: '聚合方式' }).click()
  await page.getByText('最大值', { exact: true }).click()
  const responsePromisePrivot5 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_L6epe1ndtV_32388'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse5 = await responsePromisePrivot5
  const privotJson5 = await privotResponse5.json()
  await page.waitForTimeout(1000)
  const dataList5 = privotJson5.data.dataList
  await expect(JSON.stringify(dataList5.length)).toBe(JSON.stringify(aggregationMethodDataList5.length))
  await page.waitForTimeout(1000)
  // 最小值
  await page.locator('div:nth-child(4) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.locator('span').filter({ hasText: '聚合方式' }).click()
  await page.getByText('最小值', { exact: true }).click()
  const responsePromisePrivot6 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
    && response.url().includes('subScene=edit&chartId=chart_L6epe1ndtV_32388'),
  )
  await page.getByRole('button', { name: '查询数据' }).click()
  const privotResponse6 = await responsePromisePrivot6
  const privotJson6 = await privotResponse6.json()
  await page.waitForTimeout(1000)
  const dataList6 = privotJson6.data.dataList
  await expect(JSON.stringify(dataList6.length)).toBe(JSON.stringify(aggregationMethodDataList6.length))
  await page.waitForTimeout(1000)
})

/**
 * <AUTHOR>
 * @description 排序 测试拆分维度后排序正常
 * @step 维度拆分后，维度和指标字段进行排序（升序，降序，自定义排序）
 * @expect 期望结果
 * 排序正确
 */
test('line-chart-sorting', async ({ page }) => {
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
    && response.url().includes('subScene=edit&chartId=chart_dsovythhSn_32389'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25561&pageId=page_qwHgKHYVDF_32389&projectId=3&redbi-screen-shot-token=test',
  )

  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  await page.waitForTimeout(1000)
  const dataList = privotJson.data.dataList
  await expect(isDateInAscendingOrder(dataList)).toBe(true)
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  await page.locator('div:nth-child(2) > .field > .gui-setter > .gui-field-drop-zone > .field-drop-zone > .gui-field-list > .draggable-wrapper > .field-pill > .slot-wrapper > .placement').click()
  await page.locator('span').filter({ hasText: '排序' }).click()
  const responsePromisePrivot2 = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
    && response.url().includes('subScene=edit&chartId=chart_dsovythhSn_32389'),
  )
  await page.getByText('升序').click()
  const privotResponse2 = await responsePromisePrivot2
  const privotJson2 = await privotResponse2.json()
  const dataList2 = privotJson2.data.dataList
  await expect(isNumberInAscendingOrder(dataList2.map(item => item[2]))).toBe(true)
  await page.waitForTimeout(1000)
})
