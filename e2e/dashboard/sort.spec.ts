import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'
import { isDateInAscendingOrder, isNumberInAscendingOrder, isTextInAscendingOrder } from '../utils/index'
import {
  trendAnalysisTableSamecomparedDataList, dimensionTrendTableSamecomparedDataList, dimensionTrendTableSumDataList, pivotSamecomparedsDataList, indicatorCardDataListIndex, indicatorCardDataListDimension,
} from './chatData'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 普通图表，维度中包含“日期”维度时，默认升序、折线图、透视表
 * @step 操作步骤
 * 1.折线图中包含“日期”维度，折线图X轴默认按“日期”升序
 * @expect 期望结果
 * 图表中日期默认升序（不显示在字段排序规则中）
 */
test('line-dtm-sort', async ({ page }) => {
  const responsePromise = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('chartId=chart_ZHeLbeADQb'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=25512&pageId=page_8kiogF9sf7&projectId=3&redbi-screen-shot-token=test',
  )
  // 折线图的请求数据
  const lineChartResponse = await responsePromise
  const lineJson = await lineChartResponse.json()
  await expect(lineJson.data.dataList.length).toBeGreaterThan(0)
  await expect(isDateInAscendingOrder(lineJson.data.dataList)).toBe(true)
})

/**
 * <AUTHOR>
 * @description 普通图表，维度中包含“日期”维度时，默认升序、折线图、透视表
 * @step 操作步骤
 * 2.透视表中包含“日期”维度，表格默认按“日期”升序
 * @expect 期望结果
 * 图表中日期默认升序（不显示在字段排序规则中）
 */
test('pivot-dtm-sort', async ({ page }) => {
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('chartId=chart_oULpOTJYYR'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/list?dashboardId=25515&pageId=page_HJ1GIA8Vj_32339&projectId=3&redbi-screen-shot-token=test',
  )
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dimensions = privotJson.data.meta.dimensions
  // 日期排序字段
  for (let index = 0; index < dimensions.length; index++) {
    const item = dimensions[index]
    if (item.type === 'Date') {
      // eslint-disable-next-line
      await expect(item.order.length).toBeGreaterThan(0)
      // eslint-disable-next-line
      await expect(isDateInAscendingOrder(item.order)).toBe(true)
    }
  }
})
/**
 * <AUTHOR>
 * @description 透视表，行维度数>=1，列维度数>=1
 * @step 操作步骤
 * 1.透视表行维度自定义排序+列维度自定排序
2.透视表行维度自定义排序+列维度升/降序
3.透视表行维度升/降序+列维度自定义排序
 * @expect 期望结果
 * 1-3排序均生效
 */
test.describe('pivot-dimension-greaterThanOrEqual-1', () => {
  test('row-customize-col-customize', async ({ page }) => {
    // 自定义排序行维度，列维度都选的是列表里的第二项
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25517&pageId=page_Uy47QzJPqn_32341&projectId=3&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(2000)
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await page.locator('.actions-right > .d-icon > svg').first().click()
    await page.getByRole('button', { name: '不排序' }).first().click()
    await page.getByText('自定义').nth(0).click()
    await page.waitForTimeout(1000)
    const firstProjectId = await page.locator('div:nth-child(1)>.drag-item>div').innerHTML()
    // await page.locator('div:nth-child(2) > .drag-item .d-button').click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await page.getByRole('button', { name: '不排序' }).first().click()
    await page.getByText('自定义').nth(2).click()
    await page.waitForTimeout(2000)
    const firstDeviceType = await page.locator('div:nth-child(1)>.drag-item>div').innerHTML()
    // await page.locator('div:nth-child(2) > .drag-item > .d-button').click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    // 透视表的请求数据
    const responsePromisePrivot = page.waitForResponse(
      response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
        && response.url().includes('chartId=chart_TPTShrPZqZ'),
    )
    await page.getByRole('button', { name: '确认' }).click()
    const privotResponse = await responsePromisePrivot
    const privotJson = await privotResponse.json()
    const dimensions = privotJson.data.meta.dimensions
    for (let index = 0; index < dimensions.length; index++) {
      const item = dimensions[index]
      if (item.type === 'Whole') {
        // type为Whole的order中的第一项为上面设置的firstProjectId
        // eslint-disable-next-line
        await expect(item.order.length).toBeGreaterThan(0)
        // eslint-disable-next-line
        await expect(item.order[0]).toBe(parseInt(firstProjectId, 10))
      } else {
        // type为String的order中的第一项为上面设置的firstDeviceType
        // eslint-disable-next-line
        await expect(item.order.length).toBeGreaterThan(0)
        // eslint-disable-next-line
        await expect(item.order[0]).toBe(firstDeviceType)
      }
    }
  })
  test('row-customize-col-asc', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=16656&pageId=page_Uy47QzJPqn&projectId=3&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(2000)
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await page.locator('.actions-right > .d-icon > svg').first().click()
    await page.getByRole('button', { name: '不排序' }).first().click()
    await page.getByText('自定义').nth(1).click()
    await page.waitForTimeout(2000)
    const firstProjectId = await page.locator('div:nth-child(1)>.drag-item>div').innerHTML()
    // await page.locator('div:nth-child(2) > .drag-item .d-button').click()

    await page.getByRole('button', { name: '确定' }).nth(1).click()
    await page.getByRole('button', { name: '不排序' }).first().click()
    await page.getByText('升序').nth(1).click()
    // 透视表的请求数据
    const responsePromisePrivot = page.waitForResponse(
      response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
        && response.url().includes('chartId=chart_TPTShrPZqZ'),
    )
    await page.getByRole('button', { name: '确认' }).click()
    const privotResponse = await responsePromisePrivot
    const privotJson = await privotResponse.json()
    const dimensions = privotJson.data.meta.dimensions
    for (let index = 0; index < dimensions.length; index++) {
      const item = dimensions[index]
      if (item.type === 'Whole') {
        // type为Whole的order中的第一项为上面设置的firstProjectId
        // eslint-disable-next-line
        await expect(item.order.length).toBeGreaterThan(0)
        // eslint-disable-next-line
        await expect(item.order[0]).toBe(parseInt(firstProjectId, 10))
      } else {
        // eslint-disable-next-line
        await expect(item.order?.length).toBeGreaterThan(0)
        // eslint-disable-next-line
        await expect(isTextInAscendingOrder(item.order)).toBe(true)
      }
    }
  })
  test('row-asc-col-customize', async ({ page }) => {
    await page.goto(
      'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25517&pageId=page_Uy47QzJPqn_32341&redbi-screen-shot-token=test',
    )
    await page.waitForTimeout(2000)
    await page.locator('.edit-dashboard-chart-wrapper').first().click()
    await page.locator('.actions-right > .d-icon > svg').first().click()
    await page.getByRole('button', { name: '不排序' }).first().click()
    await page.waitForTimeout(2000)
    await page.getByText('升序').first().click()
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '不排序' }).first().click()
    await page.getByText('自定义').nth(1).click()
    await page.waitForTimeout(2000)
    const firstDeviceType = await page.locator('div:nth-child(2)>.drag-item>div').innerHTML()
    await page.locator('div:nth-child(2) > .drag-item > .d-button').click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
    // 透视表的请求数据
    const responsePromisePrivot = page.waitForResponse(
      response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
        && response.url().includes('chartId=chart_TPTShrPZqZ'),
    )
    await page.getByRole('button', { name: '确认' }).click()
    const privotResponse = await responsePromisePrivot
    const privotJson = await privotResponse.json()
    const dimensions = privotJson.data.meta.dimensions
    for (let index = 0; index < dimensions.length; index++) {
      const item = dimensions[index]
      if (item.type === 'Whole') {
        // eslint-disable-next-line
        await expect(item.order.length).toBeGreaterThan(0)
        // eslint-disable-next-line
        await expect(isNumberInAscendingOrder(item.order)).toBe(true)
      } else {
        // type为String的order中的第一项为上面设置的firstDeviceType
        // eslint-disable-next-line
        await expect(item.order.length).toBeGreaterThan(0)
        // eslint-disable-next-line
        await expect(item.order[0]).toBe(firstDeviceType)
      }
    }
  })
})

/**
 * <AUTHOR>
 * @description 透视表，行维度数>=2
 * @step 操作步骤
 * 1.最外层行维度设置自定义排序+指标组内排序
 * @expect 期望结果
 * 最外层维度按用户设置的生效，内层维度按分组指标值结果排序
 */
test('pivot-row-dimension-greaterThanOrEqual-2', async ({ page }) => {
  // 已配好图表，外层维度为项目id自定义排序，第一个为1004，内层按指标组内升序
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
        && response.url().includes('chartId=chart_Mixa5i8xQ_32346'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25521&pageId=page_fBqNGugLqR_32346R&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dimensions = privotJson.data.meta.dimensions
  const dataList = privotJson.data.dataList
  const groupData = dataList.filter((item: any) => item['Dd3kJ-SUM'] === 1004)
  const formattedData = groupData.map(item => item['Mb6il-SUM'])
  await expect(isNumberInAscendingOrder(formattedData)).toBe(true)
  for (let index = 0; index < dimensions.length; index++) {
    const item = dimensions[index]
    if (item.type === 'Whole') {
      // eslint-disable-next-line
      await expect(item.order.length).toBeGreaterThan(0)
      // eslint-disable-next-line
      await expect(item.order[0]).toBe(1004)
    }
  }
})

/**
 * <AUTHOR>
 * @description 透视表，行维度数>=2
 * @step 操作步骤
 * 1.设置透视表高级排序_维度依据指标排序
 * @expect 期望结果
 * 1.高级排序生效，效果等同指标by排序维度汇总后排序，或维度按指标列小计结果排序
 */
test('pivot-row-dimension-greaterThanOrEqual-2-advanced-sorting', async ({ page }) => {
  // 判断最后一页的小计是否为升序
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
        && response.url().includes('chartId=chart_4yi7mVojhC'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25524&pageId=page_RRZ2njDEPk_32349&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const size = privotJson.data.size
  const totalSize = privotJson.data.totalSize
  const totalPage = Math.ceil(totalSize / size)
  const responsePromisePrivottotalPage = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
        && response.url().includes('chartId=chart_4yi7mVojhC'),
  )
  await page.locator('div').filter({ hasText: new RegExp(`^${totalPage}${totalPage}$`) }).first().click()
  const privotResponsetotalPage = await responsePromisePrivottotalPage
  const privotJsontotalPage = await privotResponsetotalPage.json()
  const dataList = privotJsontotalPage.data.dataList
  const groupData = dataList.filter((item: any) => item['Mb6il-SUM-column-Dd3kJ-SUM-summary'])
  const formattedData = groupData.map(item => item['Mb6il-SUM-column-Dd3kJ-SUM-summary'])
  await expect(isNumberInAscendingOrder(formattedData)).toBe(true)
})

/**
 * <AUTHOR>
 * @description 趋势分析表
 * @step 操作步骤
 * 1.添加日期、指标字段（包含原始指标，sum/sum型指标）
2.配置自定义时间周期
3.切换同环比3种类型：统一设置环比/年同比、显示环比/年同比切换、按时间粒度自定义同环比
4.设置部分指标“默认显示在已选字段中“，再新增指标
 * @expect 期望结果
 * 1-3.趋势分析表在日｜周｜月｜季｜年｜自定义周期下数据正确，同环比数据正确
1-3.弹窗趋势图日｜周｜月｜双月｜季数据正确
4.新增指标，不改变“默认显示在已选字段中”的设置
 */
test('trend-analysis-table-samecompared', async ({ page }) => {
  // 趋势分析表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
        && response.url().includes('chartId=chart_cNWDGGghqW'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25527&pageId=page_ETLtREmZaq_32352&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson?.data?.dataList
  await expect(dataList?.length).toBe(trendAnalysisTableSamecomparedDataList.length)
  // 弹窗趋势图的请求数据  选不到
  //   const responsePromisePop = page.waitForResponse(
  //     response => response.url().includes('/api/dashboard/dashboard/data')
  //     && response.url().includes('subScene=edit&chartId=chart_cNWDGGghqWfasfas'),
  //   )
  //   await page.locator('canvas').click({
  //     position: {
  //       x: 76,
  //       y: 51
  //     }
  //   });
  //   const popDataListLocal=[
  // ]
  //   const popResponse = await responsePromisePop
  //   const popJson = await popResponse.json()
  //   const popDataList = popJson.data.dataList
  //   await expect(JSON.stringify(popDataList)).toBe(JSON.stringify(popDataListLocal))
})

/**
 * <AUTHOR>
 * @description 透视表，行维度数>=2
 * @step 操作步骤
 * 0.开启求和/均值
1.添加日期、指标字段（包含原始指标，sum/sum型指标）
2.配置自定义时间周期
3.切换同环比3种类型：统一设置环比/年同比、显示环比/年同比切换、按时间粒度自定义同环比
 * @expect 期望结果
 * 0-3.开启求和/均值后，趋势分析表在日｜周｜月｜季｜年｜自定义周期下数据正确，同环比数据正确
0-3.弹窗趋势图日｜周｜月｜双月｜季数据正确
 */
test('trend-analysis-table-sum', async () => {
  // 趋势分析表的请求数据
  // const responsePromisePrivot = page.waitForResponse(
  //   response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
  //     && response.url().includes('chartId=chart_3nQrt5Lu9O'),
  // )
  // await page.goto(
  //   'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25530&pageId=page_kmXXrNWFXM_32355&projectId=3&redbi-screen-shot-token=test',
  // )
  // await page.waitForTimeout(2000)
  // const privotResponse = await responsePromisePrivot
  // const privotJson = await privotResponse.json()
  // const dataList = privotJson.data.dataList
  // await expect(JSON.stringify(dataList)).toBe(JSON.stringify(trendAnalysisTableSumDataList))
})

/**
 * <AUTHOR>
 * @description 测试看板拖拽
 * @step 操作步骤
 * 0.开启求和/均值
1.添加拖拽
2.看拖拽是否可见
3.删除拖拽
 * @expect 期望结果
 * 拖拽添加成功
 */
test('test-drag-use', async ({ page }) => {
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25551&pageId=page_8kiogF9sf__32379&projectId=3&redbi-screen-shot-token=test',
  )

  const source = await page.locator('div:nth-child(4) > .material-button > .two-tone-icon')
  const target = await page.locator('.safe-wrapper')
  // 获取起始元素的位置
  const sourceBox = await source.boundingBox()
  const targetBox = await target.boundingBox()
  if (sourceBox && targetBox) {
    // 模拟拖动操作
    await page.mouse.move(sourceBox.x + sourceBox.width / 2, sourceBox.y + sourceBox.height / 2) // 移动到起始元素中心
    await page.mouse.down() // 按下鼠标
    await page.mouse.move(targetBox.x + targetBox.width / 2, targetBox.y + targetBox.height / 2) // 移动到目标位置中心
    await page.mouse.up() // 松开鼠标
  } else {
    throw new Error('无法获取元素的位置信息')
  }
  await expect(page.getByText(/请拖拽字段到此处/)).toBeVisible()
  // 定位包含 "请拖拽字段到此处" 的文字区域
  await page.waitForTimeout(2000)
  await page.getByText('请拖拽字段到此处')

  // 定位文字区域旁边的三个点（假设三个点是一个按钮或图标）
  await page.locator('.group-wrapper-menu > .d-button').first().hover()

  // 将鼠标悬停在三个点上
  //  await threeDots.hover();

  await page.getByText('删除').click()
  await expect(page.getByText(/请拖拽字段到此处/)).toBeHidden()
})

// 对比数据

/**
 * <AUTHOR>
 * @description 维度趋势表，维度数>=2
 * @step 操作步骤
 * 1.添加日期、维度、指标字段（包含原始指标，sum/sum型指标）
2.配置自定义时间周期
3.配置维度筛选
4.切换同环比3种类型：统一设置环比/年同比、显示环比/年同比切换、按时间粒度自定义同环比
5.切换维度在外、指标在外样式
6.设置部分指标“默认显示在已选字段中“，再新增指标
 * @expect 期望结果
 * 1-4.维度趋势表在日｜周｜月｜季｜年｜自定义周期下数据正确，同环比数据正确
1-4.切换维度筛选，指标和同环比数据正确
1-4.弹窗趋势图日｜周｜月｜双月｜季数据正确
5.切换维度在外、指标在外，数据均正确
6.新增指标，不改变“默认显示在已选字段中”的设置
 */
test('dimension-trend-table-samecompared', async ({ page }) => {
  // 维度分析表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('chartId=chart_8X84CwLqhZ'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25531&pageId=page_lKMRhUNNlh_32356&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList.slice(0, 10)
  await expect(JSON.stringify(dataList)).toBe(JSON.stringify(dimensionTrendTableSamecomparedDataList))
})

/**
 * <AUTHOR>
 * @description 维度趋势表，维度数>=2
 * @step 操作步骤
 * 0.开启求和/均值
1.添加日期、维度、指标字段（包含原始指标，sum/sum型指标）
2.配置自定义时间周期
3.切换同环比3种类型：统一设置环比/年同比、显示环比/年同比切换、按时间粒度自定义同环比
4.切换维度在外、指标在外样式
5.设置部分指标“默认显示在已选字段中“，再新增指标
 * @expect 期望结果
 * 0-4.开启求和/均值后，维度趋势表在日｜周｜月｜季｜年｜自定义周期下数据正确，同环比数据正确
0-4.切换维度筛选，指标和同环比数据正确
0-4.弹窗趋势图日｜周｜月｜双月｜季数据正确
0-5.切换维度在外、指标在外，数据均正确
6.新增指标，不改变“默认显示在已选字段中”的设置
 */
test('dimension-trend-table-sum', async ({ page }) => {
  // 维度分析表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('chartId=chart_j1R7h7Y4tt'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25535&pageId=page_DAckusNM7z_32360&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList.slice(0, 10)
  await expect(JSON.stringify(dataList)).toBe(JSON.stringify(dimensionTrendTableSumDataList))
})

/**
 * <AUTHOR>
 * @description 维度趋势表，cube表，维度数>=2
 * @step 操作步骤
 * 0.开启求和/均值
1.添加日期、维度、指标字段（包含原始指标，sum/sum型指标）
2.配置自定义时间周期
3.切换同环比3种类型：统一设置环比/年同比、显示环比/年同比切换、按时间粒度自定义同环比
4.切换维度在外、指标在外样式
5.关闭维度汇总功能
6.开启维度汇总-配置自定义维度汇总
 * @expect 0-6.维度汇总行不显示
0-5，7，维度趋势表在日｜周｜月｜季｜年｜自定义周期下，在求和/均值开启后，在维度在外｜指标在外样式下，数据均能正确显示
 */
// test('dimension-trend-table-cube', async ({ }) => {
//   // 维度分析表的请求数据
//   // const responsePromisePrivot = page.waitForResponse(
//   //   response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
//   //     && response.url().includes('chartId=chart_ApQMsDJDUm'),
//   // )
//   // await page.goto(
//   //   'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25539&pageId=page_K7FxuwMi4U_32365&projectId=3&redbi-screen-shot-token=test',
//   // )
//   // await page.waitForTimeout(2000)
//   // const privotResponse = await responsePromisePrivot
//   // const privotJson = await privotResponse.json()
//   // const dataList = privotJson.data.dataList.slice(0, 10)
//   // await expect(JSON.stringify(dataList)).toBe(JSON.stringify(dimensionTrendTableCubeDataList))
// })

/**
 * <AUTHOR>
 * @description 透视表，行维度数>=2, 列维度>=1,
 * @step 操作步骤
 * 1. 指标包含（原始指标、sum/sum指标、高级计算同环比指标、行列占比指标）
2.设置指标分组
3.设置部分字段默认显示在已选字段中
4.配置维度自定义排序+指标组内排序
3.配置行列总计
4.配置批量同环比
5.切换紧凑模式
 * @expect 期望结果
 * 1-2 透视表数据正确显示、排序生效
1，3.行列汇总数据、行列占比的汇总数据正确显示
4. 批量同环比数据正确显示
5.紧凑模式下，数据正确显示
 */
test('pivot-row-dimension-greaterThanOrEqual-2-col-greaterThanOrEqual-1', async ({ page }) => {
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('chartId=chart_Rjdm2CvVrb'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25542&pageId=page_Tv7mXsK8Fo_32368&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dimensions = privotJson.data.meta.dimensions
  const dataList = privotJson.data.dataList
  const groupData = dataList.filter((item: any) => item['Dd3kJ-SUM'] === 1004)
  const formattedData = groupData.map(item => item['Mb6il-SUM'])
  await expect(isNumberInAscendingOrder(formattedData)).toBe(true)
  await expect(JSON.stringify(groupData)).toBe(JSON.stringify(pivotSamecomparedsDataList))
  for (let index = 0; index < dimensions.length; index++) {
    const item = dimensions[index]
    if (item.type === 'Whole') {
      // eslint-disable-next-line
      await expect(item.order.length).toBeGreaterThan(0)
      // eslint-disable-next-line
      await expect(item.order[0]).toBe(1004)
    }
  }
})

/**
 * <AUTHOR>
 * @description 透视表,
 * @step 操作步骤
 * 1.透视表添加维度指标
2.设置指标全局排序
3.维度字段设置为链接
 * @expect 期望结果
 * 1-2 排序数据正确
3. 链接可正常点击跳转
 */
test('pivot-dimension-isLink', async ({ page }) => {
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/pivot-convert-query')
      && response.url().includes('chartId=chart_s4hObkzGMj'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=16656&pageId=page_9qQ3iytMP8&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()
  const dataList = privotJson.data.dataList
  const formattedData = dataList.map(item => item['Mb6il-SUM']).reverse()
  await expect(isNumberInAscendingOrder(formattedData)).toBe(true)
  // await expect(JSON.stringify(dataList)).toBe(JSON.stringify(pivotSamecomparedsDataList))
  // for (let index = 0; index < dimensions.length; index++) {
  //   const item = dimensions[index]
  //   if (item.type === 'Whole') {
  //     await expect(item.order.length).toBeGreaterThan(0)
  //     await expect(item.order[0]).toBe(1004)
  //   }
  // }
})

/**
 * <AUTHOR>
 * @description 折线图,
 * @step 操作步骤
 * 1.折线图配置维度、Y轴指标、Y次轴指标、颜色图例、提示指标、拆分维度
2.开启节假日
3.配置辅助线
 * @expect 期望结果
 * 1.折线图正确绘制，默认按日期升序、tooltip正常、拆分正常
2.节假日正常显示
3.辅助线正常显示
 */
test('line-chart-holiday', async ({ page }) => {
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_VM24alwrVC'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=16656&pageId=page_3WjjOw3mZA&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()

  const dimensions = privotJson.data.meta.dimensions
  // 日期排序字段
  for (let index = 0; index < dimensions.length; index++) {
    const item = dimensions[index]
    if (item.type === 'Date') {
      if (item.order) {
      // eslint-disable-next-line
      await expect(item.order.length).toBeGreaterThan(0)
        // eslint-disable-next-line
      await expect(isDateInAscendingOrder(item.order)).toBe(true)
      }
    }
  }
})

/**
 * <AUTHOR>
 * @description 指标卡,
 * @step 操作步骤
 * 1.指标卡配置指标
2.分别设置并列主指标、主次指标样式
3.配置高级计算同环比
4.配置批量同环比
 * @expect 期望结果
 * 1-4.指标卡样式正常、数据正确
 */
test('indicator-card-index', async ({ page }) => {
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_WVJdxED6LW'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=25544&pageId=page_E2izDkV44w_32370&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()

  const dataList = privotJson.data.dataList
  await expect(JSON.stringify(dataList)).toBe(JSON.stringify(indicatorCardDataListIndex))
})

/**
 * <AUTHOR>
 * @description 指标卡,
 * @step 操作步骤
 * 1.指标卡配置维度、指标
2.配置高级计算同环比
3.配置批量同环比
 * @expect 期望结果
 * 1-3.指标卡样式正常、数据正确
 */
test('indicator-card-dimension', async ({ page }) => {
  // 透视表的请求数据
  const responsePromisePrivot = page.waitForResponse(
    response => response.url().includes('/api/dashboard/dashboard/data')
      && response.url().includes('subScene=edit&chartId=chart_7yGkO0md9q'),
  )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=16656&pageId=page_K44Q0NNpNk&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(2000)
  const privotResponse = await responsePromisePrivot
  const privotJson = await privotResponse.json()

  const dataList = privotJson.data.dataList
  await expect(JSON.stringify(dataList)).toBe(JSON.stringify(indicatorCardDataListDimension))
})

/**
 * <AUTHOR>
 * @description 看板全局替换数据集,
 * @step 操作步骤
 * 1.全局替换（看板右上角-预览左侧）
2.单个图表替换（切换数据集时）注意涉及到大量字段（90+）的替换、注意计算字段、分组字段的展示
 * @expect 期望结果
 * 1.对该看板内所用同一数据集的图表全局替换
2. 对所选图表数据集进行切换
3.正确匹配数据集字段，不同类型、不同属性的无法匹配，展示相应提示信息
4.正确自动创建相应的计算字段
5. 看板展示结果正确，没有数据缺失、组件异常，关注高级计算功能
 */
test('dashboard-allreplace-dataset', async ({ page }) => {
  // // 透视表的请求数据
  // const responsePromisePrivot = page.waitForResponse(
  //   response => response.url().includes('/api/dashboard/dashboard/data')
  //     && response.url().includes('subScene=edit&chartId=chart_7yGkO0md9q'),
  // )
  // const responsePromisePrivot = page.waitForResponse(
  //   response => response.url().includes('/api/dashboard/data')
  //     && response.url().includes('subScene=edit&chartId=chart_ZHeLbeADQb'),
  // )
  await page.goto(
    'https://redbi.devops.beta.xiaohongshu.com/dashboard/detail?dashboardId=18703&pageId=page_ETLtREmZaq_24031&projectId=3&redbi-screen-shot-token=test',
  )
  await page.waitForTimeout(1000)
  await page.locator('.edit-dashboard-chart-wrapper').first().click()
  if (await page.getByText('我知道了').count() > 0) {
    await page.getByText('我知道了').click()
  }
  await page.getByRole('button', { name: '替换数据集' }).click()
  await page.locator('span').filter({ hasText: 'ck自动化回归—勿动' }).click()
  await page.getByPlaceholder('请选择数据集').click()
  await page.waitForTimeout(1000)
  await page.getByPlaceholder('请选择数据集').fill('hive自动化回归测试—勿动')
  await page.getByText('Hive自动化回归测试—勿动', { exact: true }).click()
  await page.waitForTimeout(1000)
  await expect(page.getByText('无可匹配字段').first()).toBeVisible()
  await page.locator('div').filter({ hasText: /^exp_id\(exp_id\) 无可匹配字段$/ }).getByRole('textbox').click()
  await page.getByText('analysis_id', { exact: true }).click()
  // await page.locator('div').filter({ hasText: /^holdout_scene\(holdout_scene\) 无可匹配字段$/ }).getByRole('textbox').click()
  // await page.getByText('analysis_type', { exact: true }).click()
  // const responsePromisePrivot = page.waitForResponse(
  //   response => response.url().includes('/api/dashboard/dashboard/data')
  //     && response.url().includes('subScene=edit&chartId=chart_VM24alwrVC'),
  // )
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await page.waitForTimeout(1000)
  await expect(page.getByText('hive自动化回归测试—勿动').first()).toBeVisible()
  // 前端操作更改
  // const privotResponse = await responsePromisePrivot || {}
  // const privotJson = await privotResponse.json()
  // const dataList = privotJson?.data?.dataList
  // await expect(dataList.length).toBeGreaterThanOrEqual(0)
})
