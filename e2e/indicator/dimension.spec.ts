import { test, expect } from '@playwright/test'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 新建/编辑维度
 * @setp 操作步骤
 * 1、点击新建维度2、输入内容点确认3、编辑新建的维度4、删除编辑后的维度
 * @expect 期望结果
 * 1、前两步之后页面中出现新建的维度 2、编辑新建的维度，确认后维度内容改变3、删除维度之后，新建的维度从页面中消失
 */
test('dimension-create-config', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/dimension/list?redbi-screen-shot-token=test')
  await page.getByRole('button', { name: '新建维度' }).click()
  await page.locator('div').filter({ hasText: /^维度名称0\/20$/ }).getByPlaceholder('请输入').click()
  await page.locator('div').filter({ hasText: /^维度名称0\/20$/ }).getByPlaceholder('请输入').fill('新建维度测试用例1')
  await page.getByText('业务域请选择').click()
  await page.locator('div').filter({ hasText: /^社区$/ }).nth(3).click()
  await page.getByText('实体请选择').click()
  await page.locator('div').filter({ hasText: /^用户$/ }).nth(3).click()
  await page.getByText('关联来源数据集').click()
  await page.locator('div').filter({ hasText: /^项目$/ }).nth(2).click()
  await page.getByText('TEST项目组').click()
  await page.getByRole('textbox', { name: '请选择数据集' }).click()
  await page.getByText('我私有的').click()
  await page.getByTitle('梅新成测试2').click()
  await page.getByText('字段请选择').click()
  await page.getByText('维度2').click()
  await page.locator('div').filter({ hasText: /^口径负责人梅新成$/ }).getByRole('textbox').click()
  await page.getByRole('textbox', { name: '梅新成' }).fill('本间')
  await page.locator('span').filter({ hasText: '本间(刘华)' }).click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('维度新建成功').first()).toBeVisible()

  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).click()
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).fill('新建维度测试用例1')
  await page.waitForTimeout(2000)
  await page.locator('span', { hasText: '编辑' }).first().click()
  await page.locator('div').filter({ hasText: /^维度名称9\/20$/ }).getByPlaceholder('请输入').click()
  await page.locator('div').filter({ hasText: /^维度名称9\/20$/ }).getByPlaceholder('请输入').fill('新建维度测试用例2')
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('维度编辑成功').first()).toBeVisible()

  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).click()
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).fill('新建维度测试用例2')
  await page.waitForTimeout(2000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('删除').first().click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('删除成功').first()).toBeVisible()
  await page.waitForTimeout(2000)
})

/**
 * <AUTHOR>
 * @description 批量新建维度
 * @setp 操作步骤
 * 1、点击批量新建维度 2、上传文档，点击确认 3、批量删除批量编辑后的维度
 * @expect 期望结果
 * 1、前两步之后页面中出现批量创建的维度 2、多选批量创建的维度进行批量删除之后，批量新建的维度不存在于页面中
 */
test('dimension-batch-create', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/dimension/list?redbi-screen-shot-token=test')
  await page.getByRole('button').filter({ hasText: /^$/ }).click()
  await page.getByText('批量创建').click()
  await page.locator('form').getByText('业务域').click()
  await page.locator('div').filter({ hasText: /^社区$/ }).nth(3).click()
  await page.getByText('实体请选择').click()
  await page.locator('div').filter({ hasText: /^用户$/ }).nth(3).click()
  await page.locator('div').filter({ hasText: /^口径负责人梅新成$/ }).getByRole('textbox').click()
  await page.getByRole('textbox', { name: '梅新成' }).fill('benjian')
  await page.locator('span').filter({ hasText: '本间(刘华)' }).click()
  await page.getByText('关联来源数据集').click()
  await page.locator('div').filter({ hasText: /^项目$/ }).nth(2).click()
  await page.getByText('TEST项目组').click()
  await page.getByRole('textbox', { name: '请选择数据集' }).click()
  await page.getByRole('textbox', { name: '请选择数据集' }).fill('墨韩测试2')
  await page.getByTitle('墨韩测试2', { exact: true }).locator('span').click()
  await page.getByRole('button', { name: '添加字段' }).click()
  await page.locator('div').filter({ hasText: /^goods_industry$/ }).locator('span').nth(1)
    .click()
  await page.getByRole('button', { name: '添加', exact: true }).click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('批量创建成功').first()).toBeVisible()

  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).click()
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).fill('goods_industry')
  await page.waitForTimeout(2000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('删除').first().click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('删除成功').first()).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 查询维度
 * @setp 操作步骤
 *1、按查询规则查询相应的维度2、按应用产品查询相应的维度
 * @expect 期望结果
 * 1、对应的维度出现在页面上
 */

test('dimension-search-config', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/dimension/list?redbi-screen-shot-token=test')
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).click()
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).fill('我是测试用例专用维度')
  // 验证查询结果中包含 "我是测试用例专用"
  await expect(page.locator('text=我是测试用例专用维度')).toBeVisible()
})
// /**
//  * <AUTHOR>
//  * @description 维度合并
//  * @setp 操作步骤
//  * 1、指标列表中点击维度合并2、选择合并至的维度
//  * @expect 期望结果
//  * 1、提示成功、2原维度被删除
//  */

test('dimension-assign-config', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/dimension/list?redbi-screen-shot-token=test')
  await page.getByRole('button', { name: '新建维度' }).click()
  await page.locator('div').filter({ hasText: /^维度名称0\/20$/ }).getByPlaceholder('请输入').click()
  await page.locator('div').filter({ hasText: /^维度名称0\/20$/ }).getByPlaceholder('请输入').fill('新建维度合并用例1')
  await page.getByText('业务域请选择').click()
  await page.locator('div').filter({ hasText: /^社区$/ }).nth(3).click()
  await page.getByText('实体请选择').click()
  await page.locator('div').filter({ hasText: /^用户$/ }).nth(3).click()
  await page.getByText('关联来源数据集').click()
  await page.locator('div').filter({ hasText: /^项目$/ }).nth(2).click()
  await page.getByText('TEST项目组').click()
  await page.getByRole('textbox', { name: '请选择数据集' }).click()
  await page.getByText('我私有的').click()
  await page.getByTitle('梅新成测试2').click()
  await page.getByText('字段请选择').click()
  await page.getByText('维度2').click()
  await page.locator('div').filter({ hasText: /^口径负责人梅新成$/ }).getByRole('textbox').click()
  await page.getByRole('textbox', { name: '梅新成' }).fill('本间')
  await page.locator('span').filter({ hasText: '本间(刘华)' }).click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('维度新建成功').first()).toBeVisible()

  await page.getByRole('button', { name: '新建维度' }).click()
  await page.locator('div').filter({ hasText: /^维度名称0\/20$/ }).getByPlaceholder('请输入').click()
  await page.locator('div').filter({ hasText: /^维度名称0\/20$/ }).getByPlaceholder('请输入').fill('新建维度合并用例2')
  await page.getByText('业务域请选择').click()
  await page.locator('div').filter({ hasText: /^社区$/ }).nth(3).click()
  await page.getByText('实体请选择').click()
  await page.locator('div').filter({ hasText: /^用户$/ }).nth(3).click()
  await page.getByText('关联来源数据集').click()
  await page.locator('div').filter({ hasText: /^项目$/ }).nth(2).click()
  await page.getByText('TEST项目组').click()
  await page.getByRole('textbox', { name: '请选择数据集' }).click()
  await page.getByText('我私有的').click()
  await page.getByTitle('梅新成测试2').click()
  await page.getByText('字段请选择').click()
  await page.getByText('维度2').click()
  await page.locator('div').filter({ hasText: /^口径负责人梅新成$/ }).getByRole('textbox').click()
  await page.getByRole('textbox', { name: '梅新成' }).fill('本间')
  await page.locator('span').filter({ hasText: '本间(刘华)' }).click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('维度新建成功').first()).toBeVisible()

  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).click()
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).fill('新建维度合并用例')
  await page.waitForTimeout(2000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').first().click()
  await page.getByText('维度合并').nth(3).click()
  await page.locator('div').filter({ hasText: /^合并至维度$/ }).getByPlaceholder('请选择').click()
  await page.locator('div').filter({ hasText: /^合并至维度$/ }).getByPlaceholder('请选择').fill('新建维度合并用例2')
  await page.locator('span').filter({ hasText: /^新建维度合并用例2$/ }).click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('维度合并成功').first()).toBeVisible()
  await page.waitForTimeout(1000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('删除').first().click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('删除成功').first()).toBeVisible()
})

// /**
//  * <AUTHOR>
//  * @description 指标关联数据集/批量关联数据集
//  * @setp 操作步骤
//  * 1、指标列表中点击关联数据集 2、选择指定数据集批量关联多个指标，选项项目和数据集3、点击前往关联指标 4、进入指标详情删除刚刚关联的数据集
//  * @expect 期望结果
//  * 1、成功跳转到数据集页 2、点击指标进入详情，出现刚刚关联的数据集、3、删除刚刚关联的数据集
//  */

// test('indicator-connect-dataset', async ({ page }) => {
//   await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
//   await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
//   await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('我是测试用例专用')
//   await page.waitForTimeout(2000)
//   await page.getByText('关联数据集', { exact: true }).first().click()
//   await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).click()
//   await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).fill('test')
//   await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).press('CapsLock')
//   await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).fill('TEST')
//   await page.getByText('TEST项目组').click()
//   await page.getByRole('textbox', { name: '请选择数据集' }).click()

//   await page.getByText('我私有的').click()
//   await page.getByText('梅新成测试2').click()
//   const page2Promise = page.waitForEvent('popup')
//   await page.getByRole('button', { name: '前往关联指标' }).click()
//   const page2 = await page2Promise

//   await page2.getByRole('button', { name: '确定' }).nth(1).click()

//   await page2.getByRole('row', { name: '我是测试用例专用指标 ID: 1301345 新增 我是测试用例专用指标 维度 度量 整数 case 4/600 count' }).getByPlaceholder('请输入字段名').click()
//   await page2.locator('span').filter({ hasText: '我是测试用例专用指标' }).nth(1).click()
//   await page2.getByRole('button', { name: '保存' }).click()
//   await page2.waitForTimeout(2000)
//   await page2.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/detail?id=4911&redbi-screen-shot-token=test')
//   await expect(page2.getByText('梅新成测试2').first()).toBeVisible()
//   await page2.getByRole('cell', { name: '查看 分析 新建数据服务' }).getByRole('img').click()
//   await page2.getByText('取消关联').click()
//   await page2.getByRole('button', { name: '确定' }).nth(1).click()
//   await expect(page2.getByText('取消成功').first()).toBeVisible()
//   // 批量关联
//   await page2.getByText('关联数据集', { exact: true }).first().click()
//   await page2.getByText('批量关联多个数据集').click()
//   await page2.locator('form').getByRole('textbox', { name: '请选择' }).click()
//   await page2.locator('form').getByRole('textbox', { name: '请选择' }).press('CapsLock')
//   await page2.locator('form').getByRole('textbox', { name: '请选择' }).fill('TEST')
//   await page2.getByText('TEST项目组').click()
//   await page2.getByRole('button', { name: '添加' }).click()

//   await page2.locator('div:nth-child(6) > div > .d-virtual-tree-node > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
//   await page2.getByRole('button', { name: '添加' }).nth(1).click()
//   await page2.getByRole('button', { name: '确认' }).click()
//   await expect(page2.getByText('梅新成测试2').first()).toBeVisible()
//   await page2.getByRole('cell', { name: '查看 分析 新建数据服务' }).getByRole('img').click()
//   await page2.getByText('取消关联').click()
//   await page2.getByRole('button', { name: '确定' }).nth(1).click()
//   await expect(page2.getByText('取消成功').first()).toBeVisible()
//   // await page2.waitForTimeout(2000)
// })

// /**
//  * <AUTHOR>
//  * @description 维度发布
//  * @setp 操作步骤
//  * 1、指标列表中点击指标发布2、弹窗点击确认3、再点击取消发布
//  * @expect 期望结果
//  * 1、发布成功后，状态改变 2、取消发布后，状态恢复
//  */

test('dimension-publish', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/dimension/list?redbi-screen-shot-token=test')
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).click()
  await page.getByRole('textbox', { name: '搜索维度名称/ID/别名/相关字段名/维度口径' }).fill('我是维度发布用例测试')
  await page.waitForTimeout(2000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('维度发布', { exact: true }).click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('更新成功').first()).toBeVisible()
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('取消发布').click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('更新成功').first()).toBeVisible()
  // await page.waitForTimeout(2000)
})

// /**
//  * <AUTHOR>
//  * @description 关联产品页面
//  * @setp 操作步骤
//  * 1、指标列表中点击关联产品页面2、输入名称与地址
//  * @expect 期望结果
//  * 1、关联后，当前指标展示关联页面的名称
//  */

// test('indicator-connect-product-page', async ({ page }) => {
//   await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
//   await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
//   await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('我是指标发布用例测试')
//   await page.waitForTimeout(2000)
//   await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
//   await page.getByText('关联产品页面', { exact: true }).click()
//   await page.getByRole('button', { name: '确定' }).nth(1).click()
//   await expect(page.getByText('关联成功').first()).toBeVisible()
//   // await page.waitForTimeout(2000)
// })

// /**
//  * <AUTHOR>
//  * @description 设置应用范围
//  * @setp 操作步骤
//  * 1、进入指标详情页从数据集列表中点击设置应用范围2、点击确认
//  * @expect 期望结果
//  * 1、当前数据集应用范围列回显改变
//  */

// test('indicator-set-application', async ({ page }) => {
//   await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/detail?id=5030&redbi-screen-shot-token=test')
//   await page.getByRole('cell', { name: '查看 分析 新建数据服务' }).getByRole('img').click()
//   await page.getByText('设置应用范围', { exact: true }).click()
//   await page.getByRole('button', { name: '确定' }).nth(1).click()
//   await expect(page.getByText('设置成功').first()).toBeVisible()
//   // await page.waitForTimeout(2000)
// })

/**
 * <AUTHOR>
 * @description 新建业务术语
 * @setp 操作步骤
 * 1、点击新建业务术语按钮2、填充内容，点击确定 3、点击编辑，保存4、删除业务术语
 * @expect 期望结果
 * 1、新建成功后提示成功，页面上出现新建的业务术语、2编辑后业务术语内容改变、3删除之后，编辑后的业务术语消失
 */

test('create-business', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/businessknow/list?redbi-screen-shot-token=test')
  await page.getByRole('button', { name: '新建业务术语' }).click()
  await page.getByRole('textbox', { name: '请输入, 名称不可重复' }).click()
  await page.getByRole('textbox', { name: '请输入, 名称不可重复' }).fill('测试用例')
  await page.getByText('请选择').first().click()
  await page.locator('div').filter({ hasText: /^社区$/ }).nth(3).click()
  await page.getByText('请选择').click()
  await page.getByText('字段集合').nth(1).click()
  await page.locator('form').getByRole('textbox').nth(2).click()
  await page.locator('div:nth-child(2) > .d-options-wrapper > div > div:nth-child(2) > .d-option > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
  await page.locator('div:nth-child(2) > .d-options-wrapper > div > div:nth-child(5) > .d-option > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
  await page.getByText('取消 确定').click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('业务术语新建成功').first()).toBeVisible()
  await page.getByRole('textbox', { name: '请输入业务术语或描述搜索' }).click()
  await page.getByRole('textbox', { name: '请输入业务术语或描述搜索' }).fill('测试用例')
  await page.waitForTimeout(2000)
  await page.getByText('编辑').click()
  await page.getByRole('textbox', { name: '请输入, 名称不可重复' }).click()
  await page.getByRole('textbox', { name: '请输入, 名称不可重复' }).fill('测试用例2')
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('业务术语编辑成功').first()).toBeVisible()
  await page.getByText('删除').click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('业务术语删除成功').first()).toBeVisible()
})
