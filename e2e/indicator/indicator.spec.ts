import { test, expect } from '@playwright/test'
import * as path from 'path'
import { interceptor } from '../interceptor'

test.beforeEach(async ({ context }) => {
  await interceptor(context, '<EMAIL>')
})

/**
 * <AUTHOR>
 * @description 指标新建
 * @setp 操作步骤
 * 1、点击新建指标2、输入内容点确认3、编辑新建的指标4、删除编辑后的指标
 * @expect 期望结果
 * 1、前两步之后页面中出现新建的指标 2、编辑新建的指标，确认后指标内容改变3、删除指标之后，新建的指标从页面中消失
 */
test('indicator-create-config', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
  await page.getByRole('button', { name: '新建指标' }).click()
  await page.getByRole('textbox', { name: '请输入指标名称，需保证全局唯一' }).click()
  await page.getByRole('textbox', { name: '请输入指标名称，需保证全局唯一' }).fill('用例1')
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).click()
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).fill('用例1')
  await page.locator('div:nth-child(2) > .d-form-item__content > .d-form-item__wrapper > .d-select-wrapper > .d-select').click()
  await page.locator('form').filter({ hasText: '指标口径负责人梅新成技术口径负责人高级配置' }).getByPlaceholder('请输入并选择').fill('本间')
  await page.locator('span').filter({ hasText: '本间(刘华)' }).click()
  await page.locator('.d-form-item__wrapper > .d-select-wrapper > .d-select > .d-grid').first().click()
  await page.locator('.d-grid-item > .d-grid > div > .d-text').first().click()
  await page.locator('.d-textarea').click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('指标新建成功').first()).toBeVisible()

  await page.locator('span', { hasText: '编辑' }).first().click()
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).click()
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).fill('用例2')
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('指标编辑成功').first()).toBeVisible()

  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('用例1')
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).press('Enter')
  await page.waitForTimeout(2000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('删除').first().click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('删除成功').first()).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 批量新建指标
 * @setp 操作步骤
 * 1、点击批量新建指标 2、上传文档，点击确认 3、批量删除批量编辑后的指标
 * @expect 期望结果
 * 1、前两步之后页面中出现批量创建的指标 2、多选批量创建的指标进行批量删除之后，批量新建的指标不存在于页面中
 */
test('indicator-batch-create', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
  await page.getByRole('button').filter({ hasText: /^$/ }).click()
  await page.getByText('批量创建').click()
  // await page.getByRole('button', { name: '上传文件' }).click();
  // await page.getByRole('button', { name: '上传文件' }).setInputFiles('导入指标模板 (10).xlsx');
  await page
    .locator('.d-upload__input')
    .setInputFiles(path.join(__dirname, './导入指标模板.xlsx'))
  await page.getByRole('button', { name: '下一步' }).click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('创建成功').first()).toBeVisible()

  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('自动化用例批量')
  await page.waitForTimeout(3000)
  await page.locator('thead').getByRole('cell').filter({ hasText: /^$/ }).click()
  await page.locator('#app').getByText('删除').click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('删除成功').first()).toBeVisible()
  await page.waitForTimeout(2000)
})

/**
 * <AUTHOR>
 * @description 查询指标
 * @setp 操作步骤
 * 1、按查询规则查询相应的指标2、按应用产品查询相应的指标
 * @expect 期望结果
 * 1、对应的指标出现在页面上
 */

test('indicator-search-config', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('我是测试用例专用')
  // 验证查询结果中包含 "我是测试用例专用"
  await expect(page.locator('text=我是测试用例专用')).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 指标合并
 * @setp 操作步骤
 * 1、指标列表中点击指标合并2、选择合并至的指标
 * @expect 期望结果
 * 1、提示成功、2原指标被删除
 */

test('indicator-assign-config', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
  await page.getByRole('button', { name: '新建指标' }).click()
  await page.getByRole('textbox', { name: '请输入指标名称，需保证全局唯一' }).click()
  await page.getByRole('textbox', { name: '请输入指标名称，需保证全局唯一' }).fill('测试用例合并1')
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).click()
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).fill('用例1')
  await page.locator('div:nth-child(2) > .d-form-item__content > .d-form-item__wrapper > .d-select-wrapper > .d-select').click()
  await page.locator('form').filter({ hasText: '指标口径负责人梅新成技术口径负责人高级配置' }).getByPlaceholder('请输入并选择').fill('本间')
  await page.locator('span').filter({ hasText: '本间(刘华)' }).click()
  await page.locator('.d-form-item__wrapper > .d-select-wrapper > .d-select > .d-grid').first().click()
  await page.locator('.d-grid-item > .d-grid > div > .d-text').first().click()
  await page.locator('.d-textarea').click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('指标新建成功').first()).toBeVisible()
  await page.getByRole('button', { name: '新建指标' }).click()
  await page.getByRole('textbox', { name: '请输入指标名称，需保证全局唯一' }).click()
  await page.getByRole('textbox', { name: '请输入指标名称，需保证全局唯一' }).fill('测试用例合并2')
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).click()
  await page.getByRole('textbox', { name: '请输入指标口径，包括指标的数据范围、计算方式等。如DAU' }).fill('用例1')
  await page.locator('div:nth-child(2) > .d-form-item__content > .d-form-item__wrapper > .d-select-wrapper > .d-select').click()
  await page.locator('form').filter({ hasText: '指标口径负责人梅新成技术口径负责人高级配置' }).getByPlaceholder('请输入并选择').fill('本间')
  await page.locator('span').filter({ hasText: '本间(刘华)' }).click()
  await page.locator('.d-form-item__wrapper > .d-select-wrapper > .d-select > .d-grid').first().click()
  await page.locator('.d-grid-item > .d-grid > div > .d-text').first().click()
  await page.locator('.d-textarea').click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('指标新建成功').first()).toBeVisible()

  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('测试用例合并')
  await page.waitForTimeout(2000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).first().click()
  await page.locator('div').filter({ hasText: /^关联数据集$/ }).locator('path').nth(1)
    .click()
  await page.getByRole('row', { name: '测试用例合并1 ' }).locator('svg').nth(3).click()
  await page.getByText('指标合并').first().click()
  await page.locator('div').filter({ hasText: /^合并至指标$/ }).getByPlaceholder('请选择').click()
  await page.locator('div').filter({ hasText: /^合并至指标$/ }).getByPlaceholder('请选择').fill('测试用例合并')
  await page.locator('span').filter({ hasText: /^测试用例合并2$/ }).click()
  await page.getByRole('button', { name: '确认' }).click()
  await expect(page.getByText('指标合并成功').first()).toBeVisible()
  await page.waitForTimeout(1000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('删除').first().click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('删除成功').first()).toBeVisible()
})

/**
 * <AUTHOR>
 * @description 指标关联数据集/批量关联数据集
 * @setp 操作步骤
 * 1、指标列表中点击关联数据集 2、选择指定数据集批量关联多个指标，选项项目和数据集3、点击前往关联指标 4、进入指标详情删除刚刚关联的数据集
 * @expect 期望结果
 * 1、成功跳转到数据集页 2、点击指标进入详情，出现刚刚关联的数据集、3、删除刚刚关联的数据集
 */

test('indicator-connect-dataset', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('我是测试用例专用')
  await page.waitForTimeout(2000)
  // await page.locator('div')
  //   .filter({ hasText: /^DataAgent功能更新说明$/ })
  //   .locator('path')
  //   .nth(1)
  //   .click()
  if (await page.getByText('我知道了').count() > 0) {
    await page.getByText('我知道了').click()
  }
  await page.getByText('关联数据集', { exact: true }).first().click()
  await page.getByText('前往指定数据集页批量关联多个指标').click()
  await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).click()
  await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).fill('test')
  await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).press('CapsLock')
  await page.locator('form').getByRole('textbox', { name: '请选择', exact: true }).fill('TEST')
  await page.getByText('TEST项目组').click()
  await page.getByRole('textbox', { name: '请选择数据集' }).click()

  await page.getByText('我私有的').click()
  await page.getByText('梅新成测试2').nth(1).click()
  const page2Promise = page.waitForEvent('popup')
  await page.getByRole('button', { name: '前往关联指标' }).click()
  const page2 = await page2Promise

  await page2.getByRole('button', { name: '取消' }).nth(1).click()

  if (await page.getByText('我知道了').count() > 0) {
    await page.getByText('我知道了').click()
  }

  await page2.getByRole('row', { name: '我是测试用例专用指标' }).getByPlaceholder('请输入字段名').click()
  await page2.locator('span').filter({ hasText: '我是测试用例专用指标' }).nth(2).click()
  await page2.getByRole('button', { name: '保存' }).click()
  await page2.waitForTimeout(2000)
  await page2.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/detail?id=4911&redbi-screen-shot-token=test')
  await expect(page2.getByText('梅新成测试2').first()).toBeVisible()
  await page2.getByRole('cell', { name: '查看 分析 新建数据服务' }).getByRole('img').click()
  await page2.getByText('取消关联').click()
  await page2.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page2.getByText('取消成功').first()).toBeVisible()
  // 批量关联
  await page2.getByText('关联数据集', { exact: true }).first().click()
  await page2.getByText('批量关联多个数据集').click()
  await page2.locator('form').getByRole('textbox', { name: '请选择' }).click()
  await page2.locator('form').getByRole('textbox', { name: '请选择' }).press('CapsLock')
  await page2.locator('form').getByRole('textbox', { name: '请选择' }).fill('TEST')
  await page2.getByText('TEST项目组').click()
  await page2.getByRole('button', { name: '添加' }).click()

  await page2.locator('div:nth-child(6) > div > .d-virtual-tree-node > .d-grid > .d-checkbox-simulator > .d-checkbox-indicator').click()
  await page2.getByRole('button', { name: '添加' }).nth(1).click()
  await page2.getByRole('button', { name: '确认' }).click()
  await expect(page2.getByText('梅新成测试2').first()).toBeVisible()
  await page2.getByRole('cell', { name: '查看 分析 新建数据服务' }).getByRole('img').click()
  await page2.getByText('取消关联').click()
  await page2.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page2.getByText('取消成功').first()).toBeVisible()
  // await page2.waitForTimeout(2000)
})

/**
 * <AUTHOR>
 * @description 指标发布
 * @setp 操作步骤
 * 1、指标列表中点击指标发布2、弹窗点击确认3、再点击取消发布
 * @expect 期望结果
 * 1、发布成功后，状态改变 2、取消发布后，状态恢复
 */

test('indicator-publish', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('我是指标发布用例测试')
  await page.waitForTimeout(2000)
  // await page.locator('div')
  //   .filter({ hasText: /^DataAgent功能更新说明$/ })
  //   .locator('path')
  //   .nth(1)
  //   .click()
  if (await page.getByText('我知道了').count() > 0) {
    await page.getByText('我知道了').click()
  }
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  if (await page.getByText('指标发布').count() > 0) {
    // await page.getByText('我知道了').click()
    await page.getByText('指标发布', { exact: true }).click()
    await page.getByRole('button', { name: '确定' }).nth(1).click()
  }
  await expect(page.getByText('更新成功').first()).toBeVisible()
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('取消发布').click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('更新成功').first()).toBeVisible()
  // await page.waitForTimeout(2000)
})

/**
 * <AUTHOR>
 * @description 关联产品页面
 * @setp 操作步骤
 * 1、指标列表中点击关联产品页面2、输入名称与地址
 * @expect 期望结果
 * 1、关联后，当前指标展示关联页面的名称
 */

test('indicator-connect-product-page', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/list?redbi-screen-shot-token=test')
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).click()
  await page.getByRole('textbox', { name: '搜索指标名称/指标ID/别名/相关字段名/业务口径' }).fill('我是指标发布用例测试')
  await page.waitForTimeout(2000)
  await page.getByRole('cell', { name: '查看 编辑 关联数据集' }).getByRole('img').click()
  await page.getByText('关联产品页面', { exact: true }).click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('关联成功').first()).toBeVisible()
  // await page.waitForTimeout(2000)
})

/**
 * <AUTHOR>
 * @description 设置应用范围
 * @setp 操作步骤
 * 1、进入指标详情页从数据集列表中点击设置应用范围2、点击确认
 * @expect 期望结果
 * 1、当前数据集应用范围列回显改变
 */

test('indicator-set-application', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/indicator/detail?id=5030&redbi-screen-shot-token=test')
  await page.getByRole('cell', { name: '查看 分析 新建数据服务' }).getByRole('img').click()
  await page.getByText('设置应用范围', { exact: true }).click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('设置成功').first()).toBeVisible()
  // await page.waitForTimeout(2000)
})

/**
 * <AUTHOR>
 * @description 新建应用产品
 * @setp 操作步骤
 * 1、点击新建应用产品2、点击确定3、编辑应用产品4、删除新建的应用产品
 * @expect 期望结果
 * 1、确定后，页面显示新建的产品、2、编辑后，页面显示编辑后的名称 3删除后，页面无编辑后的产品
 */

test('create-application-product', async ({ page }) => {
  await page.goto('https://redbi.devops.beta.xiaohongshu.com/application/list?redbi-screen-shot-token=test')
  await page.getByRole('button', { name: '新建应用产品' }).click()
  await page.getByRole('textbox', { name: '请输入' }).click()
  await page.getByRole('textbox', { name: '请输入' }).fill('测试1')
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('应用产品新建成功').first()).toBeVisible()
  await page.getByRole('row', { name: '测试1' }).locator('div').getByText('删除', { exact: true }).click()
  await page.getByRole('button', { name: '确定' }).nth(1).click()
  await expect(page.getByText('应用产品删除成功').first()).toBeVisible()
  await page.waitForTimeout(2000)
})
