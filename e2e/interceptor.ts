// 忽略下面这行错误
// @ts-nocheck
import { BrowserContext } from '@playwright/test'
import axios from 'axios'

const tokenMap: Record<string, string> = {}

export async function interceptor(context: BrowserContext, email: string): Promise<void> {
  const token = await getToken(email)
  await context.route('**/*', (route, request) => {
    const headers = {
      ...request.headers(),
      'redbi-system-service-login-token': token,
    }
    return route.continue({ headers })
  })

  // https://github.com/microsoft/playwright/issues/13890
  context.addInitScript({
    content: `
    // 跳过 guide
    localStorage.setItem('GUIDE-HISTORY:ANALYSIS-HOME-USER-GUIDE', true)
    localStorage.setItem('GUIDE-HISTORY:<EMAIL>', true)
    localStorage.setItem('GUIDE-HISTORY:<EMAIL>', true)
    localStorage.setItem('GUIDE-HISTORY:<EMAIL>', true)
    localStorage.setItem('GUIDE-HISTORY:<EMAIL>', true)
    localStorage.setItem('GUIDE-HISTORY:<EMAIL>', true)
    localStorage.setItem('GUIDE-HISTORY:<EMAIL>', true)
    localStorage.setItem('GUIDE-HISTORY:MATERIAL-MENU-GUIDE', true)
    localStorage.setItem('GUIDE-HISTORY:ANALYSIS-USER-GUIDE', true)
    localStorage.setItem('GUIDE-HISTORY:DATASET-FIELD-GUIDE', true)
    localStorage.setItem('GUIDE-HISTORY:ANALYSIS-BACK-HOME-GUIDE', true)
    localStorage.setItem('ANALYSIS-HOME-USER-GUIDE-recommend', true)
    localStorage.setItem('ANALYSIS-HOME-USER-GUIDE-datasets', true)
    localStorage.setItem('data-agent-update-modal-shown-v2.0', 'true')
  `,
  })
}

export async function getToken(email: string): Promise<string> {
  if (tokenMap[email]) return tokenMap[email]
  const response = await axios.get(
    `https://redbi.devops.beta.xiaohongshu.com/api/system/system-service-login-url?userEmail=${email}`,
  )
  tokenMap[email] = response.data.data
  return response.data.data
}
