/**
 * @note 一些常用函数
 * <AUTHOR>
 * @date 2024-09-11 14:38:13
 * @Last Modified by: ch<PERSON><PERSON><PERSON>@xiaohongshu.com
 * @Last Modified time: 2024-09-11 14:55:20
 */

import { Page } from '@playwright/test'

// 判断数组是否按照第一项日期升序
export function isDateInAscendingOrder(lineJson: [string, ...any[]][] | string[]): boolean {
  // 辅助函数：解析日期字符串并返回Date对象
  function parseDate(dateStr: string): Date {
    return new Date(dateStr)
  }

  // 遍历数组，比较相邻元素的日期
  for (let i = 0; i < lineJson.length - 1; i++) {
    if (lineJson[i] === null) {
      // eslint-disable-next-line
      continue
    }
    const currentDate = parseDate(
      Array.isArray(lineJson[i]) ? lineJson[i][0] : (lineJson[i] as string),
    )
    const nextDate = parseDate(
      Array.isArray(lineJson[i + 1]) ? lineJson[i + 1][0] : (lineJson[i + 1] as string),
    )
    // 如果发现日期不是升序的，返回false
    if (currentDate >= nextDate) {
      return false
    }
  }

  // 遍历完成没有发现问题，返回true
  return true
}

export function isNumberInAscendingOrder(lineJson: [string, ...any[]][] | string[]): boolean {
  for (let i = 0; i < lineJson.length - 1; i++) {
    if (lineJson[i] === null) {
      // eslint-disable-next-line
      continue
    }
    const currentNum = lineJson[i]
    const nextNum = lineJson[i + 1]
    if (currentNum > nextNum) {
      return false
    }
  }
  return true
}
export const isTextInAscendingOrder = (arr:any[]):boolean => {
  // 将数组中的每个元素转换为小写
  const lowerCaseArray = arr.map(item => item.toLowerCase())

  // 创建一个排序后的数组
  const sortedArray = [...lowerCaseArray].sort()

  // 比较原数组和排序后的数组
  return lowerCaseArray.every((value, index) => value === sortedArray[index])
}

/**
 * 判断文本是否被高亮
 */
export const isAllTextHighlighted = (el: HTMLElement, text: string): boolean => {
  let highlighted = true

  walkEl(el, node => {
    const index = node.textContent ? node.textContent.indexOf(text) : -1
    // 如果找到指定的 text 且在纯文本中（没有被标签包裹）那么认为它是非高亮的
    if (index > -1 && node.nodeType === 3) {
      highlighted = false
    }
  })

  return highlighted
}

/**
 * 遍历元素节点（只遍历元素和文本）
 */
export const walkEl = (el: HTMLElement, cb: (node: Node) => any): void => {
  const walkNode = (node: Node) => {
    // only element(1) and text(3)
    if (node.nodeType === 1 || node.nodeType === 3) {
      cb(node)
    }
  }

  const walkNodeList = (nodes: NodeListOf<ChildNode>) => {
    nodes.forEach(node => {
      if (node.hasChildNodes()) {
        walkNodeList(nodes)
      } else {
        walkNode(node)
      }
    })
  }

  if (el.hasChildNodes()) {
    walkNodeList(el.childNodes)
  }

  walkNode(el)
}

export function getYesterdayDate(): string {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(today.getDate() - 1)

  const year = yesterday.getFullYear()
  const month = String(yesterday.getMonth() + 1).padStart(2, '0') // 月份从0开始，需要加1
  const day = String(yesterday.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// '2024年11-12月'
const YearDoubleMonthReg = /^\d+[\u4E00-\u9FFF]*\d{1,2}-\d{1,2}[\u4E00-\u9FFF]*/
export const matchYearDoubleMonth = (str: string): boolean => YearDoubleMonthReg.test(str || '')

// '2024Q4'
const YearQuarterReg = /^\d+[qQ]{1}\d/
export const matchYearQuarter = (str: string): boolean => YearQuarterReg.test(str || '')

/**
 * 等待查询请求结束，自助分析页面刚进入时会自动发起一次请求，等待该请求结束
 */
// eslint-disable-next-line
export const waitSearchApiRequestDone = async (page: Page) => {
  // eslint-disable-next-line
  while (await page.getByRole('button', { name: '查询' }).isEnabled() === false) {}
  await page.waitForTimeout(200)
  // eslint-disable-next-line
  while (await page.getByRole('button', { name: '查询' }).isEnabled() === false) {}
}
