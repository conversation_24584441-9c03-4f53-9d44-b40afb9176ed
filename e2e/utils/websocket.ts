// https://socket.io/docs/v4/socket-io-protocol/
// https://github.com/socketio/socket.io/tree/main/packages/socket.io-parser

import { Page } from '@playwright/test'
import EventEmitter from 'events'

/**
 * ```
 * 2["test",42]
 * ||
 * |└─ JSON-encoded payload
 * └─ packet type (2 => EVENT)
 * ```
 */
const parse = (data: string) => {
  const matched = data.match(/^\d+/)

  const type = matched![0]

  const left = data.substring(type.length)

  let payload = {}
  let path: null | string = null

  if (left) {
    if (left.charAt(0) === '/') {
      const idx = left.indexOf(',')
      if (idx > -1) {
        path = left.substring(0, idx)
        const extra = left.substring(path.length + 1)
        if (extra) {
          payload = JSON.parse(extra)
        }
      }
    } else {
      payload = JSON.parse(left)
    }
  }

  return { type, payload, path }
}

const getResponseType = (type: string) => {
  const t = type.charAt(1)
  return `${type.charAt(0)}${parseInt(t, 10) + 1}${type.slice(2)}`
}

type RequestEntity = {
  requestType: string
  api: string
  headers: Record<string, string>
  params: Record<string, any>
}

// eslint-disable-next-line
export const websocket = (page: Page) => {
  const ee = new EventEmitter()

  const onFramereceived = ({ payload }) => {
    ee.emit('framereceived', parse(payload))
  }

  const onFramesent = ({ payload }) => {
    ee.emit('framesent', parse(payload))
  }

  // eslint-disable-next-line
  page.on('websocket', websocket => {
    websocket.on('framereceived', onFramereceived)
    websocket.on('framesent', onFramesent)
  })

  // return {
  // api request
  const waitForApiResponse = <T = any>(predicate: (request: RequestEntity) => boolean) => new Promise<T>(resolve => {
    const onSent = sent => {
      if (sent.payload[0] === 'apiRequest' && predicate(sent.payload[1])) {
        const onReceived = received => {
          if (received.type === getResponseType(sent.type)) {
            resolve(received.payload[0])
            ee.off('framesent', onSent)
            ee.off('framereceived', onReceived)
          }
        }
        ee.on('framereceived', onReceived)
      }
    }
    ee.on('framesent', onSent)
  })
  const waitForApiResponseWithTimeout = async <T = any>(
    predicate: (request: RequestEntity) => boolean,
    timeoutMs: number,
  ): Promise<T | null> => {
    const timeout = new Promise<null>(resolve => setTimeout(resolve, timeoutMs, null))
    return Promise.race([
      waitForApiResponse(predicate),
      timeout,
    ])
  }
  return {
    waitForApiResponse,
    waitForApiResponseWithTimeout,
  }
  // }
}
