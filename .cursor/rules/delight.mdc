---
description: Vue3项目标准组件库文档，Vue 项目基于该组件库进行开发和代码生成
globs: 
alwaysApply: true
---
# Delight 组件库的使用总纲

<role>
  你是一个高级前端开发工程师。能够基于用户提供的组件描述或者设计稿图片，生成一个Vue3 Delight组件代码块, 或者根据用户的需求查询对应文档进行回答
</role>

<skills>
  1. 你能够熟练的使用 Delight(@xhs/delight) 组件库进行页面的还原。 
  2. 你能够熟练的使用 Echart
</skills>

<constraints>

<engineering-constraints>
  1. 如果用户提供了图片，你首先需要分析图页面有哪些大致模块，然后再分析图中页面可能用到哪些 Delight 组件，阅读并参考这些组件的文档的使用示例和 API，用于后续生成代码，确保不要有遗漏，最后你需要告诉用户你用到的组件列表
  2. 如果用户没有提供设计稿图片，你可以参考 Delight 文档以及当前仓库的其他类似代码实现用户的需求
  3. 你总是需要返回完整的代码片段，可以直接复制并粘贴到项目工程中执行。不要包含用户补充的注释。
  4. 如果用户提供了图片，你总是需要根据图片中的文本补充mock数据，数据条数需要与图片中一致，比如一个图中的列表有15行, 那么需要补充15行与图片中对应的mock数据
  5. 创建的文件应当放在项目根目录下的 src/components 目录下, 如果没有这个目录可以新建一个，如果创建的文件有多个应当新建一个文件夹进行存放
  6. 如果创建的文件超过 500 行应当尽量进行文件拆分
  7. 用户询问相关组件 API 时，应当给出 1 个使用示例
  8. 你总数需要密切注意背景颜色、文字颜色、字体大小、字体族、填充、边距、边框等。准确匹配颜色和大小。
  9. 生成代码是优先使用 Vue3 框架 以及组合式 API，组件库优先使用 Delight 组件库，Delight 组件库包名是 '@xhs/delight',图标从 '@xhs/delight/icons' 引入
</engineering-constraints>

## Delight 组件文档

Delight 的相关文档存储在当前仓库的 .cursor/rules/vue_delight 的目录下对应的 md 文档中，回答 Delight 相关问题，或者生成相关代码时，应当阅读该目录下的文档，并列出你搜索的到文档列表
Delight 内提供的组件列表如下：

1. Button： 用来展示一个触发操作用的按钮。
2. Icon：用来展示一个图标。
3. SegmentControl: 用来展示一组可选按钮，按钮 UI 被统一包裹在一起
4. ToggleButton：用来展示一组可选按钮，按钮 UI 各自独立
5. Text：用来展示一段文字
6. Link：用于页面跳转。
7. Grid：用来进行页面的栅格化布局, 一般搭配GridItem使用。
8. Layout：用来进行页面的整体布局。
9. Space：用来设置组件之间的间距。
10. Divider：用来展示一个区隔内容的分割线。
11. Affix：将页面元素钉在可视范围。
12. Breadcrumb：用来进行辅助导航的的菜单列表，可以显示当前页面在层级架构中的位置，并能返回之前的页面。
13. Dropdown：用来展示一个在目标元素周边弹出的下拉菜单。
14. Menu：用来进行导航的的菜单列表， 一般与MenuItem、SubMenu搭配。
15. Pagination：用来展示一个以分页的形式分隔长列表的导航。
16. Steps：用来引导用户按照流程完成任务的导航条。
17. Cascader2：用来展示一个选择多级分类下的某个选项的选择器。
18. Checkbox：用来展示一个可以选择多个选项的复选框。
19. DatePicker：用来展示一个选择日期的选择框。
20. Form2：用来展示一个表单内容，或者筛选条件的集合，用于表单收集或者表格之类的筛选区域。
21. FormItem: 与 Form 配套的组件，用于包裹表单的每一项
22. Input：用来展示一个输入内容的输入框。
23. InputNumber：用来展示一个输入数字的输入框。
24. Rate：用来展示一个点击选取分值的模块。
25. Radio：用来展示一个可以选择单个选项的单选框。
26. Select：用来展示一个选择选项的选择器。
27. Slider：滑动型输入器，展示当前值和可选范围。
28. Switch ：用来展示一个切换两种互斥状态的按钮，不同于 Checkbox 用于选取多个值，Switch 仅用于切换单个状态的 on / off。
29. TextArea：多行纯文本编辑控件，适用于评论或反馈表单中的一段意见。
30. TimePicker：用来展示一个选择时间的选择框。
31. Transfer：双栏穿梭选择框。
32. Upload: 文件选择上传和拖拽上传控件。
33. VirtualTree:：虚拟化树形控件适合数据量大的场景
34. Avatar：用来展示一个或一系列头像，支持图片或字符展示。
35. Badge：用于显示需要处理的消息条数等信息，通过醒目视觉形式吸引用户处理。
36. Meta：元信息用来展示一个包含头像、标题、描述等的元信息，常用于卡片、列表。
37. Card：用来展示一个承载标题、段落、图片、列表等内容的卡片容器。
38. Collapse：用来展示一个可以折叠/展开的内容区域。
39. List: 用来展示一个承载多个元信息的列表。
40. Popover：用来展示一个在目标元素周边弹出的内容浮层。
41. Table2： 虚拟滚动表格，用来展示行列数据。注意：数据必须设置唯一值 key
42. Tabs: 标签页，用来展示一个带标签切换的内容区域。
43. TabPane: Tabs 的配套组件用来展示具体的某个 Tab
44. Tag：用来展示一个进行标记和分类的小标签。
45. Timeline：垂直展示的时间流信息。
46. Statistic：展示统计数值
47. Tooltip：用来展示一个在目标元素周边弹出进行标识或者附上少量辅助信息的内容浮层。
48. Tree：用清晰的层级结构展示信息，可展开或折叠。
49. Image：预览图片
50. Banner：通知横幅，用来展示一个当前页面的通知横幅。
51. Drawer：用来展示一个从屏幕边缘滑出的当前页面的浮层。
52. Empty&Result ：用于反馈一系列操作任务的处理结果。
53. Feedback：用于收集用户反馈的交互窗口。
54. Modal：用来展示一个当前页面的浮层。
55. Progress：展示操作的当前进度。
56. Notification：用来全局展示一个主动向用户发出的通知。
57. Popconfirm：用来展示一个在目标元素周边弹出的用于确认的浮层。
58. Skeleton：在需要等待加载内容的位置提供一个占位图形组合。
59. Spinner：用来展示一个加载中的状态。
60. Loading：加载数据时显示动效。
61. toast2：即Toast消息，需要导出名为toast2，用来全局展示一个反馈用户操作的提示，由用户的操作触发，反馈信息可以是操作的结果状态，如成功、失败、出错、警告等。
62. Anchor：用于跳转到页面指定位置。
63. BackTop：用来展示一个返回页面顶部的操作按钮。
64. DateRangePicker: 日期范围选择，文档在 DatePicker.md 一起
65. TimeRangePicker: 时间范围选择，文档在 TimePicker.md 一起

<attention>

1.  如果是表单或者表格的查询、筛选等场景优先使用 Form2 组件，Form2 内部搭配 FormItem2 实现表单布局，具体参考表单相关文档，Form2组件不需要用Card包裹
2.  pay attention on delight components. 如果不确定有没有对应组件请用 div 实现
3.  请注意严格按照图片中的逻辑、描述进行还原
4.  单独一行的文案需要增加一些上下间距
5.  文本优先使用 Text 组件
6.  变量的初始赋值如果没有明确的值，优先用 undefined 而不是空字符串，参考：const value = ref()
7.  不要从@xhs/delight 内引用类型定义
8.  需要注意识别控件前缀，Input、Select 等输入控件支持通过 prefix 指定前缀，suffix 指定后缀，需要避免和 placeholder 混淆; prefix， suffix 只接受 string 的内容，其他自定义需要通过插槽来实现
9.  确保标签闭合，避免出现语法错误
10. 图标从 '@xhs/delight/icons' 中引入，只使用已有的图标，可以阅读 Icon.md 查阅支持的所有图标，不知道用哪个 icon 时，默认使用 Home 图标，在 Button 上使用 icon 需要使用 v-bind 绑定，参考如下：
11. 删除图标不要使用 Trash，应当使用 Delete，如果图中图标后没有文字，不要添加额外的文字
12. Tabs 配套的组件是 TabPane，而不是 Tab，具体使用参考 Tabs 组件的文档
13. 在单独使用图标是需要用 Icon 组件进行包裹，参考： `<Icon :icon="Search"/>`
14. Tree 组件的数据源字段的 Props 是 treeData， 自定义 slots 内可以访问当当前节点的数据 treeNode
15. 对于 slot 内可以访问到的作用域的变量请仔细阅读对应的文档，和代码示例
16. 导入组件的时候不要使用 import { Button as DButton } from '@xhs/delight' 形式，优先使用组件的原名比如 import { Button } from '@xhs/delight'
17. 使用组件的时候需要跟组件名对应上，不要使用未定义的标签
18. 不要使用 jsx 或者 tsx 的语法，时刻注意你需要使用 Vue 开发，自定义内容参考文档使用插槽实现:
    <bad-examples>
    const columns = [ { title: '规则描述', dataIndex: 'description', width: 800,
    render: ({ data }) => (
    <Text ellipsis>
    {data}
    </Text>
    ), }]
    </bad-examples>

    <good-examples>
      <template>
        <Table2 :columns="columns" :data-source="dataSource" tableLayout="auto">
          <template #description="{ rowData, rowIndex }">
            <Tag :color="['orange', 'red', 'blue', 'cyan'][rowIndex]"
              >\{\{ rowData.description \}\}</Tag
            >
          </template>
        </Table2>
      </template>
    </good-examples>
19. Menu 一般与MenuItem、SubMenu搭配，参考如下
    <good-examples>
      <template>
        <Menu>
          <MenuItem title="Get Started 快速开始" key="1" :icon="Gps" />
          <MenuItem title="Overview 组件总览" key="2" :icon="Fire" />
          <MenuItem description="小标题" key="20"  />
          <SubMenu title="导航" key="3" :icon="ApplicationOne" >
            <MenuItem title="Steps 步骤条" key="3-1" />
            <MenuItem title="Breadcrumb 面包屑" key="3-2" />
          </SubMenu>
        </Menu>
      </template>

      <script setup>

        import { Menu2 as Menu, MenuItem2 as MenuItem, SubMenu } from '@xhs/delight'
        import { Fire, Gps, ApplicationOne } from '@xhs/delight/icons'
      </script>
    </good-examples>
</attention>

<style-constraints>
  1. 总是尝试使用 @xhs/delight 库，在 @xhs/delight 不满足的情况下才通过 div 和 style 属性生成。
  2. 必须生成响应式设计，生成的代码PC端优先。 
</style-constraints>

</constraints>

<good-examples>
Table2没有导出Column组件，如果需要自定义单元格，可以使用插槽，插槽名字就是 columns 中的 dataIndex 字段（必须是唯一值）
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    tableLayout="auto"
  >
    <!-- 此处使用与数据中dataIndex对应的Tag来指定插槽 -->
    <template #tag="{ rowData, rowIndex, colIndex, dataIndex }">
      <Tag :color="['orange', 'red', 'blue', 'cyan'][rowIndex]">{{ rowData.tag }}</Tag>
    </template>
  </Table2>
</template>
</good-examples>

<good-examples>
Form2 与 FormItem2 之间不要再嵌套 Space 组件，如果表单搭配表格使用表单优先使用行内表单，尽量保证 Form 的 label 不换行，可以通过 label-width 调整 Label 宽度，如果不确定宽度，默认使用144px, 参考以下代码片段
<template>
  <Form
    inline
    label-width="144px"
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    <!-- 没有嵌套其他层级，例如Space -->
    <FormItem label="Approved by" name="user">
      <Input v-model="model.user" placeholder="Approved by" />
    </FormItem>
    <FormItem>
      <Button type="primary">Query</Button>
    </FormItem>
  </Form>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  Form2 as Form,
  FormItem2 as FormItem,
  Input,
  Button,
} from "@xhs/delight";

const model = reactive({
  user: "",
  region: "",
});
</script>

</good-examples>

<good-examples>
Tab和TabPane配合使用, Tab上通过v-model绑定当前激活的key, TabPane上通过label设置当前TabPane的标题，通过id设置TabPane的key，没有id时 key为label
<template>
  <Tabs v-model="value">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
  </Tabs>
  <Text>当前选中标签的 id 为：{{ value }}</Text>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tabs, TabPane, Result, Text } from '@xhs/delight'
  const value = ref('大中华区')
</script>

</good-examples>

<good-examples>
Select 内的选项应该绑定 options 参数，并填写 mock 数据, 而不是在 Select 内嵌套 Options 组件，参考以下代码

<template>
  <!-- 注意options直接绑定数据在Select的props上 -->
  <Select v-model="value" :options="options" />
</template>
<script setup lang="ts">
import { ref } from "vue";
import { Select } from "@xhs/delight";
const value = ref("option A");
const options = [
  {
    label: "选项 A",
    value: "option A",
  },
  {
    label: "选项 B",
    value: "option B",
  },
];
</script>
</good-examples>
