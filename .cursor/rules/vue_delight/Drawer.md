

##    基本使用

通过 `visible` 设置抽屉展示，通过 `title` 设置抽屉标题，通过 `slots.default` 设置抽屉内容：

```vue
<template>
  <Button type="primary" @click="handleOpen">Drawer</Button>
  <Drawer
    v-model:visible="visible"
    title="逍遥游"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Drawer, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }
</script>
```

##    自定义标题

通过 `slots.title` 设置自定义标题：

```vue
<template>
  <Button type="primary" @click="handleOpen">Drawer</Button>
  <Drawer
    v-model:visible="visible"
  >
    <template #title>
      <Text
        :icon="{ icon: Fire, theme: 'filled', color: 'red-6' }"
        type="h5"
        bold
      >
        逍遥游
      </Text>
    </template>
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Drawer, Text } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }
</script>
```

##    自定义底部

通过 `slots.footer` 设置自定义底部：

```vue
<template>
  <Button type="primary" @click="handleOpen">Drawer</Button>
  <Drawer
    v-model:visible="visible"
  >
    <template #title>
      <Text
        :icon="{ icon: Fire, theme: 'filled', color: 'red-6' }"
        type="h5"
        bold
      >
        逍遥游
      </Text>
    </template>
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
    <template #footer>
      <Button type="danger" @click="handleClose">关闭</Button>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Drawer, Text } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    位置

通过 `placement` 设置抽屉展示位置，支持 `top` 、 `right` 、 `bottom` 、 `left` 四个位置，默认 `right`：

```vue
<template>
  <Space>
    <Button type="primary" @click="() => handleOpen('top')">top</Button>
    <Button type="primary" @click="() => handleOpen()">right</Button>
    <Button type="primary" @click="() => handleOpen('bottom')">bottom</Button>
    <Button type="primary" @click="() => handleOpen('left')">left</Button>
  </Space>
  <Drawer
    v-model:visible="visible"
    :placement="placement"
    title="逍遥游"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Drawer, Text } from '@xhs/delight'

  const placement = ref()
  const visible = ref(false)

  function handleOpen(v) {
    placement.value = v
    visible.value = true
  }
</script>
```

##    尺寸

通过 `size` 设置尺寸，支持 `default` 、 `large` 两种尺寸，也可以自定义：

```vue
<template>
  <Space>
    <Button type="primary" @click="() => handleOpen()">default</Button>
    <Button type="primary" @click="() => handleOpen('large')">large</Button>
    <Button type="primary" @click="() => handleOpen(300)">300px</Button>
    <Button type="primary" @click="() => handleOpen('calc(100vw - 300px)')">calc(100vw - 300px)</Button>
  </Space>
  <Drawer
    v-model:visible="visible"
    :size="size"
    title="逍遥游"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Drawer, Text } from '@xhs/delight'

  const size = ref()
  const visible = ref(false)

  function handleOpen(s) {
    size.value = s
    visible.value = true
  }
</script>
```

##    默认可关闭

通过 `closeable` 设置默认可关闭，默认为 `true`：

```vue
<template>
  <Button type="primary" @click="handleOpen">Drawer</Button>
  <Drawer
    v-model:visible="visible"
    :closeable="false"
    title="逍遥游"
  >
    <Space
      :style="{
        minHeight: '100%',
        paddingBottom: '24px',
      }"
      direction="vertical"
    >
      <Text>
        北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
      </Text>
      <Button
        :style="{
          marginTop: 'auto',
        }"
        type="danger"
        block
        @click="handleClose"
      >
        关闭
      </Button>
    </Space>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Drawer, Text, Space } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    点击外部关闭

通过 `outsideCloseable` 设置点击外部关闭

```vue
<template>
  <Button type="primary" @click="handleOpen">outsideCloseable</Button>
  <Drawer
    v-model:visible="visible"
    outside-closeable
    title="逍遥游"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Drawer, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }
</script>
```

##    遮罩层

通过 `mask` 设置遮罩层
- 通过 `maskCloseable` 设置点击遮罩层关闭

```vue
<template>
  <Space>
    <Button type="primary" @click="handleOpen">maskCloseable</Button>
  </Space>
  <Drawer
    v-model:visible="visible"
    mask
    mask-closeable
    title="逍遥游"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Drawer, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen(v) {
    visible.value = true
  }
</script>
```

##    自定义层级

设置 zIndex 属性，去控制 Drawer 的层级

```vue
<template>
  <Space>
    <Button type="primary" @click="handleOpen">maskCloseable</Button>
  </Space>
  <Drawer
    v-model:visible="visible"
    mask
    mask-closeable
    :z-index="1000"
    title="逍遥游"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Drawer, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen(v) {
    visible.value = true
  }
</script>
```

##    注意

当在 Drawer 组件中嵌套使用 Modal 组件时，在 Drawer 和 Modal 组件均不使用遮罩层的情况下，请不要同时在 Drawer 和 Modal 组件中使用 `outsideCloseable` 配置，否则点击空白区域会导致两者同时关闭：

```vue
<template>
  <Button type="primary" @click="handleOpen">Drawer</Button>
  <Drawer
    v-model:visible="visible"
    title="逍遥游"
    outside-closeable
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
    <Button type="primary" @click="handleModalOpen">Modal</Button>
  </Drawer>
  <Modal
    v-model:visible="modelVisible"
    title="逍遥游"
    outside-closeable
    :mask="false"
    @confirm="handleModalClose"
    @cancel="handleModalClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Drawer, Text, Modal } from '@xhs/delight'

  const visible = ref(false)
  const modelVisible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleModalOpen() {
    modelVisible.value = true
  }

  function handleModalClose() {
    modelVisible.value = false
  }
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|title|抽屉标题|string|-|
|placement|抽屉展示的位置|'top' &#124; 'right' &#124; 'bottom' &#124; 'left'|'bottom'|
|size|抽屉尺寸|'default' &#124; 'large' &#124; string &#124; number|'default'|
|closeable|抽屉默认可关闭|boolean|true|
|visible|抽屉是否展示|boolean|false|
|outsideCloseable|抽屉点击外部可关闭|boolean|false|
|mask|抽屉带遮罩层|boolean|false|
|maskCloseable|抽屉点击遮罩层可关闭|boolean|false|
|maskStyle|遮罩样式|CSSProperties|-|
|zIndex|设置 Drawer 的 z-index|number|99|
|ssr|当你发现默认插槽可能出现挂载多次的问题，请设置 ssr=false|boolean|true|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:visible|抽屉展示状态变化的回调事件|(v: boolean) => void|-|
|close|关闭弹窗的回调时间|() => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|抽屉的内容|
|title|抽屉的标题|
|footer|抽屉的底部|