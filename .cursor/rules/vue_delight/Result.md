

### 何时使用 当有重要操作需告知用户处理结果，且反馈内容较为复杂时使用。

##    基本使用

#### 成功结果：
```vue
<template>
  <Result status="success" title="创建成功" sub-title="活动将于 2021 年 11 月 20 日 13 点开始。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

#### 失败：

```vue
<template>
  <Result status="fail" title="加载失败" sub-title="加载页面失败，请稍后重试。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, But<PERSON> } from '@xhs/delight'
</script>
```

#### 404：
```vue
<template>
  <Result status="404" title="找不到页面" sub-title="请刷新后重试。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

#### 没有权限：
```vue
<template>
  <Result status="forbidden" title="暂无访问权限" sub-title="请联系管理员，或通过平台申请权限。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

#### 没有找到相关内容：
```vue
<template>
  <Result status="unrelated" title="没有找到相关内容" sub-title="请修改查询条件后重试。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

#### 暂无内容：
```vue
<template>
  <Result status="resultless" title="暂无内容" sub-title="请按照引导添加内容。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

#### 空：
```vue
<template>
  <Result status="empty" title="当前状态的主要标题" sub-title="可自定义的一段文字，用来表述一些辅助信息，比如平台的使用小技巧、帮助、小历史等等内容。需要考虑加载时间，不要放太多内容导致用户读不完。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

#### 建设中：
```vue
<template>
  <Result status="constructing" title="功能建设中" sub-title="敬请期待。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

#### 操作指引：
```vue
<template>
  <Result status="guide" title="操作指引" sub-title="请按照引导添加内容。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

##    使用默认插槽插入底部内容

```vue
<template>
  <Result status="fail" title="加载失败" sub-title="请刷新后重试。">
      <Button type="primary" style="margin-right:var(--size-space-large)">一级操作</Button>
      <Button>二级操作</Button>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button } from '@xhs/delight'
</script>
```

##    自定义结果图标

```vue
<template>
  <Result status="empty" title="暂无内容" sub-title="请刷新后重试。">
      <Button>返回上页</Button>
      <template #icon>
          <Icon :icon="Home" color="info" size="extra-large" />
      </template>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button, Icon } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
</script>
```

##    自定义标题内容

```vue
<template>
  <Result status="empty" title="暂无内容" sub-title="请刷新后重试。" >
      <Button>返回上页</Button>
      <template #title>
          <Icon :icon="Home" color="info" size="extra-large" />
      </template>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button, Icon } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
</script>
```

##    自定义副标题内容

```vue
<template>
  <Result status="empty" title="暂无内容" sub-title="请刷新后重试。" >
      <Button>返回上页</Button>
      <template #subTitle>
          <Icon :icon="Home" color="info" size="extra-large" />
      </template>
  </Result>
</template>

<script setup lang="ts">
  import { Result, Button, Icon } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
</script>
```

##    API 参考

#### Result 属性 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|status|`Result` 返回状态|`'success'` \| `'fail'` \| `'404'` \| `'forbidden'` \| `'empty'` \| `'resultless'` \| `'constructing'` \| `'guide'` \| `'unrelated'`|`'empty'`|
|title|标题|`string`|-|
|subTitle|副标题描述|`string`|-|

#### Result 插槽 |插槽|说明|
| :- | :- |
| default | 默认插槽 |
| icon | 自定义 `Result` 图标 |
| title | 自定义 `Result` 标题区的内容 |
| subTitle | 自定义 `Result` 副标题描述区的内容 |
| bottom | 自定义 在底部插入内容 |
