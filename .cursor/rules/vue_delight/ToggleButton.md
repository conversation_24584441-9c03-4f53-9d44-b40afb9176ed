

##    单选

```vue
<template>
  <ToggleButton v-model="value" :options="options" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1' },
    { label: '二线城市', value: '2' },
    { label: '三线城市', value: '3' },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    多选

```vue
<template>
  <ToggleButton v-model="value" :options="options" multiple />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref(['1', '3'])

  const options = [
    { label: '广告通', value: '1' },
    { label: 'Google', value: '2' },
    { label: 'Facebook', value: '3' },
    { label: '爱奇艺', value: '4' },
    { label: '趣头条', value: '5' },
    { label: '网易云音乐', value: '6' },
    { label: '华为', value: '7' },
    { label: 'OPPO', value: '8' },
    { label: 'VIVO', value: '9' },
  ]
</script>
```

##    间距

通过`gap`设置，默认12px。

```vue
<template>
  <ToggleButton v-model="value" :options="options" />
  <br/>
  <ToggleButton v-model="value" :gap='20' :options="options" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1' },
    { label: '二线城市', value: '2' },
    { label: '三线城市', value: '3' },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    轻量风格

```vue
<template>
  <ToggleButton v-model="value" :options="options" light />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1' },
    { label: '二线城市', value: '2' },
    { label: '三线城市', value: '3' },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    图标

通过 icon 设置ToggleButton图标，通过 iconPosition 更改 icon 插入的位置，也通过 iconSpace 更改 icon 插入的间距：

```vue
<template>
  <Space direction="vertical">
    <ToggleButton v-model="value" :options="options" :icon="Star"/>
    <ToggleButton v-model="value" :options="options" :icon="Star" icon-position="right"/>
    <ToggleButton v-model="value" :options="options" :icon="Star" :iconSpace="8" icon-position="right"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, ToggleButton } from '@xhs/delight'
  import { Star } from '@xhs/delight/icons'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1' },
    { label: '二线城市', value: '2' },
    { label: '三线城市', value: '3' },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    尺寸

size 可选值 default 、small，默认是 default。

```vue
<template>
  <ToggleButton v-model="value" :options="options" />

  <br />

  <ToggleButton v-model="value" :options="options" size="small" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1' },
    { label: '二线城市', value: '2' },
    { label: '三线城市', value: '3' },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    禁用

可以直接设置 ToggleButton 属性 disabled 为 true，禁用全部。

也可以通过 options 对象中每一项设置 disabled: true，禁用单个。

```vue
<template>
  <ToggleButton v-model="value" :options="options" disabled />

  <br />
  <br />

  <ToggleButton v-model="value" :options="options" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1' },
    { label: '二线城市', value: '2', disabled: true },
    { label: '三线城市', value: '3' },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    可取消 cancelable

`cancelable` 只针对单选的情况下，默认是必选中一项，`cancelable = true` 表示可以取消选中

```vue
<template>
  <ToggleButton v-model="value" :options="options" cancelable />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1' },
    { label: '二线城市', value: '2' },
    { label: '三线城市', value: '3' },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    可配置 tooltip

tooltip 接受一个字符串，便会展示 tooltip，也可以通过 tooltipValidate 控制 tooltip 是否展示

```vue
<template>
  <ToggleButton v-model="value" :options="options" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ToggleButton } from '@xhs/delight'

  const value = ref('1')

  const options = [
    { label: '一线城市', value: '1', tooltip: '一线城市' },
    { label: '二线城市', value: '2', tooltip: '二线城市', disabled: true },
    { label: '三线城市', value: '3', tooltip: '不会显示', tooltipValidate: false },
    { label: '四线城市', value: '4' },
  ]
</script>
```

##    API 参考

```
type ValueType = number | string | boolean

interface Option { 
  lable: string
  value: string | number | boolean
  disabled?: boolean
  tooltip?: string
  tooltipValidate?: boolean // 判断 tooltip 是否显示
  [props: string]: any  
}

```

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|model-value|选中的值|ValueType &#124; ValueType[]|-|
|options|ToggleButton 组件中的选项|Option[]|-|
|multiple|是否可以多选|boolean|false|
|gap|间距|number|12|
|cancelable|可取消选中元素|boolean|false|
|light|轻量风格|boolean|false|
|size|尺寸大小|string  可选 'default' &#124; 'small'|default|
|icon|[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|`(p: IconProps) => string`|-|
|iconPosition|图标位置|`'left'` \| `'right'`|`'left'`|
|iconSpace|图标间距|string \| number|-|

## 事件 |事件|说明|类型|
| :- | :- | :- |
|update:model-value|变化时回调函数|(v) => void|
|change|变化时回调函数|(v) => void|
|click|`click` 事件的 handler|`(e: Event, option: Option) => void` |