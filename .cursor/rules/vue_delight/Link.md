

import { Icon, Text } from '@xhs/delight'

##    尺寸

通过 `size` 设置链接的尺寸：
```vue
<template>
  <Link size="large" href='https://delight.devops.xiaohongshu.com' target="_blank">large</Link>
  <Link href='https://delight.devops.xiaohongshu.com' target="_blank">default</Link>
  <br/>
  <Link size="small" href='https://delight.devops.xiaohongshu.com' target="_blank">small</Link>
  <br/>
</template>

<script setup lang="ts">
  import { Link } from '@xhs/delight'
</script>
```

##    链接颜色

通过 `color` 设置链接的颜色：

```vue
<template>
  <Link href='https://delight.devops.xiaohongshu.com' target="_blank">default</Link>
  <br/>
  <Link color="warning" href='https://delight.devops.xiaohongshu.com' target="_blank">warning</Link>
  <br/>
  <Link color="danger" href='https://delight.devops.xiaohongshu.com' target="_blank">danger</Link>
  <br/>
  <Link color="success" href='https://delight.devops.xiaohongshu.com' target="_blank">success</Link>
  <br/>
  <Link color="info" href='https://delight.devops.xiaohongshu.com' target="_blank">info</Link>
  <br/>
  <Link href='https://delight.devops.xiaohongshu.com' muted target="_blank">muted</Link>
</template>

<script setup lang="ts">
  import { Link } from '@xhs/delight'
</script>
```

##    图标

通过 `icon` 设置带有图标的链接， iconPosition 设置图标的位置：

```vue
<template>
    <Link :icon="BubbleChart">left</Link>
    <br />
    <Link :icon="BubbleChart" icon-position="right">right</Link>
  </Space>
</template>

<script setup lang="ts">
  import { Link } from '@xhs/delight'
  import { BubbleChart } from '@xhs/delight/icons'
</script>
```

##    禁用

通过 `disabled` 禁用链接：
```vue
<template>
  <Link href='https://delight.devops.xiaohongshu.com' disabled target="_blank">default</Link>
  <br/>
  <Link color="warning" href='https://delight.devops.xiaohongshu.com' disabled target="_blank">warning</Link>
  <br/>
  <Link color="danger" href='https://delight.devops.xiaohongshu.com' disabled target="_blank">danger</Link>
  <br/>
  <Link color="success" href='https://delight.devops.xiaohongshu.com' disabled target="_blank">success</Link>
  <br/>
  <Link color="info" href='https://delight.devops.xiaohongshu.com' disabled target="_blank">info</Link>
  <br/>
  <Link href='https://delight.devops.xiaohongshu.com' muted disabled target="_blank">muted</Link>
</template>

<script setup lang="ts">
  import { Link } from '@xhs/delight'
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|size|尺寸|`'default'` \| `'small'` \| `'large'`|`'default'`|
|color|链接的文字颜色|`'primary' \| 'warning' \| 'danger' \| 'info' \| 'success' `|'primary'|
|icon|[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|`(p: IconProps) => string`|-|
|iconPosition|图标位置|`'left' \| 'right'`|'left'|
|href|原生 href 属性|`string`|-|
|disabled|链接禁用鼠标事件|`boolean`|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|`(e: MouseEvent) => void`|-|
|mousedown|鼠标按下的回调事件|`(e: MouseEvent) => void`|-|
|mouseenter|鼠标进入的回调事件|`(e: MouseEvent) => void`|-|
|mouseleave|鼠标离开的回调事件|`(e: MouseEvent) => void`|-|
|mouseup|鼠标抬起的回调事件|`(e: MouseEvent) => void`|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|链接的内容|
