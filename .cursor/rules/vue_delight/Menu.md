
import { Link } from '@xhs/delight'

Delight 目前已经重构了 Menu，Menu2 主要的功能是受控，Menu1 的功能是基于 ` 做的，
用户对高亮和展开没有受控要求可以使用 Menu1，有此方面的定制需求可以使用 Menu2 
升级手册：升级到 Menu2 注意项

##    基本使用

```vue
<template>
  <Menu>
    <MenuItem title="Get Started 快速开始" key="1" :icon="Gps" />
    <MenuItem title="Overview 组件总览" key="2" :icon="Fire" />
    <MenuItem description="小标题" key="20"  />
    <SubMenu title="导航" key="3" :icon="ApplicationOne" >
      <MenuItem title="Steps 步骤条" key="3-1" />
      <MenuItem title="Breadcrumb 面包屑" key="3-2" />
    </SubMenu>
  </Menu>
</template>

<script setup>

  import { Menu2 as Menu, MenuItem2 as MenuItem, SubMenu } from '@xhs/delight'
  import { Fire, Gps, ApplicationOne } from '@xhs/delight/icons'





  
</script>
```

##    横向展示

```vue
<template>
  <Menu mode="horizontal">
    <MenuItem title="基础组件" key="1" :icon="Gps" />
    <MenuItem title="物料平台" key="2" :icon="Fire" />
    <SubMenu title="更多资源" :icon="ApplicationOne" key="3" >
      <MenuItem title="Delight Formily" key="3-1" />
      <MenuItem title="Icon 平台" key="3-2" />
    </SubMenu>
  </Menu>
</template>

<script setup>
  import { Menu2 as Menu, MenuItem2 as MenuItem, SubMenu } from '@xhs/delight'
  import { Fire, Gps, ApplicationOne } from '@xhs/delight/icons'

</script>
```

##    受控展开

```vue
<template>
  <Menu v-model:open-keys="openKeys">
    <MenuItem title="Get Started 快速开始" key="1" :icon="Gps" />
    <MenuItem title="Overview 组件总览" key="2" :icon="Fire" />
    <SubMenu title="导航" key="3" :icon="ApplicationOne" >
      <MenuItem title="Steps 步骤条" key="3-1" />
      <MenuItem title="Breadcrumb 面包屑" key="3-2" />
    </SubMenu>
  </Menu>
</template>

<script setup>
  import { ref } from 'vue'
  import { Menu2 as Menu, MenuItem2 as MenuItem, SubMenu } from '@xhs/delight'
  import { Fire, Gps, ApplicationOne } from '@xhs/delight/icons'

  const openKeys = ref(['3'])
</script>
```

##    受控选择

```vue
<template>
  <Menu v-model:selected-keys="selectedKeys">
    <MenuItem title="Get Started 快速开始" key="1" :icon="Gps" />
    <MenuItem title="Overview 组件总览" key="2" :icon="Fire" />
    <SubMenu title="导航" key="3" :icon="ApplicationOne" >
      <MenuItem title="Steps 步骤条" key="3-1" />
      <MenuItem title="Breadcrumb 面包屑" key="3-2" />
    </SubMenu>
  </Menu>
</template>

<script setup>
  import { ref } from 'vue'
  import { Menu2 as Menu, MenuItem2 as MenuItem, SubMenu } from '@xhs/delight'
  import { Fire, Gps, ApplicationOne } from '@xhs/delight/icons'

  const selectedKeys = ref(['1'])
</script>
```

##    受控折叠

```vue
<template>
  <Switch v-model="collapse" />
  <br />

  <Menu v-model:collapse="collapse">
    <MenuItem title="Get Started 快速开始" key="1" :icon="Gps" />
    <MenuItem title="Overview 组件总览" key="2" :icon="Fire" />
    <SubMenu title="导航" key="3" :icon="ApplicationOne" >
      <MenuItem title="Steps 步骤条" key="3-1" />
      <MenuItem title="Breadcrumb 面包屑" key="3-2" />
    </SubMenu>
  </Menu>
</template>

<script setup>
  import { ref } from 'vue'
  import { Menu2 as Menu, MenuItem2 as MenuItem, SubMenu, Switch } from '@xhs/delight'
  import { Fire, Gps, ApplicationOne, MenuFold } from '@xhs/delight/icons'

  const collapse = ref(false)

</script>
```

##    API 参考

### Menu 属性 |属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|collapse (v-model)|是否初始折叠|boolean|false|
|collapseTitle|折叠文案|string|-|
|useCollapse|是否开启折叠|boolean|true|
|width|Menu 菜单的宽度（仅仅针对 vertical 模式下）|number|256|
|mode|菜单的展示方式 |	'horizontal' &#124; 'vertical'|'vertical'|
|openKeys (v-model)|当前展开的 SubMenu 菜单项 key 数组|string[]|-|
|selectedKeys (v-model)|当前选中的菜单项 key 数组|string[]|-|

### Menu 事件 |事件 | 说明 | 回调参数|
| :- | :- | :- |
|click|点击 MenuItem 调用此函数|`({ key }) => void`|
|openChange|SubMenu 展开/关闭的回调|`(openKeys: string[]) => void`|
|select|被选中时调用|`({ key, selectedKeys }) => void`|

### MenuItem 属性 |属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|icon|选项的图标，@xhs/delight/icons 默认提供了一套基于 IconPark 的 svg 图标|(p: IconProps) => string|-|
|disabled|禁用状态|boolean|false|
|title|选项的标题|string|-|
|key|item 的唯一标志|string|-|
|to|路由跳转地址|RouteLocationRaw|-|
|open|外链地址|string|-|

### MenuItem 插槽 |插槽 | 说明|
| :- | :- |
|icon|图标|
|title|选项的标题|

### SubMenu 属性 |属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|icon|选项的图标，@xhs/delight/icons 默认提供了一套基于 IconPark 的 svg 图标|(p: IconProps) => string|-|
|disabled|禁用状态|boolean|false|
|title|选项的标题|string|-|
|key|item 的唯一标志|string|-|

### SubMenu 插槽 |插槽 | 说明|
| :- | :- |
|icon|图标|
|title|选项的标题|

