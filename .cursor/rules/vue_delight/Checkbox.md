

##    基本使用

通过 `value` 设置复选框的值，通过 `label` 或 `slots.default` 设置复选框右侧展示的内容：

```vue
<template>
  <Space direction="vertical" align="start">
    <Checkbox value="option A"/>
    <Checkbox value="option A" label="选项 A" autofocus/>
    <Checkbox value="option A">选项 A</Checkbox>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Checkbox } from '@xhs/delight'
</script>
```

### 描述 通过 `description` 或 `slots.description` 设置描述：

```vue
<template>
  <Space direction="vertical" align="start">
    <Checkbox
      value="option A"
      label="选项 A"
      description="这是一个名为 A 的选项"
    />
    <Checkbox
      value="option A"
      label="选项 A"
    >
      <template #description>这是一个名为 A 的选项</template>
    </Checkbox>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Checkbox } from '@xhs/delight'
</script>
```

##    展开描述

通过 `expand` 设置 `Checkbox` 展开描述，展开后描述会通过换行的方式完整展示：

```vue
<template>
  <Checkbox
    value="option A"
    label="选项 A"
    description="这是一个名为 A 的选项"
    expand
  />
</template>

<script setup lang="ts">
  import { Checkbox } from '@xhs/delight'
</script>
```

##    选中状态

通过 `checked` 设置选中状态：

```vue
<template>
  <Space direction="vertical" align="start">
    <Checkbox
      v-model:checked="checked"
      value="option A"
      label="选项 A"
      description="这是一个名为 A 的选项"
    />
    <Text>当前选中状态：{{ checked }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Checkbox, Text } from '@xhs/delight'

  const checked = ref(false)
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <Checkbox
    value="option A"
    label="选项 A"
    description="这是一个名为 A 的选项"
    block
  />
</template>

<script setup lang="ts">
  import { Checkbox } from '@xhs/delight'
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <Checkbox
    value="option A"
    label="选项 A"
    description="这是一个名为 A 的选项"
    disabled
  />
</template>

<script setup lang="ts">
  import { Checkbox } from '@xhs/delight'
</script>
```

##    复选框组合

通过将 `Checkbox` 插入 `CheckboxGroup` 或传入 `CheckboxGroup` 的 `options` 设置复选框组合：
 - `Checkbox` 或 `options` 中属性的优先级比 `CheckboxGroup` 更高

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup v-model="value1">
      <Checkbox
        value="option A"
        label="选项 A"
      />
      <Checkbox
        value="option B"
        label="选项 B"
        checked
      />
      <Checkbox
        value="option C"
        label="选项 C"
        disabled
      />
    </CheckboxGroup>
    <Text>当前选中项：{{ value1 }}</Text>
    <CheckboxGroup v-model="value2" :options="options"/>
    <Text>当前选中项：{{ value2 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const value1 = ref(['option A'])
  const value2 = ref(['option B'])

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    复选框组合的 name

通过 `name` 设置复选框组合下所有 input[type="checkbox"] 的 name 属性：

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup v-model="value" :options="options" name="form"/>
    <Text>当前选中项：{{ value }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const value = ref(['option A'])

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    复选框组合的排版方向

通过 `direction` 设置复选框组合的排版方向，当纵向展示时默认将 `description` 展开，除非显示声明：

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup v-model="value1" direction="vertical">
      <Checkbox
        value="option A"
      />
      <Checkbox
        value="option B"
        label="选项 B"
        description="这是一个名为 B 的选项"
        checked
      />
      <Checkbox
        value="option C"
        label="选项 C"
        description="这是一个名为 C 的选项"
        :expand="false"
        disabled
      />
    </CheckboxGroup>
    <Text>当前选中项：{{ value1 }}</Text>
    <CheckboxGroup v-model="value2" :options="options" direction="vertical"/>
    <Text>当前选中项：{{ value2 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const value1 = ref(['option A'])
  const value2 = ref(['option B'])

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    复选框组合块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <CheckboxGroup v-model="value" :options="options" block/>
  <Text>当前选中项：{{ value }}</Text>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const value = ref(['option A'])

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    必填

通过 `required` 设置为必填项，默认在失焦 / 点击 / 手动校验后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup ref="checkbox" v-model="value" :options="options" required/>
    <Text>当前校验状态：{{ checkbox?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const checkbox = ref()
  const value = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则：

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup ref="checkbox" v-model="value" :options="options" :validate="validate"/>
    <Text>当前校验状态：{{ checkbox?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const checkbox = ref()
  const value = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]

  function validate({ modelValue, fullValue }) {
    return fullValue.some(x => x.value === 'option B')
  }
</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup ref="checkbox" v-model="value" :options="options" required validate-timing="immediate"/>
    <Text>当前校验状态：{{ checkbox?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const checkbox = ref()
  const value = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup ref="checkbox" v-model="value" :options="options" required validate-timing="manual"/>
    <Text>当前校验状态：{{ checkbox?.status }}</Text>
    <Button type="primary" @click="manualValidate">result：{{ result }}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text, Button } from '@xhs/delight'

  const checkbox = ref()
  const value = ref()
  const result = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]

  function manualValidate() {
    checkbox.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }
</script>
```

##    复选框组合全局禁用

通过 `disabled` 设置复选框组合全局禁用：

```vue
<template>
  <Space direction="vertical" align="start">
    <CheckboxGroup v-model="value" :options="options" disabled/>
    <Text>当前选中项：{{ value }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const value = ref(['option A'])

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    指示器

将用作指示器的 `Checkbox` 的 `template ref` 赋值给需要被收集状态的 `Checkbox` 或 `CheckboxGroup` 的 `indicator`：
 - 指示器会展示这些 `Checkbox` 的状态，除了 `选中 、 `未选中 还有 `部分选中 共三个状态
 - 指示器点击时会 `全部选中 、 `全部取消选中 这些 `Checkbox`
 - 指示器会通过 `modelValue` 、 `onUpdate:modelValue` 、`onChange` 控制、响应子选项的变化，并不再具备 `Checkbox` 本身的功能，即 `value` 无效

```vue
<template>
  <Space direction="vertical" align="start">
    <Checkbox
      v-model="value1"
      ref="indicator1"
      label="根节点"
      @change="v => handleChange('root: ', v)"
    />
    <Text>当前选中项：{{ value1 }}</Text>
    <Checkbox
      v-model="value2"
      ref="indicator2"
      label="一级节点"
      :indicator="indicator1"
      @change="v => handleChange('level 1: ', v)"
      style="margin-left: 32px"
    />
    <Text style="margin-left: 32px">当前选中项：{{ value2 }}</Text>
    <CheckboxGroup
      v-model="value3"
      :options="options"
      :indicator="indicator2"
      @change="v => handleChange('level 1 checkbox group: ', v)"
      style="margin-left: 64px"
    />
    <Text style="margin-left: 64px">当前选中项：{{ value3 }}</Text>
    <Checkbox label="选项 D" value="option D" checked :indicator="indicator2" style="margin-left: 64px"/>
    <Checkbox label="选项 E" value="option E" checked :indicator="indicator2" style="margin-left: 64px"/>
    <Checkbox label="选项 F" value="option F" :indicator="indicator2" style="margin-left: 64px"/>
    <Checkbox label="选项 G" value="option G" checked :indicator="indicator1" style="margin-left: 32px"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, CheckboxGroup, Checkbox, Text } from '@xhs/delight'

  const indicator1 = ref()
  const indicator2 = ref()
  const value1 = ref(['option B'])
  const value2 = ref()
  const value3 = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      checked: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
    },
  ]

  function handleChange(...args) {
    console.log(...args)
  }
</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素：

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 CheckboxGroup 的静态描述文本"
      on-error="这是一段 CheckboxGroup 的静态错误提示"
    >
      <CheckboxGroup :options="options" required :validate="validate"/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :onError="onError"
    >
      <CheckboxGroup :options="options" required :validate="validate"/>
    </FormItem>
    <FormItem name="3">
      <CheckboxGroup :options="options" required :validate="validate"/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue, fullValue }">当前输入内容为：{{ fullValue?.map(v => v.label).join() }}</template>
      <template v-slot:on-error="{ modelValue, fullValue }">{{
        modelValue !== undefined
          ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，必须选择选项 B'
          :'必填项'
      }}</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { CheckboxGroup, Form, FormItem } from '@xhs/delight'

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      checked: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
    },
  ]

  function description({ modelValue, fullValue }) {
    return '当前输入内容为：' + fullValue?.map(v => v.label).join()
  }

  function onError({ modelValue, fullValue }) {
    return  modelValue !== undefined
        ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，必须选择选项 B'
        :'必填项'
  }

  function validate({ modelValue, fullValue }) {
    return fullValue.some(x => x.value === 'option B')
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    复选框按钮

复选框和按钮组合使用的案例：

```vue
<template>
  <Button @click="checked = !checked">
    <Checkbox
      v-model:checked="checked" 
      value="option A"
      @click.stop 
    >
      选项 A
    </Checkbox>
  </Button>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Checkbox, Button } from '@xhs/delight'

  const checked = ref(false)
</script>
```

##    API 参考

通过设置 Checkbox 的属性来产生不同的复选框样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|value|复选框的值|string &#124; number|-|
|label|复选框的标题|string|-|
|description|复选框的描述|string|-|
|expand|复选框的描述是否展开展示|boolean|-|
|checked (v-model)|复选框的选中状态|boolean|false|
|indeterminate|复选框的半选状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|block|展示为块级元素|boolean|false|
|indicator|指示器|template ref|-|
|extra|可配置的额外属性，会随 `onChange` 事件返回|object|-|
|id|复选框的唯一键（复选框中使用了 `label` + `name` + `value` 作为复选框的默认唯一键，但当可能存在重复时可以额外声明 `id` 来保证内容发生变化时 `dom` 被正确更新）|string|-|
|disabled|禁用|boolean|false|

### CHECKBOX 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|check-change|复选框组合中选项状态变化的回调事件|Function|-|

### CHECKBOX 插槽 |插槽|说明|
| :- | :- |
|default|复选框的标题|
|description|复选框的描述|

### CHECKBOXGROUP API 参考
```

interface AllCheckboxProps {
  value?: string | number | boolean
  label?: string
  description?: string
  checked?: boolean
  autofocus?: boolean
  block?: boolean
  extra?: any
  disabled?: boolean
  'onUpdate:checked'?: (e: boolean) => void
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
  onFocus?: (e: FocusEvent) => void
  onBlur?: (e: FocusEvent) => void
}

```

通过设置 Checkboxgroup 的属性来产生不同的复选框组合样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|复选框组合选中项的 value|(string &#124; number)[]|-|
|name|复选框组合下所有 input[type="checkbox"] 的 name 属性|string|-|
|direction|复选框组合的排版方向（当纵向展示时默认将 `description` 展开，除非显示声明）|'horizontal' &#124; 'vertical'，|'horizontal'|
|options|复选框组合中选项的属性|AllCheckboxProps[]|-|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则|(args: &#123; modelValue?: (string &#124; number)[]; fullValue: AllCheckboxProps[] &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|block|展示为块级元素|boolean|false|
|indicator|指示器|template ref|-|
|disabled|禁用|boolean|false|

### CHECKBOXGROUP 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|复选框组合中选项状态变化的回调事件|(e: AllCheckboxProps[]) => void|-|
|check-change|复选框组合中选项状态变化的回调事件|Function|-|

### CHECKBOXGROUP 插槽 |插槽|说明|
| :- | :- |
|default|复选框组合中的选项，只支持 `Checkbox`|

### CHECKBOXGROUP TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|