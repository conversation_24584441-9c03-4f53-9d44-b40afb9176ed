

##    基本使用

通过 `TimePicker` 选择单个时间，通过 `TimeRangePicker` 选择时间区间：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value"/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimePicker v-model="value" autofocus/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimePicker v-model="value" selectOnly/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimePicker v-model="value" scroll-select/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue"/>
    <Text>当前选中时间：{{ rangeValue }}</Text>
    <TimeRangePicker v-model="rangeValue" select-only/>
    <Text>当前选中时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value = ref()
  const rangeValue = ref()
</script>
```

##    placeholder

通过 `placeholder` 设置提示文本：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value" placeholder="请输入时间"/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue" :placeholder="{ start: '请输入开始时间', end: '请输入结束时间' }"/>
    <Text>当前选中时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value = ref()
  const rangeValue = ref()
</script>
```

##    前缀

通过 `prefix` 或 `slots.prefix` 设置前缀：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value"  prefix="时间"/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimePicker v-model="value">
      <template #prefix>
        <Icon :icon="Alarm"/>
      </template>
    </TimePicker>
    <Text>当前选中时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue" prefix="时间"/>
    <Text>当前选中时间：{{ rangeValue }}</Text>
    <TimeRangePicker v-model="rangeValue">
      <template #prefix>
        <Icon :icon="Alarm"/>
      </template>
    </TimeRangePicker>
    <Text>当前选中时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text, Icon } from '@xhs/delight'
  import { Alarm } from '@xhs/delight/icons'

  const value = ref()
  const rangeValue = ref()
</script>
```

##    后缀

通过 `suffix` 或 `slots.suffix` 设置后缀：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value"  suffix="时间"/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimePicker v-model="value">
      <template #suffix>
        <Icon :icon="Alarm"/>
      </template>
    </TimePicker>
    <Text>当前选中时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue" suffix="时间"/>
    <Text>当前选中时间：{{ rangeValue }}</Text>
    <TimeRangePicker v-model="rangeValue">
      <template #suffix>
        <Icon :icon="Alarm"/>
      </template>
    </TimeRangePicker>
    <Text>当前选中时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text, Icon } from '@xhs/delight'
  import { Alarm } from '@xhs/delight/icons'

  const value = ref()
  const rangeValue = ref()
</script>
```

##    清除输入

通过 `clearable` 开启清除输入功能：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value" clearable/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue" clearable/>
    <Text>当前选中时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value = ref()
  const rangeValue = ref()
</script>
```

##    必填

通过 `required` 设置为必填项：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value" required/>
    <Text>当前选中时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue" required/>
    <Text>当前选中时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value = ref()
  const rangeValue = ref()
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value" :validate="validate"/>
    <Text>必须选择 10 点之后的时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue" :validate="rangeValidate"/>
    <Text>开始时间必须选择 10 点之后的时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value = ref('09:10:00')
  const rangeValue = ref({ start: '09:10:00' })

  function validate({ fullValue }) {
    return fullValue?.hour() >= 10
  }
  function rangeValidate({ fullValue }) {
    return fullValue?.start?.hour() >= 10
  }
</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value" :validate="validate" validate-timing="immediate"/>
    <Text>必须选择 10 点之后的时间：{{ value }}</Text>
    <TimeRangePicker v-model="rangeValue" :validate="rangeValidate" validate-timing="immediate"/>
    <Text>开始时间必须选择 10 点之后的时间：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value = ref('09:10:00')
  const rangeValue = ref({ start: '09:10:00' })

  function validate({ fullValue }) {
    return fullValue?.hour() >= 10
  }
  function rangeValidate({ fullValue }) {
    return fullValue?.start?.hour() >= 10
  }
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker ref="time" v-model="value" :validate="validate" validate-timing="manual"/>
    <Button type="primary" @click="manualValidate">必须选择 10 点之后的时间：{{result}}</Button>
    <TimeRangePicker ref="timeRange" v-model="rangeValue" :validate="rangeValidate" validate-timing="manual"/>
    <Button type="primary" @click="manualRangeValidate">开始时间必须选择 10 点之后的时间：{{rangeResult}}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Button } from '@xhs/delight'

  const value = ref('09:10:00')
  const rangeValue = ref({ start: '09:10:00' })

  const time = ref()
  const result = ref()
  const timeRange = ref()
  const rangeResult = ref()

  function validate({ fullValue }) {
    return fullValue?.hour() >= 10
  }
  function rangeValidate({ fullValue }) {
    return fullValue?.start?.hour() >= 10
  }

  function manualValidate() {
    time.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }

  function manualRangeValidate() {
    timeRange.value
      ?.validate()
      .then(
        res => {
          rangeResult.value = res
        }
      )
  }
</script>
```

##    单位

通过 `unit` 设置单位，可以设置小时： `hour` 、分钟： `minute` 、秒： `second` ，默认 `second`：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value1" unit="hour"/>
    <Text>当前选中时间：{{ value1 }}</Text>
    <TimePicker v-model="value2" unit="minute"/>
    <Text>当前选中时间：{{ value2 }}</Text>
    <TimePicker v-model="value3"/>
    <Text>当前选中时间：{{ value3 }}</Text>
    <TimeRangePicker v-model="rangeValue1" unit="hour"/>
    <Text>当前选中时间：{{ rangeValue1 }}</Text>
    <TimeRangePicker v-model="rangeValue2" unit="minute"/>
    <Text>当前选中时间：{{ rangeValue2 }}</Text>
    <TimeRangePicker v-model="rangeValue3"/>
    <Text>当前选中时间：{{ rangeValue3 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value1 = ref()
  const value2 = ref()
  const value3 = ref()
  const rangeValue1 = ref()
  const rangeValue2 = ref()
  const rangeValue3 = ref()
</script>
```

##    格式

通过 `format` 设置数据格式，默认 `HH:mm:ss`（根据 unit 截取掉多余部分）：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value1" format="HH mm ss"/>
    <Text>当前选中时间：{{ value1 }}</Text>
    <TimePicker v-model="value2" format="HH,mm,ss"/>
    <Text>当前选中时间：{{ value2 }}</Text>
    <TimePicker v-model="value3"/>
    <Text>当前选中时间：{{ value3 }}</Text>
    <TimeRangePicker v-model="rangeValue1" format="HH mm ss"/>
    <Text>当前选中时间：{{ rangeValue1 }}</Text>
    <TimeRangePicker v-model="rangeValue2" format="HH,mm,ss"/>
    <Text>当前选中时间：{{ rangeValue2 }}</Text>
    <TimeRangePicker v-model="rangeValue3"/>
    <Text>当前选中时间：{{ rangeValue3 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker, Text } from '@xhs/delight'

  const value1 = ref()
  const value2 = ref()
  const value3 = ref()
  const rangeValue1 = ref()
  const rangeValue2 = ref()
  const rangeValue3 = ref()
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <TimePicker block/>
  <TimeRangePicker class="--space-m-top-default" block/>
</template>

<script setup lang="ts">
  import { TimePicker, TimeRangePicker } from '@xhs/delight'
</script>
```

##    只读

通过 `readonly` 设置只读：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker v-model="value" readonly clearable/>
    <TimeRangePicker v-model="rangeValue" readonly clearable/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TimePicker, TimeRangePicker } from '@xhs/delight'

  const value = ref('09:10:00')
  const rangeValue = ref({ start: '09:10:00',end: '09:10:55' })
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker disabled/>
    <TimeRangePicker disabled/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, TimePicker, TimeRangePicker } from '@xhs/delight'
</script>
```

##    禁用某个时间段

通过 `disabledTime` 设置禁用：

```vue
<template>
  <Space direction="vertical" align="start">
    <TimePicker :disabled-time="disabledTime" />
    <TimeRangePicker :disabled-time="disabledRangeTime" />
  </Space>
</template>

<script setup>
  import { Space, TimePicker, TimeRangePicker } from '@xhs/delight'

  const disabledTime = () => ({
    disabledHours: () => [2, 3],
    disabledMinutes: () => [4, 5],
    disabledSeconds: () => [6, 7]
  })

  const disabledRangeTime = (now, type) => {
    if (type === 'start') {
      return {
        disabledHours: () => [1, 2],
        disabledMinutes: () => [1, 2],
        disabledSeconds: () => [1, 2]
      }
    }

    return {
      disabledHours: () => [3, 4],
      disabledMinutes: () => [3, 4],
      disabledSeconds: () => [3, 4]
    }
  }
</script>
```

##    TimePicker API 参考

通过设置 TimePicker 的属性来产生不同的时间选择框样式：

```
type DisabledTime = (now: Dayjs, type?: 'start' | 'end') => {
  disabledHours?: () => number[]
  disabledMinutes?: (selectedHour?: number) => number[]
  disabledSeconds?: (selectedHour?: number, selectedMinute?: number) => number[]
}
```

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|时间选择框的选中值|string|-|
|unit|时间选择框的最小单位|'hour' &#124; 'minute' &#124; 'second'|-|
|format|时间选择框的模版|string|'HH:mm:ss'（根据 unit 截取掉多余部分）|
|placeholder|时间选择框的提示文本|string|-|
|prefix|时间选择框的前缀内容|string|-|
|suffix|时间选择框的后缀内容|string|-|
|clearable|开启清除选择内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则|(args: &#123; modelValue?: string; fullValue: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation) &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|block|展示为块级元素|boolean|false|
|readonly|只读|boolean|false|
|disabled|禁用|boolean|false|
|disabledTime|不可选择的时间|DisabledTime|-|
|selectOnly|仅允许通过选择的方式选取时间|boolean|false|
|scrollSelect|开启滚动选择|boolean|false|
|popoverClass|时间选择框中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|popoverStyle|时间选择框中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|

### TimePicker 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|时间选择框选中值变化的回调事件|(e: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation)) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|

### TimePicker 插槽 |插槽|说明|
| :- | :- |
|prefix|时间选择框的前缀内容|
|suffix|时间选择框的后缀内容|

### TimePicker TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|

##    TimeRangePicker API 参考

通过设置 TimeRangePicker 的属性来产生不同的时间范围选择框样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|时间范围选择框的选中值|&#123; start?: string; end?: string; &#125;|-|
|unit|时间范围选择框的最小单位|'hour' &#124; 'minute' &#124; 'second'|-|
|format|时间范围选择框的模版|string|'HH:mm:ss'（根据 unit 截取掉多余部分）|
|placeholder|时间范围选择框的提示文本|&#123; start?: string; end?: string; &#125;|-|
|prefix|时间范围选择框的前缀内容|string|-|
|suffix|时间范围选择框的后缀内容|string|-|
|clearable|开启清除选择内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则|(args: &#123; modelValue?: &#123; start?: string; end?: string; &#125;; fullValue: &#123; start?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); end?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); &#125; &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|block|展示为块级元素|boolean|false|
|readonly|只读|boolean|false|
|disabled|禁用|boolean|false|
|disabledTime|不可选择的时间|DisabledTime|-|
|selectOnly|仅允许通过选择的方式选取时间范围|boolean|false|
|scrollSelect|开启滚动选择|boolean|false|
|popoverClass|时间范围选择框中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|popoverStyle|时间范围选择框中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|

### TimeRangePicker 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|时间范围选择框选中值变化的回调事件|(e: &#123; start?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); end?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); &#125;) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input:start|开始时间输入的回调事件|(e: Event) => void|-|
|focus:start|开始时间获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur:start|开始时间失去焦点的回调事件|(e: FocusEvent) => void|-|
|input:end|结束时间输入的回调事件|(e: Event) => void|-|
|focus:end|结束时间获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur:end|结束时间失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|

### TimeRangePicker 插槽 |插槽|说明|
| :- | :- |
|prefix|时间范围选择框的前缀内容|
|suffix|时间范围选择框的后缀内容|

### TimeRangePicker TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|