

## 何时使用 文件夹、组织架构、生物分类、国家地区等等，世间万物的大多数结构都是树形结构。使用 `树控件` 可以完整展现其中的层级关系，并具有展开收起选择等交互功能。

##    基本使用

基础的树形结构展示
```vue
<template>
  <div style="width: 300px">
    <Tree
      :tree-data="treeData"
      @select="handleSelect"
      v-model:selected-keys="selectedKeys"
      v-model:expanded-keys="expandedKeys"
    />
  </div>
</template>

<script setup>
import { h, ref, watch } from "vue";
import { Tree, Text, Icon } from "@xhs/delight"
import { MessageEmoji } from '@xhs/delight/icons'

const checkedKeys = ref(['0-0-1'])
const selectedKeys = ref([])
const expandedKeys = ref([])

const treeData = ref([
  {
    title: '0-0',
    key: '0-0',
    children: [
      {
        title: '0-0-0',
        key: '0-0-0',
        children: [
          {
            title: '0-0-0-0',
            key: '0-0-0-0',
            isLeaf: true,
          },
          {
            title: '0-0-0-0',
            key: '0-0-0-1',
            isLeaf: true,
          },
        ],
      },
      {
        title: '0-0-1',
        key: '0-0-1',
        children: [
          {
            title: '0-0-1-0',
            key: '0-0-1-0',
            isLeaf: true,
          }
        ],
      },
    ]
  },
]);

const handleSelect = (keys) => {
  console.log('tree selected keys changed: ', keys)
}
```

##    可选择

可以勾选、禁用、选中，默认展开
```vue
<template>
  <div style="width: 300px">
    {{checkedKeys}}
    <Tree
      :tree-data="treeData"
      :checkable="true"
      :default-expand-all="true"
      @check="handleCheck"
      @select="handleSelect"
      @expand="handleExpand"
      v-model:checkedKeys="checkedKeys"
    />
  </div>
  <br/>
  <Button type='primary' @click="handle">初始化</Button>
</template>

<script setup>
import { h, ref, watch } from "vue";
import { Tree, Text, Icon, Button } from "@xhs/delight"
import { MessageEmoji } from '@xhs/delight/icons'

const checkedKeys = ref(['0-0-1'])
const handle = () => {
    checkedKeys.value = ['0-0-0-1']
}

const treeData = ref([
  {
    title: 'parent 1',
    key: '0-0',
    children: [
      {
        title: 'parent 1-0',
        key: '0-0-0',
        selectable: false,
        children: [
          {
            title: 'leaf10-0-0-00-0-0-00-0-0-00-0-0-00-0-0-00-0-0-0',
            key: '0-0-0-0',
            isLeaf: true,
            disabled: true,
          },
          {
            title: 'leaf2',
            key: '0-0-0-1',
            isLeaf: true,
          },
          {
            title: 'leaf3',
            key: '0-0-0-2',
            isLeaf: true,
          },
          {
            title: 'leaf4',
            key: '0-0-0-3',
            children: [
              {
                title: 'test',
                key: '0-0-0-3-1',
                isLeaf: true,
              },
            ]
          },
        ],
      },
      {
        title: 'parent 1-1',
        key: '0-0-1',
        children: [
          {
            title: 'leaf',
            key: '0-0-1-0',
            isLeaf: true,
          }
        ],
      },
    ]
  },
  {
    title: 'parent 2',
    key: '0-1',
  },
]);

const handleCheck = (keys) => {
  console.log('tree check changed: ', keys)
}

const handleSelect = (keys) => {
  console.log('tree selected keys changed: ', keys)
}

const handleExpand = (keys) => {
  console.log('tree expanded keys changed: ', keys)
}

watch(() => checkedKeys.value, (_checkedKeys) => {
  console.log('_checkedKeys: ', _checkedKeys)
})
```

##    严格选择模式

`设置checkStrictly=false 父子节点选中状态不再关联`
```vue
<template>
  <Tree
    :tree-data="treeData"
    :checkable="true"
    :default-expand-all="false"
    :check-strictly="true"
    @check="handleCheck"
    @select="handleSelect"
  />
</template>

<script setup>
import { h, ref } from "vue";
import { Tree, Text, Icon } from "@xhs/delight"
import { MessageEmoji } from '@xhs/delight/icons'
const treeData = ref([
  {
    title: 'parent 1',
    key: '0-0',
    children: [
      {
        title: 'parent 1-0',
        key: '0-0-0',
        children: [
          {
            title: 'leaf',
            key: '0-0-0-0',
            isLeaf: true,
          },
          {
            title: 'leaf',
            key: '0-0-0-1',
            isLeaf: true,
          },
        ],
      },
      {
        title: 'parent 1-1',
        key: '0-0-1',
        children: [
          {
            title: 'leaf',
            key: '0-0-1-0',
            isLeaf: true,
          }
        ],
      },
    ]
  },
]);

const handleCheck = (keys) => {
  console.log('tree check changed: ', keys)
}

const handleSelect = (keys) => {
  console.log('tree selected keys changed: ', keys)
}
```

##    懒加载

设置 loadData 使用懒加载

```vue
<template>
  <Tree
    :tree-data="treeData"
    :checkable="true"
    :default-expand-all="false"
    :load-data="loadData"
    @check="handleCheck"
    @select="handleSelect"
  />
</template>

<script setup lang="ts">
import { h, ref } from "vue";
import { Tree } from "@xhs/delight";
const treeData = ref([
  {
    title: 'parent 1',
    key: '0-0',
  },
]);

const handleCheck = (keys) => {
  console.log('tree check changed: ', keys)
}

const handleSelect = (keys) => {
  console.log('tree selected keys changed: ', keys)
}

const loadData = (treeNode) => {
  return new Promise((resolve) => {
    if (treeNode.children && treeNode.children.length > 0) {
      resolve()
      return
    }

    setTimeout(() => {
      treeNode.children = [{
        title: 'node ' + treeNode.key + '-0',
        key: treeNode.key + '-0',
      },
      {
        title: 'node ' + treeNode.key + '-1',
        key: treeNode.key + '-1',
      }]
      resolve()
    }, 2000)
  })
}
```

## 自定义 slots

可设置 title 和 icon 自定义 slots, slots内可以访问当当前节点的数据 treeNode

```vue
<template>
  <Tree
    :tree-data="treeData"
    :checkable="true"
    :default-expand-all="false"
    @check="handleCheck"
    @select="handleSelect"
  >
    <template #title="{ treeNode }">
      <Text link>
        {{ treeNode.title }}
      </Text>
    </template>
    <template #icon>
      <Icon
        :icon="MessageEmoji"
      />
    </template>
  </Tree>
</template>

<script setup>
import { h, ref } from "vue";
import { Tree, Text, Icon } from "@xhs/delight"
import { MessageEmoji } from '@xhs/delight/icons'

const treeData = ref([
  {
    title: 'parent 1',
    key: '0-0',
    children: [
      {
        title: 'parent 1-0',
        key: '0-0-0',
        children: [
          {
            title: 'leaf',
            key: '0-0-0-0',
            isLeaf: true,
          },
          {
            title: 'leaf',
            key: '0-0-0-1',
            isLeaf: true,
          },
        ],
      },
      {
        title: 'parent 1-1',
        key: '0-0-1',
        children: [
          {
            title: 'leaf',
            key: '0-0-1-0',
            isLeaf: true,
          }
        ],
      },
    ]
  },
]);

const handleCheck = (keys) => {
  console.log('tree check changed: ', keys)
}

const handleSelect = (keys) => {
  console.log('tree selected keys changed: ', keys)
}
```

##    拖拽

设置 draggable = true 即可开启拖拽

```vue
<template>
  <div style="width: 300px">
    <Tree
      draggable
      :treeData="treeData"
      v-model:expandedKeys="expandedKeys"
      @node-drop="handleNodeDrop"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Tree, Text, Icon } from "@xhs/delight"

function getTreeData(opts, name = '节点', start = 0) {
  const data = []
  const count = opts[start]
  for (let i = 1; i <= count; i++) {
    const isLeaf = opts.length === start + 1
    const title = name + i
    const node = {
      title,
      key: title,
    }
    if (isLeaf) {
      node.isLeaf = true
    } else {
      node.children = getTreeData(opts, title + '-', start + 1)
    }
    data.push(node)
  }
  return data
}

const expandedKeys = ref(['节点1', '节点2', '节点3'])
const treeData = ref(getTreeData([3, 2, 2]))

const handleNodeDrop = (draggingNode, dropNode, dropType, event) => {
  console.log(draggingNode, dropNode, dropType)
}

</script>
```

设置 beforeNodeDrop，可根据业务逻辑，判断是否要完成拖拽

```vue
<template>
  <div style="width: 300px">
    <Tree
      draggable
      :treeData="treeData"
      :beforeNodeDrop="beforeNodeDrop"
      v-model:expandedKeys="expandedKeys"
      @node-drop="handleNodeDrop"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Tree, Text, Icon } from "@xhs/delight"

function getTreeData(opts, name = '节点', start = 0) {
  const data = []
  const count = opts[start]
  for (let i = 1; i <= count; i++) {
    const isLeaf = opts.length === start + 1
    const title = name + i
    const node = {
      title,
      key: title,
    }
    if (isLeaf) {
      node.isLeaf = true
    } else {
      node.children = getTreeData(opts, title + '-', start + 1)
    }
    data.push(node)
  }
  return data
}

const expandedKeys = ref(['节点1', '节点2', '节点3'])
const treeData = ref(getTreeData([3, 2, 2]))

const handleNodeDrop = (draggingNode, dropNode, dropType, event) => {
  console.log("handle-node-drop")
  console.log(draggingNode, dropNode, dropType, event)
}

const beforeNodeDrop = (draggingNode, dropNode, dropType, event) => {
  console.log("before-node-drop")
  console.log(draggingNode, dropNode, dropType, event)
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log('不允许拖拽')
      reject('error')
    }, 500)
  })
}

</script>
```

##    API 参考

#### Tree 属性 | 属性            | 说明  | 类型       | 默认值  |
| :-------------- | :----------------------------- | :--------- | :-------------------------------------- |
| treeData | 树形组件可嵌套结构数据 | TreeNode[] | []
| checkable | 节点前添加 Checkbox 复选框 | boolean | false
| selectable | 树节点是否可被选中 | boolean | true
| checkStrictly | checkable 状态下节点选择完全受控（父子节点选中状态不再关联） | boolean | false
| draggable | 设置节点可拖拽 | boolean | false
| defaultExpandAll |默认展开所有树节点, 如果是异步数据，需要在数据返回后再实例化，建议用 v-if="data.length"；当有 expandedKeys 时，defaultExpandAll 将失效 | boolean | false
| checkedKeys | 选中复选框的树节点 | (string \| number)[] | []
| selectedKeys | 设置选中的树节点 | (string \| number)[] | []
| expandedKeys | 展开指定的树节点 | (string \| number)[] | []
|revertable|单选的情况下，是否支持反选取消|boolean|true|
| loadData | 异步加载数据 | function(treeNode: ITreeNode): Promise\ | —
|expandOnClickNode|是否在点击节点的时候展开或者收缩节点，默认 false 的时候，只能点击箭头图标才能展开和收缩|boolean|false|
|beforeNodeDrop| 拖拽完成前触发，返回false或者reject会终止拖拽动作 | (draggingNode:INode, dropNode:INode, dropType: 'before' \| 'inner' \| 'after', event:DragEvent) => (Promise | boolean) | 

### Tree 插槽 |插槽|说明|
| :- | :- |
|title|自定义Tree标题|
|icon|自定义icon|

#### Events 事件 | Event 名称            | 描述  | 参数       |
| :--------------| :--------| :------|
| checke| 勾选变化事件 | function(keys: string[] \| number[]) |
| select | 选中事件 | function(keys: string[] \| number[]) |
| expand | 展开/收起节点时触发 | function(keys: string[] \| number[]) |
| node-drag-start | 节点开始拖拽时触发 | function(node:INode, event:DragEvent) |
| node-drag-enter | 拖拽进入其他节点时触发 | function(draggingNode:INode, enterDropNode:INode, event:DragEvent) |
| node-drag-leave | 拖拽离开某个节点时触发 | function(draggingNode:INode, levelDropNode:INode, event:DragEvent) |
| node-drag-over | 在拖拽节点时触发 | function(draggingNode:INode, dropNode:INode, event:DragEvent) |
| node-drag-end | 拖拽结束时（可能未成功）触发 | function(draggingNode:INode, dropNode:INode \| null, dropType: 'before' \| 'inner' \| 'after' \| 'none', event:DragEvent) |
| node-drop | 拖拽成功完成时触发 | function(draggingNode:INode, dropNode:INode, dropType: 'before' \| 'inner' \| 'after', event:DragEvent) |

#### TreeNode 属性 | 属性            | 说明  | 类型       | 默认值  |
| :-------------- | :----------------------------- | :--------- | :-------------------------------------- |
| title | 标题 | string | -
| key | 必填,唯一标识树节点 | string \| number | -
| isLeaf | 可填, 是否是叶子节点 | boolean | -
| children | 孩子节点 | TreeNode[] | -
| selectable | 节点是否可被选中 | boolean | -
| checkable | 当树为 checkable 时，设置独立节点是否展示 Checkbox | boolean | -
| disabled | 禁掉响应 | boolean | -
| disableCheckbox | 禁掉 checkbox| boolean | -
| icon(开发中) | 自定义TreeNode icon，可选 | (e: number) => void | -

#### INode 属性 该属性是基于 TreeNode 结构进行扩展的，扩展的属性如下：
| 属性            | 说明  | 类型       | 默认值  |
| :-------------- | :----------------------------- | :--------- | :-------------------------------------- |
| parent | 节点的父节点的 key 属性 | string \| number | - |
| parentNode | 节点的父节点 | INode | - |
| children | 节点的父节点 | INode[] | - |