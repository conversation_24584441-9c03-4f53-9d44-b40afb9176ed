

##    基本使用

```vue
<template>
  <Button type="primary" @click="notify">Open the notification box</Button>
</template>

<script setup>
  import { Button, notification2 as notification } from '@xhs/delight'

  const notify = () => {
    notification.open({
      title: 'Notification titile',
      description: '这是一段描述信息。',
    })
  }
</script>
```

##    自动关闭的延时

自定义通知框自动关闭的延时，默认 3s，取消自动关闭只要将该值设为 0 即可。

```vue
<template>
  <Button type="primary" @click="notify">Open the notification box</Button>
</template>

<script setup>
  import { Button, notification2 as notification, Icon } from '@xhs/delight'

  const notify = () => {
    notification.success({
      title: 'Notification titile',
      description: '这是一段描述信息。',
      duration: 0,
      closeable: true,
    })
  }
</script>
```

##    提示类型

通过 `type` 设置提示类型：
- `type` 支持 `info` 、 `success` 、 `warning` 、 `danger` 共 `4` 个类型，默认 `undefined`

```vue
<template>
  <Space>
    <Button type="primary" @click="() => notify('success')">success</Button>
    <Button type="primary" @click="() => notify('info')">info</Button>
    <Button type="primary" @click="() => notify('warning')">warning</Button>
    <Button type="primary" @click="() => notify('danger')">danger</Button>
  </Space>
</template>

<script setup>
  import { Space, Button, notification2 as notification } from '@xhs/delight'

  function notify(type) {
    notification[type]({
      title: type || 'default',
      description: '这是一段描述信息。',
    })
  }
</script>
```

##    自定义图标

```vue
<template>
  <Button type="primary" @click="notify">custom icon</Button>
</template>

<script setup>
  import { h } from 'vue'
  import { Button, Icon, notification2 as notification } from '@xhs/delight'
  import { AstonishedFace } from '@xhs/delight/icons'

  function notify() {
    notification.open({
      title: 'Notification titile',
      description: '这是一段描述信息。',
      icon: h(Icon, { icon: AstonishedFace, color: 'primary' })
    })
  }
</script>
```

##    自定义标题与描述

通过给 `title` 、 `description` 传入 `VNode` 或 `JSX.Element` 自定义标题与描述：

```vue
<template>
  <Button type="primary" @click="notify">Open the notification box</Button>
</template>

<script setup>
  import { h } from 'vue'
  import { Button, Text, notification2 as notification } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'

  function notify() {
    notification.open(
      {
        title: h(
          Text,
          {
            icon: {
              icon: Fire,
              theme: 'filled',
              color: 'red-6'
            },
            type: 'h5',
            bold: true,
          },
          () => '标题'
        ),
        description: h(
          Text,
          { size: 'small' },
          () => '这是一段描述信息。'
        ),
      }
    )
  }
</script>
```

##    自定义样式

```vue
<template>
  <Button type="primary" @click="notify">custom style</Button>
</template>

<script setup>
  import { h } from 'vue'
  import { Button, Icon, notification2 as notification } from '@xhs/delight'

  function notify() {
    notification.open({
      title: 'Notification titile',
      description: '这是一段描述信息。',
      class: 'my-custom-notify',
      style: { width: '600px' }
    })
  }
</script>

<style>
  .d-new-notification .my-custom-notify {
    background-color: pink;
  }
</style>
```

##    额外操作

```vue
<template>
  <Button type="primary" @click="notify">actions</Button>
</template>

<script setup lang="ts">
  import { Button, notification2 as notification } from '@xhs/delight'


  function notify() {
    const key = 'close-key'

    notification.danger(
      {
        key,
        title: '出错啦',
        description: '点击重试再试一次。',
        actions: () => [
          {
            name: '重试',
            onClick: () => { console.log('重试咯...') },
          },
          {
            name: '关闭',
            onClick: () => { notification.close(key) },
          },
        ],
        duration: 0,
      }
    )
  }
</script>
```

##    自定义额外操作

通过给 `actions` 传入 `JSX.Element` 自定义额外操作：

```vue
<template>
  <Button type="primary" @click="notify">actions</Button>
</template>

<script setup lang="ts">
  import { h } from 'vue'
  import { Button, notification2 as notification } from '@xhs/delight'

  function notify() {
    const key = 'close-key'

    notification.danger(
      {
        key,
        title: '自定义额外操作',
        description: '点击关闭手动消失。',
        actions: () => [
          h(
            Button,
            {
              style: { color: 'var(--color-danger)', marginLeft: 'auto', },
              size: 'small',
              type: 'light',
              onClick: () => { notification.close(key) },
            },
            () => '关闭'
          )
        ],
        duration: 0,
      }
    )
  }
</script>
```

##    更新消息内容

```vue
<template>
  <Button type="primary" @click="notify">Open the notification box (update by key)</Button>
</template>

<script setup>
  import { h } from 'vue'
  import { Button, notification2 as notification } from '@xhs/delight'

  const key = 'updatable'

  function notify() {
    notification.open({
      key,
      title: 'Notification titile',
      description: 'description',
    })

    setTimeout(() => {
      notification.open({
        key,
        title: 'New Title',
        description: 'New description',
      })
    }, 1000)
  }
</script>
```

##    默认关闭按钮

通过 `closeable` 设置开启默认关闭 icon：

```vue
<template>
  <Button type="primary" @click="notify">closeable</Button>
</template>

<script setup>
  import { Button, notification2 as notification } from '@xhs/delight'

  function notify() {
    notification.danger(
      {
        title: '默认关闭按钮',
        description: '点击关闭按钮即可关闭。',
        duration: 0,
        closeable: true,
      }
    )
  }
</script>
```

##    位置

通过 `placement` 设置通知位置，通过 `title` 设置通知标题，通过 `description` 设置通知描述：
- `placement` 支持   `top-left` 、`top` 、 `top-right` 、`bottom-left` 、 `bottom` 、`bottom-right` 共 `6` 个位置，默认 `top-right`

```vue
<template>
  <Space block>
    <Button type="primary" @click="notify('top-left')">top-left</Button>
    <Button type="primary" @click="notify('top')">top</Button>
    <Button type="primary" @click="notify('top-right')">top-right</Button>
  </Space>
  <Space block style="margin-top: 20px">
    <Button type="primary" @click="notify('bottom-left')">bottom-left</Button>
    <Button type="primary" @click="notify('bottom')">bottom</Button>
    <Button type="primary" @click="notify('bottom-right')">bottom-right</Button>
  </Space>
</template>

<script setup>
  import { h } from 'vue'
  import { Space, Button, notification2 as notification } from '@xhs/delight'

  function notify(placement) {
    notification.open({
      placement,
      title: 'Notification ' + placement,
      description: 'description',
    })
  }
</script>
```

##    API 参考

- `notification2.success(config)`
- `notification2.danger(config)`
- `notification2.info(config)`
- `notification2.warning(config)`
- `notification2.warn(config)`
- `notification2.open(config)`
- `notification2.close(key: String)`
- `notification2.destroy()`

#### config 参数如下： |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|placement|通知位置| `top-left` &#124; `top` &#124; `top-right` &#124; `bottom-left` &#124; `bottom` &#124; `bottom-right`|'top-right'|
|type|通知类型|'info' &#124; 'success' &#124; 'warning' &#124; 'danger'|-|
|title|通知标题|string &#124; JSX.Element &#124; VNode|-|
|description|通知描述|string &#124; JSX.Element  &#124; VNode|-|
|actions|通知额外操作|(JSX.Element &#124; VNode)[]  &#124; () => (name: string; onClick: (e: MouseEvent) => void)[]|-|
|duration|通知持续时间，单位 ms|number|3000|
|closeable|是否展示默认关闭按钮|boolean|false|
|getContainer|配置渲染节点的输出位置|() => HTMLElement|() => document.body|
|top|消息从顶部弹出时，距离顶部的位置，单位像素。|string|40px|
|bottom|消息从底部弹出时，距离底部的位置，单位像素。|string|40px|
|key|当前通知唯一标志|string|-|
|onClose|点击默认关闭按钮时触发的回调函数|() => void|-|
|onClick|点击 notification 触发的回调函数|() => void|-|
|icon|自定义图标|VNode &#124; JSX.Element|-|
|style|自定义内联样式|`Record`|-|
|class|自定义 CSS class|string|-|
|closeIcon|自定义关闭图标|VNode &#124; JSX.Element|-|
