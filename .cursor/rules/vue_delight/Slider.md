
### 何时使用 当用户需要在数值区间/自定义区间内进行选择时，可为连续或离散值

##    基本使用

```vue
<template>
  <div>
    <Slider v-model="value" />
    <div style="margin: 40px;"></div>
    <p>禁用:</p>
    <Slider :model-value="40" disabled />
    <p>value:{{ value }}</p>
  </div>

</template>

<script setup>
  import { ref } from 'vue'
  import { Slider } from '@xhs/delight'

  const value = ref(10)
</script>

```

##    设置步长

传入 step 设置步长，默认步长为 1。

建议设置值能够被 max - min 整除，否则会出现可选最大值小于 max 的情况。

```vue
<template>
  <div>
    <Slider v-model="value1" :max="1" :step="0.1" />
    <p>value:{{ value1 }}</p>
    <div style="margin: 40px;"></div>
    <Slider v-model="value2" :step="88" />
    <p>value:{{ value2 }}</p>
  </div>

</template>

<script setup>
  import { ref } from 'vue'
  import { Slider } from '@xhs/delight'

  const value1 = ref(1)
  const value2 = ref(0)
</script>
```

##    范围选择

支持选择某一数值范围

配置 range 属性以激活范围选择模式，该属性的绑定值是一个数组，由最小边界值和最大边界值组成

```vue
<template>
  <Slider v-model="value1" range />
  <p>range: {{ value1 }}</p>
</template>

<script setup>
  import { ref } from 'vue'
  import { Slider } from '@xhs/delight'

  const value1 = ref([10, 30])
</script>


```

##    垂直模式

配置 vertical 属性为 true 启用垂直模式。 在垂直模式下，必须设置其父级的高度，或者设置 Slider 高度。

```vue
<template>
  <div style="height: 300px">
    <Slider v-model="value" vertical />

    <span style="margin: 40px;" />

    <Slider v-model="value1" range vertical />

    <p>value: {{ value }}</p>
    <p>value1: {{ value1 }}</p>
  </div>

</template>

<script setup>
  import { ref } from 'vue'
  import { Slider } from '@xhs/delight'

  const value = ref(50)
  const value1 = ref([10, 30])

</script>


```

##    自定义提示

使用 tipFormatter 可以格式化 Tooltip 的内容，设置 tipFormatter=null，则隐藏 Tooltip。

```vue
<template>
  <Slider v-model="value" :tip-formatter="formatter" />

  <Slider v-model="value1" :tip-formatter="null" style="margin-top: 40px;" />
</template>

<script setup>
  import { ref } from 'vue'
  import { Slider } from '@xhs/delight'

  const value = ref(50)
  const value1 = ref(80)

  const formatter = (value) => {
    return value + '%'
  }

</script>
```

##    显示标记

设置 marks 属性可以在滑块上显示标记。

```

marks 属性类型：

{ number: string|VNode } or 

{ 
  number: { 
    style: object, 
    label: string|VNode 
  } 
}

```

```vue
<template>
  <div style="height: 300px">
    <Slider v-model="value" :marks="marks" />

    <div style="margin: 40px;"></div>

    <Slider v-model="value1" :marks="marks" range vertical  />

    <p>value: {{ value }}</p>
    <p>value1: {{ value1 }}</p>
  </div>

</template>

<script setup>
  import { ref, h } from 'vue'
  import { Slider, Tag } from '@xhs/delight'

  const value = ref(50)
  const value1 = ref([10, 30])

  const marks = ref({
    0: '0°C',
    26: h('div', {}, '26°C'),
    37: <Tag size="small">标签</Tag>,
    100: {
      style: {
        color: '#f50',
      },
      label: '100°C',
    },
  });
</script>


```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|disabled|值为 `true` 时，滑块为禁用状态|boolean|false|
|min|最小值|number|0|
|max|最大值|number|100|
|step|步长，取值区间 (0, max - min]|number|1|
|marks|刻度标记，`key` 的类型必须为 `number` 且取值在闭区间 `[min, max]` 内，每个标签可以单独设置样式|object| `{ number: string ｜ VNode }` or  `{ number: { style: object, label: string ｜ VNode } }`|
|range|双模块模式|boolean|false|
|model-value (v-model)|设置当前取值|number &#124; number[]|-|
|vertical|值为 true 时，Slider 为垂直方向|boolean|false|
|tipFormatter|Slider 会把当前值传给 tipFormatter，并在 Tooltip 中显示 tipFormatter 的返回值，若为 null，则隐藏 Tooltip。|null \| (value: number) => string \| number|-|

### 事件 |事件|说明|类型|
| :- | :- | :- |
|change|当 Slider 的值发生改变时，会触发 change 事件，并把改变后的值作为参数传入。|`(number ｜ [number, number]) => void`|