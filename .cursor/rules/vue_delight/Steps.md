

##    基本使用

- 属性 currentStep 设置当前步骤
- 属性 direction 设置展示方向
- Step属性 title 设置标题
- Step属性 description 设置描述

```vue
<template>
  <Space
    align="start"
    direction="vertical"
    block
  >
    <Steps
      :current="currentStep"
      direction="horizontal"
      @change="handleChange"
    >
      <Step title="步骤1" description="一些描述" />
      <Step title="步骤2" description="一些描述" />
      <Step title="步骤3" description="一些描述" />
    </Steps>
    <Space
      style="margin: 0 auto;"
    >
      <Button
        type="primary"

        @click="handlePrevStep"
      >
        上一步
      </Button>
      <Button
        type="primary"
        @click="handleNextStep"
      >
        下一步
      </Button>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import {
    ref
  } from 'vue'
  import { Steps, Step, Button, Space } from '@xhs/delight'

  const currentStep = ref(0)

  const handlePrevStep = () => {
    if (currentStep.value - 1 < -1) {
      return
    }

    currentStep.value -= 1
  }

  const handleNextStep = () => {
    if (currentStep.value + 1 > 3) {
      return
    }

    currentStep.value = currentStep.value + 1
  }

  const handleChange = (step) => {
    console.log('step change: ', step)
  }
</script>
```

##    竖向展示

设置 `direction='vertical' 竖向展示`

```vue
<template>
  <Steps :current="0" direction="vertical" style="height: 400px">
    <Step title="步骤1" description="一些描述" />
    <Step title="步骤2" description="一些描述" />
    <Step title="步骤3" description="一些描述" />
  </Steps>
</template>

<script setup lang="ts">
  import { Steps, Step } from '@xhs/delight'
</script>
```

##    状态展示

以下是一些状态展示

```vue

<template>
  <Steps :current="1" style="height: 120px">
    <Step title="步骤1" description="一些描述" />
    <Step title="步骤2" description="一些描述" />
    <Step title="步骤3" description="一些描述" />
  </Steps>
  <Steps :current="1" status="warning" style="height: 120px">
    <Step title="步骤1" description="一些描述" />
    <Step title="步骤2" description="一些描述" />
    <Step title="步骤3" description="一些描述" />
  </Steps>
  <Steps :current="1" status="loading" style="height: 120px">
    <Step title="步骤1" description="一些描述" />
    <Step title="步骤2" description="一些描述" />
    <Step title="步骤3" description="一些描述" />
  </Steps>
</template>

<script setup lang="ts">
  import { Steps, Step } from '@xhs/delight'
</script>
```

##    Step 自定义图标

通过 对 Step 设置 icon, 展示自定义图标 

```vue
<template>
  <Steps direction="vertical" :current="0" style="height: 400px">
    <Step :icon="FolderUpload" title="步骤1" description="一些描述" />
    <Step :icon="PeopleUpload" title="步骤2" description="一些描述" />
    <Step :icon="UploadLogs" title="步骤3" description="一些描述" />
  </Steps>
</template>

<script setup lang="ts">
  import { Steps, Step } from '@xhs/delight'
  import { FolderUpload, PeopleUpload, UploadLogs } from '@xhs/delight/icons'
</script>
```

##    Step 自定义 slots

设置 `"title"、"description" 自定义 slot`

```vue
<template>
  <Steps :current="1">
    <Step title="步骤1">
      <template #description>
        <Text>
          步骤一
          <Tooltip content="这里是步骤一的详细信息.....">
            <Text link>详细信息</Text>
          </Tooltip>
        </Text>
      </template>
    </Step>
    <Step description="一些描述" status='danger'>
      <template #title>
        <Text bold type="h6" color="danger">
          步骤2
        </Text>
      </template>
      <template #description>
        <Text color="danger">
          步骤二 详细信息
        </Text>
      </template>
    </Step>
    <Step title="步骤3" description="步骤三 详细信息" />
  </Steps>
</template>

<script setup lang="ts">
  import { Steps, Step, Text, Tooltip } from '@xhs/delight'
</script>
```

##    Steps API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|current|指定当前步骤，从 0 开始记数。在子 Step 元素中，可以通过 status 属性覆盖状态|number|0|
|direction|指定步骤条方向。目前支持水平（horizontal）和竖直（vertical）两种方向|string|horizontal|
|status|指定当前步骤的状态，可选 wait process loading success danger|string|process|

### Steps 事件 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|change|当前步骤发生变化时的回调事件,返回当前步骤|(e: number) => void|-|

##    Step API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|title|标题|(e: number) => void|-|
|description|步骤的详情描述，可选|(e: number) => void|-|
|icon|步骤的[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)，可选|(p: IconProps) => string|-|
|status|指定状态。当不配置该属性时，会使用 Steps 的 current 来自动指定状态。可选：wait process loading success danger|string|wait|

### Step 插槽 |插槽|说明|
| :- | :- |
|title|自定义step标题|
|description|自定义step描述|

