## 何时使用 需要展现当前页面上可供跳转的锚点链接，以及快速在锚点之间跳转。

## 基本使用

最简单的用法

```vue
<template>
  <Anchor :get-container="getContainer" :offset-top="70">
    <AnchorLink href="#components-anchor-demo-basic" title="Basic demo" />
    <AnchorLink href="#components-anchor-demo-static" title="Static demo" />
    <AnchorLink
      href="#components-anchor-demo-custom-click"
      title="Custom click demo"
    />
    <AnchorLink
      href="#components-anchor-demo-custom-anchor-highlight"
      title="Custom anchor highlight demo"
    />
    <AnchorLink
      href="#components-anchor-demo-watch-anchor-change"
      title="Watch anchor change demo"
    />
    <AnchorLink
      href="#components-anchor-demo-set-anchor-offset"
      title="Set anchor offset demo"
    />
    <AnchorLink href="#API" title="Api">
      <AnchorLink href="#Anchor-Props" title="Anchor Prop" />
      <AnchorLink href="#AnchorLink-Props" title="Link Prop" />
    </AnchorLink>
  </Anchor>
</template>
<script setup>
import { onMounted } from "vue";
import { Anchor, AnchorLink } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const getContainer = () => document.querySelector(".d-layout-main");
</script>
```

## 静态位置

不浮动，状态不随页面滚动变化。

```vue
<template>
  <Anchor :affix="false" :get-container="getContainer">
    <AnchorLink href="#Basic-demo" title="Basic demo" />
    <AnchorLink href="#Static-demo" title="Static demo" />
    <AnchorLink href="#Custom-click-demo" title="Custom click demo" />
    <AnchorLink
      href="#Custom-anchor-highlight-demo"
      title="Custom anchor highlight demo"
    />
    <AnchorLink
      href="#Watch-anchor-change-demo"
      title="Watch anchor change demo"
    />
    <AnchorLink href="#Set-anchor-offset-demo" title="Set anchor offset demo" />
    <AnchorLink href="#API" title="Api">
      <AnchorLink href="#Anchor-Props" title="Anchor Prop" />
      <AnchorLink href="#AnchorLink-Props" title="Link Prop" />
    </AnchorLink>
  </Anchor>
</template>
<script setup>
import { onMounted } from "vue";
import { Anchor, AnchorLink } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const getContainer = () => document.querySelector(".d-layout-main");
</script>
```

## 自定义 click 事件

```vue
<template>
  <Anchor :affix="false" :get-container="getContainer" @click="handleClick">
    <AnchorLink href="#Basic-demo" title="Basic demo" />
    <AnchorLink href="#Static-demo" title="Static demo" />
    <AnchorLink href="#Custom-click-demo" title="Custom click demo" />
    <AnchorLink
      href="#Custom-anchor-highlight-demo"
      title="Custom anchor highlight demo"
    />
    <AnchorLink
      href="#Watch-anchor-change-demo"
      title="Watch anchor change demo"
    />
    <AnchorLink href="#Set-anchor-offset-demo" title="Set anchor offset demo" />
    <AnchorLink href="#API" title="Api">
      <AnchorLink href="#Anchor-Props" title="Anchor Prop" />
      <AnchorLink href="#AnchorLink-Props" title="Link Prop" />
    </AnchorLink>
  </Anchor>
</template>
<script setup>
import { onMounted } from "vue";
import { Anchor, AnchorLink } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const getContainer = () => document.querySelector(".d-layout-main");

const handleClick = (e, link) => {
  console.log(e, link);
};
</script>
```

## 自定义锚点高亮

```vue
<template>
  <Anchor
    :affix="false"
    :get-container="getContainer"
    :get-current-anchor="getCurrentAnchor"
  >
    <AnchorLink href="#Basic-demo" title="Basic demo" />
    <AnchorLink href="#Static-demo" title="Static demo" />
    <AnchorLink href="#Custom-click-demo" title="Custom click demo" />
    <AnchorLink
      href="#Custom-anchor-highlight-demo"
      title="Custom anchor highlight demo"
    />
    <AnchorLink
      href="#Watch-anchor-change-demo"
      title="Watch anchor change demo"
    />
    <AnchorLink href="#Set-anchor-offset-demo" title="Set anchor offset demo" />
    <AnchorLink href="#API" title="Api">
      <AnchorLink href="#Anchor-Props" title="Anchor Prop" />
      <AnchorLink href="#AnchorLink-Props" title="Link Prop" />
    </AnchorLink>
  </Anchor>
</template>
<script setup>
import { onMounted } from "vue";
import { Anchor, AnchorLink } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const getContainer = () => document.querySelector(".d-layout-main");

const getCurrentAnchor = () => "#Api";
</script>
```

## 监听锚点链接改变

```vue
<template>
  <Anchor :affix="false" :get-container="getContainer" @change="handleChange">
    <AnchorLink href="#Basic-demo" title="Basic demo" />
    <AnchorLink href="#Static-demo" title="Static demo" />
    <AnchorLink href="#Custom-click-demo" title="Custom click demo" />
    <AnchorLink
      href="#Custom-anchor-highlight-demo"
      title="Custom anchor highlight demo"
    />
    <AnchorLink
      href="#Watch-anchor-change-demo"
      title="Watch anchor change demo"
    />
    <AnchorLink href="#Set-anchor-offset-demo" title="Set anchor offset demo" />
    <AnchorLink href="#API" title="Api">
      <AnchorLink href="#Anchor-Props" title="Anchor Prop" />
      <AnchorLink href="#AnchorLink-Props" title="Link Prop" />
    </AnchorLink>
  </Anchor>
</template>
<script setup>
import { onMounted } from "vue";
import { Anchor, AnchorLink } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const getContainer = () => document.querySelector(".d-layout-main");

const handleChange = (link) => {
  console.log("Anchor:OnChange", link);
};
</script>
```

## 设置锚点滚动偏移量

锚点目标滚动到屏幕正中间。

```vue
<template>
  <Anchor :get-container="getContainer" :target-offset="targetOffset">
    <AnchorLink href="#Basic-demo" title="Basic demo" />
    <AnchorLink href="#Static-demo" title="Static demo" />
    <AnchorLink href="#Custom-click-demo" title="Custom click demo" />
    <AnchorLink
      href="#Custom-anchor-highlight-demo"
      title="Custom anchor highlight demo"
    />
    <AnchorLink
      href="#Watch-anchor-change-demo"
      title="Watch anchor change demo"
    />
    <AnchorLink href="#Set-anchor-offset-demo" title="Set anchor offset demo" />
    <AnchorLink href="#API" title="Api">
      <AnchorLink href="#Anchor-Props" title="Anchor Prop" />
      <AnchorLink href="#AnchorLink-Props" title="Link Prop" />
    </AnchorLink>
  </Anchor>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { Anchor, AnchorLink } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const getContainer = () => document.querySelector(".d-layout-main");

const targetOffset = ref();

onMounted(() => {
  targetOffset.value = window.innerHeight / 2;
});
</script>
```

## API 参考

### Anchor Props

| 属性             | 说明                                  | 类型              | 默认值       |
| :--------------- | :------------------------------------ | :---------------- | :----------- |
| affix            | 固定模式                              | boolean           | true         |
| bounds           | 锚点区域边界                          | number            | 5(px)        |
| getContainer     | 指定滚动的容器                        | () => HTMLElement | () => window |
| getCurrentAnchor | 自定义高亮的锚点                      | () => string      | -            |
| offsetBottom     | 距离窗口底部达到指定偏移量后触发      | number            | -            |
| offsetTop        | 距离窗口顶部达到偏移量后触发          | number            | -            |
| targetOffset     | 锚点滚动偏移量，默认与 offsetTop 相同 | number            | -            |
| wrapperClass     | 容器的类名                            | string            | -            |
| wrapperStyle     | 容器样式                              | Object            | -            |

### Anchor 事件

| 事件   | 说明                   | 类型                                                        |
| :----- | :--------------------- | :---------------------------------------------------------- |
| change | 监听锚点链接改变       | `(currentActiveLink: string) => void`                       |
| click  | `click` 事件的 handler | `(e: Event, link: { title: string; link: string }) => void` |

### AnchorLink Props

| 属性   | 说明                           | 类型         | 默认值 |
| :----- | :----------------------------- | :----------- | :----- |
| href   | 锚点链接                       | string       | -      |
| target | 该属性指定在何处显示链接的资源 | string       | -      |
| title  | 文字内容                       | string、slot | -      |
