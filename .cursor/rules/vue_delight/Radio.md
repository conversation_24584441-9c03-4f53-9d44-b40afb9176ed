

##    基本使用

通过 `value` 设置单选框的值，通过 `label` 或 `slots.default` 设置单选框右侧展示的内容：

```vue
<template>
  <Space direction="vertical" align="start">
    <Radio value="option A"/>
    <Radio value="option A" label="选项 A" autofocus/>
    <Radio value="option A">选项 A</Radio>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Radio } from '@xhs/delight'
</script>
```

##    描述

通过 `description` 或 `slots.description` 设置描述：

```vue
<template>
  <Space direction="vertical" align="start">
    <Radio
      value="option A"
      label="选项 A"
      description="这是一个名为 A 的选项"
    />
    <Radio
      value="option A"
      label="选项 A"
    >
      <template #description>这是一个名为 A 的选项</template>
    </Radio>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Radio } from '@xhs/delight'
</script>
```

##    展开描述

通过 `expand` 设置 `Radio` 展开描述，展开后描述会通过换行的方式完整展示：

```vue
<template>
  <Radio
    value="option A"
    label="选项 A"
    description="这是一个名为 A 的选项"
    expand
  />
</template>

<script setup lang="ts">
  import { Radio } from '@xhs/delight'
</script>
```

##    选中状态

通过 `checked` 设置选中状态：

```vue
<template>
  <Space direction="vertical" align="start">
    <Radio
      v-model:checked="checked"
      value="option A"
      label="选项 A"
      description="这是一个名为 A 的选项"
    />
    <Text>当前选中状态：{{ checked }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Radio, Text } from '@xhs/delight'

  const checked = ref(false)
</script>
```

##    可反选

`Radio` 选中后默认不可以通过再次点击取消，除非设置 `revertable`：

```vue
<template>
  <Space direction="vertical" align="start">
    <Radio
      v-model:checked="checked"
      value="option A"
      label="选项 A"
      description="这是一个名为 A 的选项"
      revertable
    />
    <Text>当前选中状态：{{ checked }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Radio, Text } from '@xhs/delight'

  const checked = ref(true)
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <Radio
    value="option A"
    label="选项 A"
    description="这是一个名为 A 的选项"
    block
  />
</template>

<script setup lang="ts">
  import { Radio } from '@xhs/delight'
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <Radio
    value="option A"
    label="选项 A"
    description="这是一个名为 A 的选项"
    disabled
  />
</template>

<script setup lang="ts">
  import { Radio } from '@xhs/delight'
</script>
```

##    单选框组合

通过将 `Radio` 插入 `RadioGroup` 或传入 `RadioGroup` 的 `options` 设置单选框组合：
 - `Radio` 或 `options` 中属性的优先级比 `RadioGroup` 更高

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup v-model="value1" @click="handleClick">
      <Radio
        value="option A"
        label="选项 A"
        revertable
      />
      <Radio
        value="option B"
        label="选项 B"
        checked
      />
      <Radio
        value="option C"
        label="选项 C"
        disabled
      />
    </RadioGroup>
    <Text>当前选中项：{{ value1 }}</Text>
    <RadioGroup v-model="value2" :options="options"/>
    <Text>当前选中项：{{ value2 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Radio, Text } from '@xhs/delight'

  const value1 = ref('option A')
  const value2 = ref('option B')

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]

  const handleClick = (val) => {
    console.log('click val', val)
  }
</script>
```

##    单选框组合的 name

通过 `name` 设置单选框组合下所有 input[type="radio"] 的 name 属性：

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup v-model="value" :options="options" name="form"/>
    <Text>当前选中项：{{ value }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Radio, Text } from '@xhs/delight'

  const value = ref('option A')

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    单选框组合的排版方向

通过 `direction` 设置单选框组合的排版方向，当纵向展示时默认将 `description` 展开，除非显示声明：

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup v-model="value1" direction="vertical">
      <Radio
        value="option A"
        label="选项 A"
        revertable
      />
      <Radio
        value="option B"
        label="选项 B"
        description="这是一个名为 B 的选项"
        checked
      />
      <Radio
        value="option C"
        label="选项 C"
        description="这是一个名为 C 的选项"
        :expand="false"
        disabled
      />
    </RadioGroup>
    <Text>当前选中项：{{ value1 }}</Text>
    <RadioGroup v-model="value2" :options="options" direction="vertical"/>
    <Text>当前选中项：{{ value2 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Radio, Text } from '@xhs/delight'

  const value1 = ref('option A')
  const value2 = ref('option B')

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    单选框组合块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <RadioGroup v-model="value" :options="options" block/>
  <Text>当前选中项：{{ value }}</Text>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Radio, Text } from '@xhs/delight'

  const value = ref('option A')

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    必填

通过 `required` 设置为必填项，默认在失焦 / 点击 / 手动校验后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup ref="radio" v-model="value" :options="options" required/>
    <Text>当前校验状态：{{ radio?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Text } from '@xhs/delight'

  const radio = ref()
  const value = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则：

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup ref="radio" v-model="value" :options="options" :validate="validate"/>
    <Text>当前校验状态：{{ radio?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Text } from '@xhs/delight'

  const radio = ref()
  const value = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]

  function validate({ modelValue, fullValue }) {
    return fullValue.some(x => x.value === 'option B')
  }
</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup ref="radio" v-model="value" :options="options" required validate-timing="immediate"/>
    <Text>当前校验状态：{{ radio?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Text } from '@xhs/delight'

  const radio = ref()
  const value = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup ref="radio" v-model="value" :options="options" required validate-timing="manual"/>
    <Text>当前校验状态：{{ radio?.status }}</Text>
    <Button type="primary" @click="manualValidate">result：{{ result }}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Text, Button } from '@xhs/delight'

  const radio = ref()
  const value = ref()
  const result = ref()

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]

  function manualValidate() {
    radio.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }
</script>
```

##    单选框组合全局禁用

通过 `disabled` 设置单选框组合全局禁用：

```vue
<template>
  <Space direction="vertical" align="start">
    <RadioGroup v-model="value" :options="options" disabled/>
    <Text>当前选中项：{{ value }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, RadioGroup, Radio, Text } from '@xhs/delight'

  const value = ref('option A')

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]
</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素：

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 RadioGroup 的静态描述文本"
      on-error="这是一段 RadioGroup 的静态错误提示"
    >
      <RadioGroup :options="options" required :validate="validate"/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :on-error="onError"
    >
      <RadioGroup :options="options" required :validate="validate"/>
    </FormItem>
    <FormItem name="3">
      <RadioGroup :options="options" required :validate="validate"/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue, fullValue }">当前输入内容为：{{ fullValue?.map(v => v.label).join() }}</template>
      <template v-slot:on-error="{ modelValue, fullValue }">{{
        modelValue !== undefined
          ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，必须选择选项 B'
          :'必填项'
      }}</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { RadioGroup, Form, FormItem } from '@xhs/delight'

  const options = [
    {
      value: 'option A',
      label: '选项 A',
      description: '这是一个名为 A 的选项',
      revertable: true,
    },
    {
      value: 'option B',
      label: '选项 B',
      description: '这是一个名为 B 的选项',
      checked: true,
    },
    {
      value: 'option C',
      label: '选项 C',
      description: '这是一个名为 C 的选项',
      disabled: true,
    },
  ]

  function description({ modelValue, fullValue }) {
    return '当前输入内容为：' + fullValue?.map(v => v.label).join()
  }

  function onError({ modelValue, fullValue }) {
    return  modelValue !== undefined
        ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，必须选择选项 B'
        :'必填项'
  }

  function validate({ modelValue, fullValue }) {
    return fullValue.some(x => x.value === 'option B')
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    RADIO API 参考

通过设置 Radio 的属性来产生不同的单选框样式：

|属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|value|单选框的值|string &#124; number|-|
|label|单选框的标题|string|-|
|description|单选框的描述|string|-|
|tooltipProps|透传给 description 显示的 Tooltip 组件（具体属性请看 Tooltip 组件属性）|`Tooltip.props`|-|
|expand|单选框的描述是否展开展示|boolean|-|
|checked|单选框的选中状态|boolean|false|
|revertable|单选框选中后再次点击是否支持取消选中|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean|false|
|descriptionClass|给描述添加自定义类名，用于样式更改|string|-|

### RADIO 事件 |事件 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|update:checked|单选框的选中状态变化的回调事件|(e: boolean) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|

### RADIO 插槽 |插槽 | 说明|
| :- | :- |
|default|单选框的标题|
|description|单选框的描述|

##    RADIOGROUP API 参考

```
interface AllRadioProps {
  value?: string | number | boolean
  label?: string
  description?: string
  checked?: boolean
  autofocus?: boolean
  block?: boolean
  disabled?: boolean
  tooltipProps?: Record<string, any>
  'onUpdate:checked'?: (e: boolean) => void
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
  onFocus?: (e: FocusEvent) => void
  onBlur?: (e: FocusEvent) => void
}

```

通过设置 Radiogroup 的属性来产生不同的单选框组合样式：

|属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|modelValue|单选框组合选中项的 value|string &#124; number|-|
|name|单选框组合下所有 input[type="radio"] 的 name 属性|string|-|
|direction|单选框组合的排版方向（当纵向展示时默认将 `description` 展开，除非显示声明）|'horizontal' &#124; 'vertical'，|'horizontal'|
|options|单选框组合中选项的属性|AllRadioProps[]|-|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则，不能排除存在 `value` 相同的选项，`fullValue` 参数永远是一个数组|(args: &#123; modelValue?: string &#124; number; fullValue: AllRadioProps[] &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean|false|
|tooltipProps|透传给 Radio 组件（注意：只有接受 options 属性生成的 Radio 才会生效）|`Tooltip.props`|-|

### RADIOGROUP 事件 |事件 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|update:modelValue|单选框组合中选中项变化的回调事件|(e: string &#124; number) => void|-|
|change|复选框组合中选项状态变化的回调事件，不能排除存在 `value` 相同的选项，`onChange` 返回的永远是一个数组|(e: AllRadioProps[]) => void|-|
|click|手动点击复选框组合中选项时的回调事件 | (val: string &#124; number) => void | - |

### RADIOGROUP 插槽 |插槽 | 说明|
| :- | :- |
|default|单选框组合中的选项，只支持 `Radio`|

### RADIOGROUP TEMPLATE REF API |内容 | 说明 | 类型|
| :- | :- | :- |
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|