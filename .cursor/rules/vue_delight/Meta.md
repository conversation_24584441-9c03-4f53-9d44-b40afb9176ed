

##    基本使用

- 通过 `avatar` 或 `slots.avatar` 设置头像
- 通过 `title` 或 `slots.title` 设置标题
- 通过 `description` 或 `slots.description` 设置描述内容
- 通过 `actions` 或 `slots.actions` 设置操作栏

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Meta
      :avatar="{ text: 'Meta' }"
      title="标题"
      description="描述内容"
      :actions="actions"
    />
    <Meta>
      <template #avatar>
        <Avatar text="Meta"/>
      </template>
      <template #title>
        <Text color="text-title" bold>标题</Text>
      </template>
      <template #description>
        <Text>描述内容</Text>
      </template>
      <template #actions>
        <Button
          size="small"
          type="light"
        >
          ...
        </Button>
      </template>
    </Meta>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Meta, Avatar, Text, Button } from '@xhs/delight'

  const actions = Array.from({ length: 4 }, (_, i) => ({ name: '操作 ' + (i + 1), onClick: () => console.log(i + 1) }))
</script>
```

##    边缘间距

通过 `space` 设置边缘间距：

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Meta
      space="unset"
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    />
    <Meta
      space="small"
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    />
    <Meta
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    />
    <Meta
      space="large"
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    />
  </Space>
</template>

<script setup lang="ts">
  import { Space, Meta, Button } from '@xhs/delight'
</script>
```

##    头像对齐方式

通过 `avatarAlign` 设置头像对齐方式，默认居中对齐：

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Meta
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    />
    <Meta
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    />
    <Meta
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="end"
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    />
  </Space>
</template>

<script setup lang="ts">
  import { Space, Meta, Button } from '@xhs/delight'
</script>
```

##    操作栏对齐方式

通过 `actionsAlign` 设置操作栏对齐方式，默认居中对齐：

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Meta
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      :actions="actions"
      actions-align="start"
    >
      <template #description>
        <Text :max-lines="3" ellipsis>
          北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
        </Text>
      </template>
    </Meta>
    <Meta
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      :actions="actions"
    >
      <template #description>
        <Text :max-lines="3" ellipsis>
          北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
        </Text>
      </template>
    </Meta>
    <Meta
      avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png"
      avatar-align="start"
      title="逍遥游"
      :actions="actions"
      actions-align="end"
    >
      <template #description>
        <Text :max-lines="3" ellipsis>
          北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
        </Text>
      </template>
    </Meta>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Meta, Text } from '@xhs/delight'

  const actions = Array.from({ length: 4 }, (_, i) => ({ name: '操作 ' + (i + 1), onClick: () => console.log(i + 1) }))
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|space|元信息边缘间距|'unset' &#124; 'small' &#124; 'default' &#124; 'large'|'default'|
|avatar|元信息的头像，当传入 `string` 时认为是图片头像的 `url`|string &#124; AvatarProps|-|
|avatarAlign|元信息头像的对齐方式|'start' &#124; 'center' &#124; 'end' &#124; 'unset'|'center'|
|title|元信息的标题|string|-|
|description|元信息的描述内容|string|-|
|actions|元信息操作栏|&#123; name: string; onClick: (e: MouseEvent) => void &#125;[]|-|
|actionsAlign|元信息操作栏的对齐方式|'start' &#124; 'center' &#124; 'end' &#124; 'unset'|'center'|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|avatar|元信息的头像|
|title|元信息的标题|
|description|元信息的描述内容|
|actions|元信息的操作栏|