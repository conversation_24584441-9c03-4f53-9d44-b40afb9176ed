用来展示滚动的空白区域

## 基本使用

通过 `target` 设置需要滚动到顶部的目标元素，默认为 `window`：

```vue
<template>
  <Text>BackTop 在页面右下角</Text>
  <BackTop :target="target" />
</template>

<script setup lang="ts">
import { BackTop, Text } from "@xhs/delight";

const target = document.querySelector(".d-layout-main");
</script>
```

## API 参考

| 属性   | 说明                     | 类型        | 默认值 |
| :----- | :----------------------- | :---------- | :----- |
| target | 需要滚动到顶部的目标元素 | HTMLElement | window |
