
import { Grid, GridI<PERSON>, Divider, Badge } from '@xhs/delight'

## 何时使用 - 当需要突出某个或某组数字时使用
- 当需要展示带描述的统计类数据时使用

##    基本使用

通过 `value` 传入需要展示的数值，`title` 传入标题：

```vue
<template>
    <Grid :columns="3" column-gap="var(--size-space-default)" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
        <GridItem>
            <Statistic :title="title" :value="value" />
        </GridItem>
        <GridItem >
            <Statistic :title="title" :value="value" trend-title="较上周" :trend-value="trendValue" :trend-style="{color:'red'}" /> 
        </GridItem>
        <GridItem>
            <Statistic :title="title" :value="value" trend-title="较上周" :trend-value="trendValue" :trend-style="{color:'red'}" reverse />
        </GridItem>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { Grid, GridItem, Statistic } from '@xhs/delight'

  const rows = Array.from({ length: 2 }, (_, i) => i + 1)
  const title = '今日销售额'
  const trendTitle = '较上周'
  const value = 123456789
  const trendValue = 987654321
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  height: 100px;
}
</style>
```

##    添加后缀

通过 `suffix` 添加单位或者自定义内容：

```vue
<template>
    <Grid :columns="3" column-gap="var(--size-space-default)" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
        <GridItem>
            <Statistic :title="title" :value="value" suffix="元" />
        </GridItem>
        <GridItem >
            <Statistic :title="title" :value="value" suffix="$" />
        </GridItem>
        <GridItem>
            <Statistic :title="title" :value="value">
              <template #suffix>
                <Icon size="small" :icon="Help" />
              </template>
            </Statistic>
        </GridItem>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { Grid, GridItem, Statistic, Icon } from '@xhs/delight'
  import { Help } from '@xhs/delight/icons'

  const rows = Array.from({ length: 2 }, (_, i) => i + 1)
  const title = '今日销售额'
  const value = 123456789
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
}
</style>
```

##    自定义 value 样式

- 通过 `valueType` 设置 value 的显示样式
- 通过 `precision` 设置 value 的精度
- 通过 `groupSeparator` 设置 value 千分位的标识符
- 通过 `decimalSeparator` 设置 value 小数位的标识符

```vue
<template>
    <Grid :columns="3" column-gap="var(--size-space-default)" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
        <GridItem>
            <Statistic :title="title" :value="value" suffix="元" />
        </GridItem>
        <GridItem >
            <Statistic :title="title" :value="value" :precision="1" />
        </GridItem>
        <GridItem>
            <Statistic :title="title" :value="value" :value-style="{color:'red',fontSize:'20px'}" />
        </GridItem>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { Space, Grid, GridItem, Text, Statistic } from '@xhs/delight'

  const rows = Array.from({ length: 2 }, (_, i) => i + 1)
  const title = '今日销售额'
  const value = 123456789
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
}
</style>
```

##    自定义内容

通过 `value` 、`title`、`trend` 插槽设置自定义内容：

```vue
<template>
    <Grid :columns="3" column-gap="var(--size-space-default)" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
        <GridItem>
            <Statistic :title="title" :value="value" suffix="元">
              <template #title>
                <div :style="{display:'flex',alignItems:'center'}">
                  <Text color="text-description">{{title}}</Text>
                  <Icon  color="text-description" :icon="Help" size="small" :style="{marginLeft:'4px'}" />
                </div>
              </template>  
            </Statistic>
        </GridItem>
        <GridItem >
            <Statistic :title="title" :value="value">
              <template #value>
                <Text>{{value}}</Text>
              </template> 
            </Statistic>
        </GridItem>
        <GridItem>
          <div>
            <Statistic :title="title" :value="value">
              <template #trend>
                <Text color="text-description" size="small">今日营业额较昨日持平</Text>
                <Icon  color="text-description" :icon="Help" size="small" :style="{marginLeft:'4px'}" />
              </template> 
            </Statistic>
          </div>
        </GridItem>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { Grid, GridItem, Text, Icon, Statistic } from '@xhs/delight'
  import { Help } from '@xhs/delight/icons'

  const rows = Array.from({ length: 2 }, (_, i) => i + 1)
  const title = '今日销售额'
  const value = 123456789
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
}
</style>
```

##    列表展示

当传给 `value` 的值是对象时，会以列表的显示渲染数据， 此时传入的其他属性失效:

```vue
<template>
    <Grid :columns="3" column-gap="var(--size-space-default)" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
        <GridItem>
            <Statistic :value="value" />
        </GridItem>
        <GridItem >
            <Statistic :value="value" />
        </GridItem>
        <GridItem>
            <Statistic :value="value" />
        </GridItem>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { Grid, GridItem, Text, Icon, Statistic } from '@xhs/delight'
  import { Help } from '@xhs/delight/icons'

  const rows = Array.from({ length: 2 }, (_, i) => i + 1)
  const value = {
    实时用户数量: 12000,
    数据2: '1级',
    数据3: '按钮标签',  
  }
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
}
</style>
```

##    API 参考

### Statistic 属性 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|title|数值的标题|string|-|
|trendTitle|增长趋势的标题|string|-|
|value|数值的内容，当传入的类型是对象时，会以列表的形式展示内容|'string' &#124; 'number' &#124; Object|-|
|trendValue|增长趋势数值的内容|number|-|
|precision|数值精度，只有 value 的类型是 number 时生效|number|-|
|prefix|设置数值前缀|string|-|
|suffix|设置数值后缀|string|-|
|groupSeparator|设置千分位标识符|string|,|
|decimalSeparator|设置小数点标识符|string|.|
|valueStyle|设置数值的样式|style|-|
|trendStyle|设置增长趋势数值的样式|style|-|
|reverse|颠倒 title、value 的位置，为 true 时，有关趋势的属性失效|boolean|false|

### Statistic 插槽 |插槽|说明|
| :- | :- |
|title|自定义数值标题|
|prefix|自定义数值前缀|
|suffix|自定义数值后缀|
|trend|自定义趋势文本|

