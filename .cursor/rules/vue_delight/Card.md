

##    基本使用

通过 `slots.default` 设置内容：

```vue
<template>
  <Card>
    北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
  </Card>
</template>

<script setup lang="ts">
  import { Card } from '@xhs/delight'
</script>

```

##    无边框

在灰色背景上使用无边框的卡片，设置 `bordered = false` 即可

```vue
<template>
  <Space block justify="center" style="height: 100%; background: rgb(236, 236, 236)">
    <Card :bordered="false">
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>
  </Space>
</template>

<script setup>
  import { Card, Space } from '@xhs/delight'
</script>
```

##    页头标题

通过 `title` 或 `slots.title` 设置页头标题：

```vue
<template>
  <Space direction="vertical" align="start">
    <Card title="逍遥游">
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>
    <Card>
      <template #title>
        <Meta space="unset" avatar="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png">
          <template #title>
            <Text type="h6" bold>逍遥游</Text>
          </template>
          <template #description>
            <Text
              ellipsis
              tooltip
              style="max-width: 100%"
              :link="{ href: 'https://delight.devops.xiaohongshu.com/delight/cmp/card' }"
            >
              https://delight.devops.xiaohongshu.com/delight/cmp/card
            </Text>
          </template>
        </Meta>
      </template>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Card, Meta, Text } from '@xhs/delight'
</script>
```

##    页头额外内容

通过 `slots.extra` 设置页头额外内容：

```vue
<template>
  <Card title="逍遥游">
    <template #extra>
      <Tooltip content="extra info">
        <Icon class="d-clickable" color="text-title" :icon="More"/>
      </Tooltip>
    </template>
    北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
  </Card>
</template>

<script setup lang="ts">
  import { Card, Icon, Tooltip } from '@xhs/delight'
  import { More } from '@xhs/delight/icons'
</script>
```

##    封面

通过 `cover` 或 `slots.cover` 设置封面：

```vue
<template>
  <Space direction="vertical" align="start">
    <Card cover="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png">
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>
    <Card>
      <template #cover>
        <img src="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png">
      </template>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>
  </Space>
</template>

<script setup lang="ts">
  import { Card, Space, Text, Icon } from '@xhs/delight'
  import { More, Copy, Edit } from '@xhs/delight/icons'
</script>
```

##    页脚

通过 `slots.footer` 设置页脚：

```vue
<template>
  <Card cover="https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png">
    <template #footer>
      <div style="display: flex">
        <Space style="margin-left: auto" :size="24">
          <Text link :icon="Copy">复制</Text>
          <Text link :icon="Edit">编辑</Text>
        </Space>
      </div>
    </template>
    北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
  </Card>
</template>

<script setup lang="ts">
  import { Card, Space, Text } from '@xhs/delight'
  import { Copy, Edit } from '@xhs/delight/icons'
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|bordered|是否有边框|boolean|true|
|title|卡片页头的标题|string|-|
|cover|卡片封面的 url|string|-|
|objectFit|卡片封面的适配方式|'contain' &#124; 'cover' &#124; 'fill' &#124; 'none' &#124; 'scale-down'|'cover'|

### Avatar 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### Avatar 插槽 |插槽|说明|
| :- | :- |
|title|卡片页头的标题|
|extra|卡片页头的额外内容|
|cover|卡片的封面|
|footer|卡片的页脚|
|default|卡片的内容|