

##    基本用法

通过 `type` 设置按钮样式：

```vue
<template>
  <Button>default</Button>
  <Button type="primary">primary</Button>
  <Button type="secondary">secondary</Button>
  <Button type="danger">danger</Button>
  <Button type="light">light</Button>
  <br/>
  <br/>
  <Button type="stroke">stroke</Button>
</template>

<script setup lang="ts">
  import { Button } from '@xhs/delight'
</script>

<style scoped>
  .d-button + .d-button {
    margin-left: var(--size-space-default);
  }
</style>
```

##    尺寸

通过 `size` 设置按钮大小：

```vue
<template>
  <Button size="small">small</Button>
  <Button>default</Button>
  <Button size="large">large</Button>
</template>

<script setup lang="ts">
  import { Button } from '@xhs/delight'
</script>

<style scoped>
  .d-button + .d-button {
    margin-left: var(--size-space-default);
  }
</style>
```

##    图标

通过 `icon` 设置按钮图标，并通过 `iconPosition` 更改 `icon` 插入的位置：

```vue
<template>
  <Button :icon="Home" />
  <Button type="primary" :icon="Home" />
  <Button type="secondary" :icon="Home" />
  <Button type="danger" :icon="Home" />
  <Button type="light" :icon="Home" />
  <Button  :icon="{ icon: Home, theme: 'filled' }">default</Button>
  <Button :icon="Home" icon-position="right">default</Button>
</template>

<script setup lang="ts">
  import { Button } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
</script>

<style scoped>
  .d-button + .d-button {
    margin-left: var(--size-space-default);
  }
</style>
```

##    禁用

通过 `disabled` 禁用按钮：

```vue
<template>
  <Button disabled>default</Button>
  <Button type="primary" disabled>primary</Button>
  <Button type="secondary" disabled>secondary</Button>
  <Button type="danger" disabled>danger</Button>
  <Button type="light" disabled>light</Button>
  <br/>
  <br/>
  <Button type="stroke" disabled>stroke</Button>
</template>

<script setup lang="ts">
  import { Button } from '@xhs/delight'
</script>

<style scoped>
  .d-button + .d-button {
    margin-left: var(--size-space-default);
  }
</style>
```

##    加载中

通过 `loading` 设置按钮为加载中，加载中会去除文字

```vue
<template>
  <Space direction="vertical" align="start">
    <Button loading :disabled='false'/>
    <Button type="primary" loading>click me</Button>
    <Button :loading="loading" @click="loading=true">click me</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
  import { ref } from 'vue'

  const loading = ref(false)
</script>
```

##    圆形按钮

通过 `round` 设置按钮为圆形按钮：

```vue
<template>
  <Button round>default</Button>
  <Button :icon="Home" round />
</template>

<script setup lang="ts">
  import { Button } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
</script>

<style scoped>
  .d-button + .d-button {
    margin-left: var(--size-space-default);
  }
</style>
```

##    块级按钮

通过 `block` 设置按钮为块级元素：

```vue
<template>
  <Button block>default</Button>
</template>

<script setup lang="ts">
  import { Button } from '@xhs/delight'
</script>
```

##    API 参考

##    按钮组合

通过将 `Button` 插入 `ButtonGroup` 设置按钮组合：

```vue
<template>
  <ButtonGroup round>
    <Button>default1</Button>
    <Button>default2</Button>
  </ButtonGroup>
  <br/>
  <br/>
  <ButtonGroup disabled>
    <Button type='primary'>primary1</Button>
    <Button type='primary'>primary2</Button>
  </ButtonGroup>
  <br/>
  <br/>
  <ButtonGroup size='large'>
    <Button type='secondary'>secondary1</Button>
    <Button type='secondary'>secondary2</Button>
  </ButtonGroup>
  <br/>
  <br/>
  <ButtonGroup type='danger'>
    <Button>danger1</Button>
    <Button>danger2</Button>
  </ButtonGroup>
  <br/>
  <br/>
  <ButtonGroup>
    <Button type='light'>light1</Button>
    <Button type='light'>light2</Button>
  </ButtonGroup>  
  <br/>
  <br/> 
  <ButtonGroup type='primary'>
    <Button :icon="Home"></Button>
    <Button :icon="Add"></Button>
    <Button :icon="More"></Button>
  </ButtonGroup>
  <br/>
  <br/> 
  <ButtonGroup>
    <Button :icon="Home">default</Button>
    <Dropdown :options="options" auto-close placement='bottom'>
      <Button :icon='Down'></Button>
    </Dropdown>
  </ButtonGroup>
  <br/>
  <br/> 
  <ButtonGroup type='primary'>
    <Button :icon="Home">default</Button>
    <Dropdown :options="options" auto-close placement='bottom'>
      <Button :icon='More'></Button>
    </Dropdown>
  </ButtonGroup>
</template>

<script setup lang="ts">
  import { ButtonGroup, Button, Dropdown } from '@xhs/delight'
  import { Home, Down, More, Add } from '@xhs/delight/icons'
  const options = [
    {
      label: 'option1',
      value: 'a',
      tooltip: 'xxxx',
      tooltipTheme: 'light',
      onClick: () => { console.log('选择了 option1') }
    },
    {
      label: 'option2',
      value: 'b',
      tooltip: 'xxxx',
      tooltipPlacement: 'bottom',
    },
    {
      label: 'option3',
      value: 'c'
    },
    {
      label: 'option4',
      value: 'd'
    }
  ]
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|type|按钮类型|`'default'` \| `'primary'` \| `'secondary'` \| `'danger'` \| `'light'`|`'default'`|
|size|按钮大小|`'small'` \| `'default'` \| `'large'`|`'default'`|
|icon|[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|`(p: IconProps) => string`|-|
|iconPosition|图标位置|`'left'` \| `'right'`|`'left'`|
|disabled|禁用状态|`boolean`|`false`|
|loading|加载状态|`boolean`|`false`|
|round|设置为圆形按钮|`boolean`|`false`|
|block|设置为块级元素|`boolean`|`false`|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|`(e: MouseEvent) => void`|-|
|mousedown|鼠标按下的回调事件|`(e: MouseEvent) => void`|-|
|mouseenter|鼠标进入的回调事件|`(e: MouseEvent) => void`|-|
|mouseleave|鼠标离开的回调事件|`(e: MouseEvent) => void`|-|
|mouseup|鼠标抬起的回调事件|`(e: MouseEvent) => void`|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|按钮的内容|

### ButtonGroup Props |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|type|全部子按钮类型|`'default'` \| `'primary'` \| `'secondary'` \| `'danger'` \| `'light'`|`'default'`|
|size|全部子按钮大小|`'small'` \| `'default'` \| `'large'`|`'default'`|
|disabled|全部子按钮禁用状态|`boolean`|`false`|
|round|全部子按钮设置为圆形按钮|`boolean`|`false`|