

### 何时使用 - 网络较慢，需要长时间等待加载处理的情况下使用。
- 在图文信息内容较多的列表/卡片中使用。
- 只在第一次加载数据的时候使用。
- 可以被 Spinner 完全代替，但是在可用的场景下可提供比 Spinner 更好的视觉效果和用户体验。

##    基本使用

```vue
<template>
  <Skeleton />
</template>

<script setup>
  import { Skeleton } from '@xhs/delight'
</script>
```

##    显示头像

通过 avatar 设置是否显示头像占位图：

```vue
<template>
  <Skeleton avatar/>
</template>

<script setup>
  import { Skeleton } from '@xhs/delight'
</script>
```

##    显示图片

通过 image 设置是否显示图片占位图：

```vue
<template>
  <Skeleton image/>
</template>

<script setup>
  import { Skeleton } from '@xhs/delight'
</script>
```

##    控制行数

通过 paragraph 设置段落占位图的行数：

```vue
<template>
  <Skeleton :paragraph="{ rows: 4 }"/>
</template>

<script setup>
  import { Skeleton } from '@xhs/delight'
</script>
```

##    包含子组件

加载占位图包含子组件：

```vue
<template>
  <Skeleton :loading="loading">
    <h3>提示</h3>
    <p>现在可以使用 Skeleton 来设置骨架屏了。</p>
  </Skeleton>
  <br/>
  <Button size="large" :disabled="disabled" @click="handleClick">Show Skeleton</Button>
</template>

<script setup>
  import { ref } from 'vue'
  import { Skeleton, Button } from '@xhs/delight'
  const loading = ref(false)
  const disabled = ref(false)
  function handleClick() {
    loading.value = true
    disabled.value = true
    setTimeout(() => {
      loading.value = false
      disabled.value = false
    }, 2000)
  }
</script>
```

##    列表

配合列表组件使用骨架屏：

```vue
<template>
  <Switch v-model="loading" />
  <Skeleton
    v-for="(item,index) in options"
    :key="index" 
    :loading="loading"
    :paragraph="{ rows: 2 }"
    :style="{ marginBottom: '20px', marginTop:' 8px', marginLeft: '12px' }"
    avatar
  >
    <List :options="[item]"/>
  </Skeleton>
</template>

<script setup>
  import { ref } from 'vue'
  import { Skeleton, List, Switch } from '@xhs/delight'
  const loading = ref(true) 
  const options = Array
    .from(
      { length: 2 },
      (_, i) => (
        {
          avatar: 'https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png',
          avatarAlign: 'start',
          title: '逍遥游',
          description: '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”',
        }
      ),
    )
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
| active | 是否展示动画效果 | boolean | true |
| avatar | 是否展示头像占位图 | boolean | false |
| loading | 为 true 时展示占位图，反之显示子组件 | boolean | true |
| title | 可为 boolean 或着 Object 类型， 为 Object 类型时，用于控制标题占位图的宽度 | boolean &#124; &#123; width: number &#124; string &#125; | true |
| paragraph | 可为 boolean 或着 Object 类型， 为 Object 类型时，用于控制段落占位图的宽度和行数 | boolean &#124; &#123; rows: number, width: (number &#124; string)[] &#124; number &#124; string &#125; | true |
| image | 可为 boolean 或着 Object 类型， 为 Object 类型时，用于控制图片占位图的宽度和高度 | boolean &#124; &#123; width: number &#124; string, height: number &#124; string &#125; | false |

