

##    类型

通过 `type` 属性，设置不同类型的进度条，默认是 `line`。

```vue
<template>
  <Progress :percent="percent" />
  <Progress type="circle" :percent="percent" style="margin: 20px 0" />
  
  <Space block>
    <Button @click="handleDecrease">decrease</Button>
    <Button @click="handleIncrease">increase</Button>
  </Space>
</template>

<script setup>
  import { Progress, Button, Space } from '@xhs/delight'
  import { ref } from 'vue'

  const percent = ref(10)

  const handleIncrease = () => {
    if (percent.value < 100) {
      percent.value += 10
    }
  }

  const handleDecrease = () => {
    if (percent.value >= 10) {
      percent.value -= 10
    }
  }
</script>

```

##    不同大小

```vue
<template>
  <Progress :percent="percent" />
  <Progress size="large" style="margin: 20px 0" :percent="percent" />

  <div class="d-demo-circle">
    <Progress type="circle" :percent="percent" />
    <Progress size="small" type="circle" :show-info="false" :percent="percent" />
    <Progress size="mini" type="circle" :show-info="false" :percent="percent" />
  </div>

  <Space block>
    <Button @click="handleDecrease">decrease</Button>
    <Button @click="handleIncrease">increase</Button>
  </Space>
</template>

<script setup>
  import { Progress, Button, Space } from '@xhs/delight'
  import { ref } from 'vue'

  const percent = ref(10)

  const handleIncrease = () => {
    if (percent.value < 100) {
      percent.value += 10
    }
  }

  const handleDecrease = () => {
    if (percent.value >= 10) {
      percent.value -= 10
    }
  }
</script>

<style scoped>
  .d-demo-circle {
    display: flex;
    align-items: center;
    margin: 20px 0;
  }
  .d-demo-circle .d-progress {
    margin-right: 20px
  }
</style>

```

##    自定义文字格式

```vue
<template>
  <Progress 
    :percent="percent"
    :format="(percentage) => percentage + '👍'"
  />

  <Progress type="circle" :percent="percent" style="margin: 20px 0">
    <template #text>
      {{ percent + '👍' }}
    </template>
  </Progress>
  
  <Space block>
    <Button @click="handleDecrease">decrease</Button>
    <Button @click="handleIncrease">increase</Button>
  </Space>
</template>

<script setup>
  import { Progress, Button, Space } from '@xhs/delight'
  import { ref } from 'vue'

  const percent = ref(10)

  const handleIncrease = () => {
    if (percent.value < 100) {
      percent.value += 10
    }
  }

  const handleDecrease = () => {
    if (percent.value >= 10) {
      percent.value -= 10
    }
  }
</script>

```

##    圆圈可以自由定义大小

在 `type="circle"` 的情况下，可以手动传入 `width` 进行控制圆圈直径

```vue
<template>
  <div class="demo-circle-width">
    <Progress :width="width" type="circle" :percent="percent" />
  </div>  
  
  <Space block>
    <Button @click="handleDecrease">decrease</Button>
    <Button @click="handleIncrease">increase</Button>
  </Space>
</template>

<script setup>
  import { Progress, Button, Space } from '@xhs/delight'
  import { ref } from 'vue'

  const width = ref(72)
  const percent = ref(30)

  const handleIncrease = () => {
    width.value += 10
  }

  const handleDecrease = () => {
    if (width.value >= 80) {
      width.value -= 10
    }
  }
</script>

<style>
  .demo-circle-width {
    height: calc(100% - 100px);
    display: flex;
    align-items: center;
    justify-content: center
  }
</style>

```

##    API 参考

#### Pros 属性 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|format|内容的模板函数|`function`|`(percent) => percent + %`|
|percent|百分比，必填。可选（0-100）|`number`|`0`|
|showInfo|是否显示进度数值或状态图标|`boolean`|`true`|
|size|进度条的大小。line类型：default是高度为4px，large高度为8px，circle类型：mini 宽度为 20px，small 宽度为 22px，default 为 72px|`'large` \| `'default'` \| `'small'` \| `'mini'`|`'default'`|
|type|进度条类型|`'line'` \| `'circle'`|`'line'`|

`type="circle"`

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|width|圆形进度条画布宽度，单位 px，优先级高于size属性|number|0|

#### Slots 插槽 |插槽|说明|
| :- | :- |
| text | 自定义文字内容 |

