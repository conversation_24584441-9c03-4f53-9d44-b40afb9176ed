

##    基本使用

通过 `items` 或者在 `slots.default` 中插入 `BreadcrumbItem` 组件设置选项：

```vue
<template>
  <Space direction="vertical" align="start">
    <Breadcrumb>
      <template v-for="title of ['菜单 A', '菜单 B', '菜单 C']" :key="title">
        <BreadcrumbItem :title="title"/>
      </template>
      <BreadcrumbItem title="菜单 D"/>
    </Breadcrumb>
    <Breadcrumb
      :items="[
        { title: '菜单 A' },
        { title: '菜单 B' },
        { title: '菜单 C' },
        { title: '菜单 D' },
      ]"
    />
  </Space>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb, BreadcrumbItem } from '@xhs/delight'
</script>
```

##    尺寸

通过 `size` 设置尺寸：

```vue
<template>
  <Space direction="vertical" align="start">
    <Breadcrumb size="small" :items="items"/>
    <Breadcrumb :items="items"/>
    <Breadcrumb size="large" :items="items"/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb } from '@xhs/delight'

  const items = [
    { title: '菜单 A' },
    { title: '菜单 B' },
    { title: '菜单 C' },
    { title: '菜单 D' },
  ]
</script>
```

##    图标

通过 `icon` 设置 `item` 的图标：

```vue
<template>
  <Breadcrumb :items="items"/>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'

  const items = [
    { title: '菜单 A', icon: Fire },
    { title: '菜单 B', icon: { icon: Fire, theme: 'filled', color: 'red-6' } },
    { title: '菜单 C' },
    { title: '菜单 D' },
  ]
</script>
```

##    路由跳转

通过 `to` 设置 `item` 的路由跳转：

```vue
<template>
  <Breadcrumb :items="items"/>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb } from '@xhs/delight'

  const items = [
    { title: 'Button 按钮', to: { name: 'delight-button' } },
    { title: 'Icon 图标', to: { name: 'delight-icon' } },
    { title: 'Typography 排版', to: { name: 'delight-text' } },
    { title: 'Breadcrumb 面包屑', to: { name: 'delight-breadcrumb' } },
  ]
</script>
```

### 其他路由 通过 `overlay` 设置 `item` 的其他路由：

```vue
<template>
  <Breadcrumb :items="items"/>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb } from '@xhs/delight'

  const overlay = [
    { title: 'Button 按钮', to: { name: 'delight-button' } },
    { title: 'Icon 图标', to: { name: 'delight-icon' } },
    { title: 'Typography 排版', to: { name: 'delight-text' } },
    { title: 'Breadcrumb 面包屑', to: { name: 'delight-breadcrumb' } },
  ]

  const items = [
    { title: '菜单 A' },
    { title: '菜单 B', overlay },
    { title: '菜单 C' },
    { title: '菜单 D' },
  ]
</script>
```

##    分隔符

通过 `separator` 或  `slots.separator` 设置分隔符：
- 如果在 `item` 中设置的 `separator` 仅对当前选项生效

```vue
<template>
  <Space direction="vertical" align="start">
    <Breadcrumb separator="->" :items="items1"/>
    <Breadcrumb :items="items1">
      <template #separator>-></template>
    </Breadcrumb>
    <Breadcrumb :items="items2"/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb } from '@xhs/delight'

  const items1 = [
    { title: '菜单 A' },
    { title: '菜单 B' },
    { title: '菜单 C' },
    { title: '菜单 D' },
  ]
  const items2 = [
    { title: '菜单 A', separator: ':' },
    { title: '菜单 B' },
    { title: '菜单 C' },
    { title: '菜单 D' },
  ]
</script>
```

##    最大选项长度

通过 `maxItemWidth` 设置最大选项长度：

```vue
<template>
  <Breadcrumb :max-item-width="100" :items="items"/>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb } from '@xhs/delight'

  const items = [
    { title: '菜单 A' },
    { title: '菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜菜单 B' },
    { title: '菜单 C' },
    { title: '菜单 D' },
  ]
</script>
```

##    最大选项数量

通过 `maxItemCount` 设置最大选项数量：
- 目前仅支持通过 `items` 配置选项时设置最大选项数量

```vue
<template>
  <Breadcrumb :max-item-count="6" :items="items"/>
  <Breadcrumb :max-item-count="5" :items="items"/>
  <Breadcrumb :max-item-count="4" :items="items"/>
  <Breadcrumb :max-item-count="3" :items="items"/>
  <Breadcrumb :max-item-count="2" :items="items"/>
</template>

<script setup lang="ts">
  import { Space, Breadcrumb } from '@xhs/delight'

  const items = [
    { title: '菜单 A' },
    { title: '菜单 B' },
    { title: '菜单 C' },
    { title: '菜单 D' },
    { title: '菜单 E' },
    { title: '菜单 F' },
    { title: '菜单 G' },
  ]
</script>
```

##    API 参考

```

interface BreadcrumbItem {
  icon?: (p: IconProps) => string
  title?: string
  separator?: string
  to?: RouteLocationRaw
  overlay?: MenuItem[]
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
}

```

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|items|面包屑的选项|BreadcrumbItem[\]|[]|
|size|面包屑的尺寸|'small' &#124; 'default' &#124; 'large'|'default'|
|separator|面包屑的分隔符|string|'/'|
|maxItemCount|面包屑选项的最大数量，超出数量后中间选项会被折叠|number|-|
|maxItemWidth|面包屑选项的最大宽度，超出宽度后会被省略并通过 `Tooltip` 提示|number|-|

### Breadcrumb 插槽 |插槽|说明|
| :- | :- |
|default|面包屑的选项|

### BreadcrumbItem API 参考 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|icon|选项的[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|(p: IconProps) => string|-|
|title|选项的标题|string|-|
|separator|面包屑的分隔符，仅对当前选项生效，默认跟随外层配置|string|-|
|to|路由跳转地址|RouteLocationRaw|-|
|overlay|其他路由（通过菜单组件实现，参数与 `MenuItem` 一致）|MenuItem[\]|-|

### BreadcrumbItem 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
