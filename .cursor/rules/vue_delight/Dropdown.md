

❗️❗️❗️ 由于通过 `Teleport` 挂载，下拉菜单样式 不能为 `scoped

##    基本使用

通过传入 `target` 或包裹 `slots.default` 设置下拉菜单的目标元素，通过 `options` 或者在 `slots.options` 中插入 `Options` 组件设置下拉选项：
- 若 `options` 中的 `option` 为 `string`，则自动扩展为 `{ label: option, value: option }`
- 增加 auto-close 属性，选择完会自动关闭下拉框

```vue
<template>
  <Space>
    <Button ref="button">hover</Button>
    <Dropdown :target="button?.$el" auto-close>
      <template #options>
        <Option label="option1" />
        <Option label="option2">
          <Option label="option3"/>
          <Option label="option4"/>
        </Option>
      </template>
    </Dropdown>
    <Dropdown auto-close>
      <Button>hover</Button>
      <template #options>
        <Option label="option1" value="option1" />
        <Option label="option2" value="option2" >
          <Option label="option3" value="option3" />
          <Option label="option4" value="option4" />
        </Option>
      </template>
    </Dropdown>
    <Dropdown :options="options" auto-close trigger="click">
      <Button>click</Button>
    </Dropdown>
    <Dropdown :options="['option1', 'option2']" auto-close>
      <Button>hover</Button>
    </Dropdown>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Dropdown, Option, Button } from '@xhs/delight'

  const button = ref()

  const options = [
    {
      description: '小标题1'
    },
    {
      label: 'option1',
      value: 'a',
      tooltip: 'xxxx',
      tooltipTheme: 'light',
      onClick: () => { console.log('选择了 option1') }
    },
    {
      label: 'option2',
      value: 'b',
      tooltip: 'xxxx',
      tooltipPlacement: 'bottom',
      children: [
        {
          label: 'option3',
          value: 'c'
        },
        {
          label: 'option4',
          value: 'd'
        },
      ]
    },
    {
      description: '小标题2'
    },
    {
      label: 'option3',
      value: 'c',
    },
  ]
</script>
```

##    尺寸

通过 `size` 设置下拉菜单的尺寸：

```vue
<template>
  <Dropdown :options="options" size="small">
    <Button>small</Button>
  </Dropdown>

  <div style="height: var(--size-space-default)"/>

  <Dropdown :options="options">
    <Button>default</Button>
  </Dropdown>

  <div style="height: var(--size-space-default)"/>

  <Dropdown :options="options" size="large">
    <Button>large</Button>
  </Dropdown>
</template>

<script setup lang="ts">
  import { Dropdown, Option, Button } from '@xhs/delight'

  const options = [
    {
      label: 'option1',
    },
    {
      label: 'option2',
    },
  ]
</script>
```

##    位置

通过 `placement` 设置下拉菜单展示的相对位置：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Dropdown :placement="placement">
        <Button block>{{ placement }}</Button>
        <template #options>
          <Option label="option1"/>
          <Option label="option2"/>
        </template>
      </Popover>
    </div>
  </Grid>
</template>

<script setup lang="ts">
  import { Grid, Dropdown, Option, Button } from '@xhs/delight'

  const placements = ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end']
</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    间距

通过 `offset` 设置下拉菜单与 `slots.default` 的间距：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Dropdown :placement="placement" :offset="12">
        <Button block>{{ placement }}</Button>
        <template #options>
          <Option label="option1"/>
          <Option label="option2"/>
        </template>
      </Popover>
    </div>
  </Grid>
</template>

<script setup lang="ts">
  import { Grid, Dropdown, Option, Button } from '@xhs/delight'

  const placements = ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end']
</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    触发方式

通过 `trigger` 设置展示下拉菜单的触发方式：

```vue
<template>
  <Dropdown>
    <Button>hover</Button>
    <template #options>
      <Option label="option1"/>
      <Option label="option2"/>
    </template>
  </Dropdown>

  <div style="height: var(--size-space-default)"/>

  <Dropdown trigger="click" @click="handle">
    <Button>click</Button>
    <template #options>
      <Option label="option1" disabled/>
      <Option label="option2"/>
    </template>
  </Dropdown>

  <div style="height: var(--size-space-default)"/>

  <Dropdown 
    trigger="manual" 
    :visible="visible" 
    :options="['option1', 'option2']"
    @click:dropdown="handleClose"
  >
    <Button @click="handleOpen">manual</Button>
  </Dropdown>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Dropdown, Option, Button } from '@xhs/delight'

  const visible = ref(false)

  const handleOpen = () => {
    visible.value = true
  }

  const handleClose = () => {
    visible.value = false
  }

  const handle = () => {
    console.log('manual click');
  }
</script>
```

##    自动关闭

```vue
<template>
  <Dropdown auto-close>
    <Button>hover</Button>
    <template #options>
      <Option label="option1"/>
      <Option label="option2"/>
    </template>
  </Dropdown>

  <div style="height: var(--size-space-default)"/>

  <Dropdown trigger="click" auto-close>
    <Button>click</Button>
    <template #options>
      <Option label="option1"/>
      <Option label="option2"/>
    </template>
  </Dropdown>
</template>

<script setup>
  import { Dropdown, Option, Button } from '@xhs/delight'
</script>
```

##    可选

通过给 `options` 或者 `slots.options` 中的 `Options` 设置 `value` 将其转化为可选选项：

```vue
<template>
  <Dropdown v-model="dropdown1" size="large">
    <Button>selected: {{ dropdown1 }}</Button>
    <template #options>
      <Option label="option1" value="option1"/>
      <Option label="option2" value="option2"/>
      <Option label="sub dropdown">
        <Option label="option3" value="option3"/>
        <Option label="option4" value="option4"/>
      </Option>
    </template>
  </Dropdown>

  <div style="height: var(--size-space-default)"/>

  <Dropdown v-model="dropdown2" :options="options" size="large" >
    <Button>selected: {{ dropdown2 }}</Button>
  </Dropdown>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Dropdown, Option, Button } from '@xhs/delight'

  const dropdown1 = ref('option1')
  const dropdown2 = ref('option2')

  const options = [
    {
      label: 'option1',
      value: 'option1',
    },
    {
      label: 'option2',
      value: 'option2',
    },
    {
      label: 'sub dropdown',
      children: [
        {
          label: 'option3',
          value: 'option3',
        },
        {
          label: 'option4',
          value: 'option4',
        },
      ]
    },
  ]
</script>
```

##    多选

通过 `multiple` 设置下拉菜单为可多选：

```vue
<template>
  <Dropdown v-model="dropdown1" size="large" multiple>
    <Button>selected: {{ dropdown1 }}</Button>
    <template #options>
      <Option label="option1" value="option1"/>
      <Option label="option2" value="option2"/>
      <Option label="sub dropdown">
        <Option label="option2" value="option2"/>
        <Option label="option3" value="option3"/>
      </Option>
    </template>
  </Dropdown>

  <div style="height: var(--size-space-default)"/>

  <Dropdown v-model="dropdown2" :options="options" size="large" multiple>
    <Button>selected: {{ dropdown2 }}</Button>
  </Dropdown>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Dropdown, Option, Button } from '@xhs/delight'

  const dropdown1 = ref(['option1'])
  const dropdown2 = ref(['option2'])

  const options = [
    {
      label: 'option1',
      value: 'option1',
    },
    {
      label: 'option2',
      value: 'option2',
    },
    {
      label: 'sub dropdown',
      children: [
        {
          label: 'option2',
          value: 'option2',
        },
        {
          label: 'option3',
          value: 'option3',
        },
      ]
    },
  ]
</script>
```

##    Option 属性

通过 `Option` 对应的属性设置下拉菜单内容：

```vue
<template>
  <Dropdown v-model="dropdown" :options="options">
    <Button>selected: {{ dropdown }}</Button>
  </Dropdown>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Dropdown, Option, Button } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const dropdown = ref('option2')

  const options = [
    {
      icon: Home,
      label: 'option1',
      value: 'option1',
      tooltip: '选项的提示信息',
    },
    {
      label: 'option2',
      value: 'option2',
      disabled: true,
    },
    {
      divider: true,
    },
    {
      label: 'disabled',
      disabled: true,
      children: [
        {
          label: 'option3',
          value: 'option3',
        },
      ]
    },
    {
      label: 'click to show',
      placement: 'bottom-start',
      offset: 16,
      trigger: 'click',
      size: 'large',
      children: [
        {
          label: 'option4',
          value: 'option4',
        },
      ]
    },
  ]
</script>
```

##    Option 自定义 icon

通过 `Option` 的 icon 属性设置自定义 icon：

```vue
<template>
  <Dropdown v-model="dropdown" :options="options">
    <Button>selected: {{ dropdown }}</Button>
  </Dropdown>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Dropdown, Option, Button } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const dropdown = ref('option2')
  const CustomIcon = (
    <svg width="100%" height="100%" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.99959 10.8333C10.4598 10.8333 10.8329 10.4602 10.8329 9.99996C10.8329 9.53972 10.4598 9.16663 9.99959 9.16663C9.53936 9.16663 9.16626 9.53972 9.16626 9.99996C9.16626 10.4602 9.53936 10.8333 9.99959 10.8333Z" fill="currentColor" fill-opacity="0.9"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M14.419 5.58053C14.6489 5.81037 14.7241 6.15288 14.6117 6.45788L12.5493 12.0558C12.465 12.2848 12.2845 12.4653 12.0555 12.5497L6.45754 14.6121C6.15254 14.7244 5.81004 14.6492 5.5802 14.4194C5.35036 14.1895 5.27513 13.847 5.3875 13.542L7.4499 7.9441C7.53426 7.7151 7.71477 7.5346 7.94376 7.45023L13.5417 5.38784C13.8467 5.27547 14.1892 5.35069 14.419 5.58053ZM8.88084 8.88117L7.5756 12.424L11.1184 11.1187L12.4236 7.57593L8.88084 8.88117Z" fill="currentColor" fill-opacity="0.9"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M0.833008 10C0.833008 4.93743 4.93706 0.833374 9.99967 0.833374C15.0623 0.833374 19.1663 4.93743 19.1663 10C19.1663 15.0626 15.0623 19.1667 9.99967 19.1667C4.93706 19.1667 0.833008 15.0626 0.833008 10ZM9.99967 2.50004C5.85754 2.50004 2.49967 5.8579 2.49967 10C2.49967 14.1422 5.85754 17.5 9.99967 17.5C14.1418 17.5 17.4997 14.1422 17.4997 10C17.4997 5.8579 14.1418 2.50004 9.99967 2.50004Z" fill="currentColor" fill-opacity="0.9"/>
    </svg>
  )
  const options = [
    {
      icon: Home,
      label: 'option1',
      value: 'option1',
      tooltip: '选项的提示信息',
    },
    {
      label: 'option2',
      value: 'option2',
      disabled: true,
    },
    {
      icon: CustomIcon,
      label: 'option3',
      value: 'option3',
      tooltip: '选项的提示信息',
      children: [
        {
          label: 'option5',
          icon: Home,
          value: 'option5',
        },
        {
          label: 'option4',
          value: 'option4',
        },
      ]
    },
  ]
</script>
```

##    独立使用

通过 `individual` 设置独立使用：

```vue
<template>
  <Dropdown v-model="dropdown" :options="options" size="large" individual/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Dropdown, Option } from '@xhs/delight'

  const dropdown = ref('option2')

  const options = [
    {
      label: 'option1',
      value: 'option1',
    },
    {
      label: 'option2',
      value: 'option2',
    },
    {
      label: 'sub dropdown',
      children: [
        {
          label: 'option3',
          value: 'option3',
        },
        {
          label: 'option4',
          value: 'option4',
        },
      ]
    },
  ]
</script>
```

### 自定义 Option、虚拟滚动 参考 [Select 选择器](http://delight.devops.xiaohongshu.com/delight/cmp/select) ##    API 参考

|属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|target|下拉菜单的目标元素|HTMLElement|-|
|placement|下拉菜单展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'bottom'|
|offset|下拉菜单与目标元素的间距|number|4|
|trigger|下拉菜单展示的触发方式|'hover' &#124; 'click' &#124; 'manual'|'hover'|
|visible|手动控制下拉菜单是否展示，仅当 `trigger` 为 `manual` 时生效|boolean|false|
|validate|判断下拉菜单是否需要展示，当需要判断条件再展示下拉菜单时使用。比如 `Option` 只有出现文字被省略时再展示 `Tooltip`|() => boolean|-|
|size|下拉菜单的尺寸|'small' &#124; 'default' &#124; 'large'|'default'|
|options|下拉菜单中的选项|Option[\]|[]|
|multiple|下拉菜单是否可以多选|boolean|false|
|modelValue (v-model)|下拉菜单选中值|string &#124; number &#124; (string &#124; number)[]|-|
|revertable|下拉菜单是否可以反选（单选时生效）|boolean|false|
|auto-close|选择完自动关闭（仅仅针对 `trigger` 在 `hover` 和 `click` 时生效）|boolean|false|
|stopPropagation|是否阻止点击下拉框事件冒泡|boolean|false|
|mark|`mark` 如果是下拉菜单中的选项 `label` 的子字符串，`label` 中对应文字会被高亮|string|-|
|maxWidth|下拉菜单最大宽度，过长的内容会被省略|number|-|
|maxHeight|下拉菜单最大高度，超过会滚动|number|-|
|individual|下拉菜单独立使用|boolean|false|
|virtualize|虚拟滚动|boolean|false|
|virtualizeOptions|虚拟滚动参数，设置选项的高、选项间的间距、真实渲染的最大选项数|&#123; optionHeight?: number; optionGap?: number; renderLimit?: number; &#125;|&#123; optionHeight: 32, optionGap: 8, renderLimit: 100 &#125;|

### Dropdown 事件 |事件 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|change|下拉菜单选中值变化的回调事件，不能排除存在 `value` 相同的选项，`onChange` 返回的永远是一个数组|(e: Option[]) => void|-|
|click|鼠标单击的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素、下拉菜单及所有子孙下拉菜单）|(e: MouseEvent) => void|-|
|click:target|鼠标单击的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素）|(e: MouseEvent) => void|-|
|click:dropdown|鼠标单击的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于下拉菜单及所有子孙下拉菜单）|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素、下拉菜单及所有子孙下拉菜单）|(e: MouseEvent) => void|-|
|mousedown:target|鼠标按下的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素）|(e: MouseEvent) => void|-|
|mousedown:dropdown|鼠标按下的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于下拉菜单及所有子孙下拉菜单）|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素、下拉菜单及所有子孙下拉菜单）|(e: MouseEvent) => void|-|
|mouseup:target|鼠标抬起的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素）|(e: MouseEvent) => void|-|
|mouseup:dropdown|鼠标抬起的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于下拉菜单及所有子孙下拉菜单）|(e: MouseEvent) => void|-|

### Dropdown 插槽 |插槽 | 说明|
| :- | :- |
|default|下拉菜单的目标元素|
|overlay|自定义下拉菜单选项展示内容|
|options|下拉菜单中的选项，只接受 Option 组件|
|optionPrefix|自定义下拉菜单选项的前缀（仅在配合使用 options 插槽情况下生效）|
|optionContent|自定义下拉菜单选项的内容区域（仅在配合使用 options 插槽情况下生效）|
|optionSuffix|自定义下拉菜单选项的后缀（仅在配合使用 options 插槽情况下生效）|
|top|下级 下拉菜单中的顶部附加项，随内容滚动|
|bottom|下级 下拉菜单中的底部附加项，随内容滚动|
|stickBottom|下级 下拉菜单中的底部附加项，不随内容滚动|
|stickTop|下拉菜单中的顶部附加项，不随内容滚动|
|top|下拉菜单中的顶部附加项，随内容滚动|
|bottom|下拉菜单中的底部附加项，随内容滚动|
|stickBottom|下拉菜单中的底部附加项，不随内容滚动|

### Option API 参考 |属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|divider|是否为分割线（如果设置为 `true` 其他属性将不再生效）|boolean|false|
|id|选项的唯一键（下拉菜单中使用了 `label` + `value` 作为选项的默认唯一键，但当可能存在重复时可以额外声明 `id` 来保证内容发生变化时 `dom` 被正确更新）|string|-|
|icon|选项的[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|(p: IconProps) => string &#124; JSX.Element &#124; VNode|-|
|name|选项的标题，优先使用 `label`|string|-|
|label|选项的标题|string|-|
|value|选项的值|string &#124; number|-|
|tooltip|选项的提示信息|string|-|
|tooltipPlacement|tooltip展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'top'|
|tooltipTheme|主题|'dark' &#124; 'light'|'dark'|
|mark|`mark` 如果是选项 `label` 的子字符串，`label` 中对应文字会被高亮|string|-|
|extra|自定义需要通过 `option` 携带的数据，用法参考 `Select` 的 `自定义 Tag`|object|-|
|diabled|禁用状态|boolean|false|
|placement|下级 下拉菜单展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'right-start'|
|offset|下级 下拉菜单与目标元素的间距|number|0|
|trigger|下级 下拉菜单展示的触发方式|'hover' &#124; 'click'|'hover'|
|size|下级 下拉菜单的尺寸|'small' &#124; 'default' &#124; 'large'|'default'|
|children|下级 下拉菜单中的选项|Option[\]|[]|
|maxDropdownWidth|下级 下拉菜单最大宽度，过长的内容会被省略|number|-|
|maxDropdownHeight|下级 下拉菜单最大高度，超过会滚动|number|-|
|dropdownClass|下级 下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|dropdownStyle|下级 下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|

### Option 事件 |事件 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### Option 插槽 |插槽 | 说明|
| :- | :- |
|option|自定义选项展示内容|
|optionPrefix|自定义选项的前缀|
|optionContent|自定义选项的内容区域|
|optionSuffix|自定义选项的后缀|
|default|下级 下拉菜单中的选项，只接受 Option 组件|
|stickTop|下级 下拉菜单中的顶部附加项，不随内容滚动|
|top|下级 下拉菜单中的顶部附加项，随内容滚动|
|bottom|下级 下拉菜单中的底部附加项，随内容滚动|
|stickBottom|下级 下拉菜单中的底部附加项，不随内容滚动|