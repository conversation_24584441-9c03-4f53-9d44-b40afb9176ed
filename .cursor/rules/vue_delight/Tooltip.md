

❗️❗️❗️ 由于通过 `Teleport` 挂载，文字气泡样式 不能为 `scoped

##    基本使用

通过传入 `target` 或包裹 `slots.default`，将 `content` 或 `slots.content` 中的内容通过浮层的方式在 `slots.default` 周围展示，左侧为设置 `content` 属性的用法，右侧为设置 `slots.content` 插槽的用法：

```vue
<template>
  <Space>
    <Tooltip content="tooltip content">
      <Button>hover</Button>
    </Tooltip>

    <Tooltip>
      <Button>hover</Button>
      <template #content>
        tooltip content
      </template>
    </Tooltip>

    <Button ref="button">hover</Button>
    <Tooltip :target="button?.$el" content="tooltip content"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tooltip, Space, Button, Text } from '@xhs/delight'

  const button = ref()
</script>
```

##    主题

通过`theme` 设置，有`dark`和`light`两种，默认为`dark`。

```vue
<template>
  <Space>
    <Tooltip content="tooltip content">
      <Button>hover</Button>
    </Tooltip>

    <Tooltip content="tooltip content" theme='light'>
      <Button>hover</Button>
    </Tooltip>  
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tooltip, Space, Button, Text } from '@xhs/delight'

  const button = ref()
</script>
```

##    背景颜色

通过`color` 设置，可以设置background-color的任意值

```vue
<template>
  <Space direction="vertical" align="start">
    <Space>
      <Tooltip v-for="color in colors" :key="color" :content="color" :color="color">
        <Button>{{ color }}</Button>
      </Tooltip>
    </Space>
    <Space>
      <Tooltip v-for="color in customColors" :key="color" :content="color" :color="color">
        <Button>{{ color }}</Button>
      </Tooltip>
    </Space>
  </Space>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'
  import { Tooltip, Space, Button, Text } from '@xhs/delight'

  const colors = [
    'var(--color-primary)',
    'var(--color-warning)',
    'var(--color-danger)',
    'var(--color-success)',
    'var(--color-cyan-6)',
    'var(--color-green-6)',
  ];
  const customColors = ['#f50', '#2db7f5', '#87d068', '#108ee9'];
</script>
```

##    位置

通过 `placement` 设置浮层展示的相对位置：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Tooltip :placement="placement" :content="placement">
        <Button block>{{ placement }}</Button>
      </Tooltip>
    </div>
  </Grid>
</template>

<script setup lang="ts">
  import { Grid, Tooltip, Button } from '@xhs/delight'

  const placements = ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end']
</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    间距

通过 `offset` 设置浮层与 `slots.default` 的间距：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Tooltip :offset="16" :placement="placement" :content="placement">
        <Button block>{{ placement }}</Button>
      </Tooltip>
    </div>
  </Grid>
</template>

<script setup lang="ts">
  import { Grid, Tooltip, Button } from '@xhs/delight'

  const placements = ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end']
</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    箭头

通过 `arrow` 设置浮层指向 `slots.default` 的箭头，`Tooltip` 默认展示箭头：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Tooltip :placement="placement" :content="placement" :arrow="false">
        <Button block>{{ placement }}</Button>
      </Tooltip>
    </div>
  </Grid>
</template>

<script setup lang="ts">
  import { Grid, Tooltip, Button } from '@xhs/delight'

  const placements = ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end']
</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    触发方式

通过 `trigger` 设置展示浮层的触发方式：

```vue
<template>
  <Tooltip content="tooltip content">
    <Button>hover</Button>
  </Tooltip>

  <div style="height: var(--size-space-large)"/>

  <Tooltip trigger="click" content="tooltip content">
    <Button>click</Button>
  </Tooltip>

  <div style="height: var(--size-space-large)"/>
  
  <Tooltip trigger="manual" :visible="manualVisible">
    <Button>manual</Button>
    <template #content>
      <Button type="danger" @click="manualHide">click here to hide</Button>
    </template>
  </Tooltip>

  <div style="height: var(--size-space-default)"/>

  <Button type="primary" @click="manualShow">click here to show</Button>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tooltip, Button } from '@xhs/delight'

  const manualVisible = ref(false)
  
  function manualShow() {
    manualVisible.value = true
  }
  
  function manualHide() {
    manualVisible.value = false
  }
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|theme|主题|'dark' &#124; 'light'|'dark'|
|color|背景颜色|'string'|-|
|content|浮层的内容|string|-|
|target|浮层的目标元素|HTMLElement|-|
|placement|浮层展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'top'|
|offset|浮层与目标元素的间距|number|4|
|arrow|浮层展示指向目标元素的箭头|boolean|true|
|trigger|浮层展示的触发方式|'hover' &#124; 'click' &#124; 'manual'|'hover'|
|visible|手动控制浮层是否展示，仅当 `trigger` 为 `manual` 时生效|boolean|false|
|validate|判断浮层是否需要展示，当需要判断条件再展示浮层时使用。比如 `Tag` 只有出现文字被省略时再展示 `Tooltip`|() => boolean|-|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|onChange|浮层展示状态变化的回调事件|(e: boolean) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|浮层的目标元素|
|content|浮层的内容|

### 注意 请确保 Tooltip 的子元素能接受 `mouseenter`、`mouseleave`、`click` 事件。