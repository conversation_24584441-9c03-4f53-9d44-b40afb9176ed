

##    基本使用

通过 `pageSize` 、 `pageSizeOptions` 、 `total` 配置分页：
 - `pageSize` 配置每页的数据数量，默认 `10`
 - `pageSizeOptions` 配置可选的每页的数据数量，默认 `[10, 20, 30, 40, 50]`
 - `total` 配置数据的总数量

```vue
<template>
  <Pagination v-model="value1" :total="300"/>
  <br/>
  <Text>当前页：{{ value1 }}</Text>
  <br/>
  <br/>
  <Pagination v-model="value2" :page-size="15" :page-size-options="[15, 35, 55]" :total="300"/>
  <br/>
  <Text>当前页：{{ value2 }}</Text>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Pagination, Text } from '@xhs/delight'

  const value1 = ref(2)
  const value2 = ref(2)
</script>
```

##    展示方式

通过 `type` 设置分页的展示方式，并可通过额外设置 `simplify` 简化展示：
 - `default` 默认展示（简化展示时仅展示当前页附近的 5 个页码）
 - `simple` 简洁展示（简化展示时不再展示总页数）
 - `small` 小号展示（简化展示时仅展示当前页附近的 5 个页码）

```vue
<template>
  <Pagination :total="300"/>
  <br/>
  <Pagination :total="300" simplify/>
  <br/>
  <Pagination type="simple" :total="300"/>
  <br/>
  <Pagination type="simple" :total="300" simplify/>
  <br/>
  <Pagination type="small" :total="300"/>
  <br/>
  <Pagination type="small" :total="300" simplify/>
</template>

<script setup lang="ts">
  import { Pagination } from '@xhs/delight'
</script>
```

##    对齐方式

通过 `align` 设置分页的对齐方式：
 - `space-between` 默认对齐方式，分首尾展示（小号不支持改为默认行首对齐，因为只有一段内容）
 - `start` 行首对齐，所有内容均在首部展示
 - `end` 行尾对齐，所有内容均在尾部展示

```vue
<template>
  <Pagination :total="300"/>
  <br/>
  <Pagination :total="300" align="start"/>
  <br/>
  <Pagination :total="300" align="end"/>
  <br/>
  <Pagination type="simple" :total="300"/>
  <br/>
  <Pagination type="simple" :total="300" align="start"/>
  <br/>
  <Pagination type="simple" :total="300" align="end"/>
  <br/>
  <Pagination type="small" :total="300"/>
  <br/>
  <Pagination type="small" :total="300" align="end"/>
</template>

<script setup lang="ts">
  import { Pagination } from '@xhs/delight'
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue|分页当前页码|number|-|
|pageSize|分页每页的数据数量|number|10|
|pageSizeOptions|分页每页的数据数量的选项|number[]|[10, 20, 30, 40, 50]|
|total|分页数据的总数量|number|10|
|type|分页的展示方式|'default' &#124; 'simple' &#124; 'small'|'default'|
|simplify|简化分页的展示方式|boolean|false|
|align|分页的对齐方式|'space-between' &#124; 'start' &#124; 'end'|'space-between'|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:modelValue|分页当前页码变化的回调事件|(e: number) => void|-|
|update:pageSize|分页每页的数据数量变化的回调事件|(e: number) => void|-|
|change|pageSize 和 当前页发生变化的时候，会抛出事件|(currentPage: number, pagesize: number) => void|-|