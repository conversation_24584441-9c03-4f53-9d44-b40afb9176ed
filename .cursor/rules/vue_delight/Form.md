import { Link } from '@xhs/delight'

Delight 目前已经重构了 Form，不建议大家使用老版本的 Form，
升级手册：升级到 Form2、Menu2 注意项

## 基础表单

```vue
<template>
  <Form
    label-width="100px"
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    <FormItem label="name" name="name">
      <Input v-model="model.name" />
    </FormItem>
    <FormItem label="age" name="age">
      <Input v-model="model.age" />
    </FormItem>
  </Form>
</template>

<script setup>
import { reactive } from "vue";
import { Form2 as Form, FormItem2 as FormItem, Input } from "@xhs/delight";

const model = reactive({
  name: "",
  age: "",
});
</script>
```

## 行内表单

```vue
<template>
  <Form
    inline
    label-width="144px"
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    <FormItem label="Approved by" name="user">
      <Input v-model="model.user" placeholder="Approved by" />
    </FormItem>
    <FormItem label="Activity zone" name="region">
      <Input v-model="model.region" placeholder="Activity zone" />
    </FormItem>
    <FormItem>
      <Button type="primary" @click>Query</Button>
    </FormItem>
  </Form>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  Form2 as Form,
  FormItem2 as FormItem,
  Input,
  Button,
} from "@xhs/delight";

const model = reactive({
  user: "",
  region: "",
});
</script>
```

## 对齐方式

根据你们的设计情况，来选择最佳的标签对齐方式。

通过设置 label-position 属性可以改变表单域标签的位置，可选值为 top、left，当设为 top 时标签会置于表单域的顶部

```vue
<template>
  <SegmentControl
    v-model="labelPosition"
    :options="options"
    style="margin-bottom: 20px"
  />
  <Form
    :label-position="labelPosition"
    label-width="180px"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    <FormItem label="name">
      <Input v-model="model.name" placeholder="Approved by" />
    </FormItem>
    <FormItem label="Approved by">
      <Input v-model="model.user" placeholder="Approved by" />
    </FormItem>
    <FormItem label="Activity zone" oo>
      <Input v-model="model.region" placeholder="Activity zone" />
    </FormItem>
  </Form>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  Form2 as Form,
  FormItem2 as FormItem,
  Input,
  SegmentControl,
} from "@xhs/delight";

const labelPosition = ref("right");
const model = reactive({
  name: "",
  user: "",
  region: "",
});

const options = [
  { label: "Left", value: "left" },
  { label: "Right", value: "right" },
  { label: "Top", value: "top" },
];
</script>
```

## 表单校验

Form 组件允许你验证用户的输入是否符合规范，来帮助你找到和纠正错误。

Form 组件提供了表单验证的功能，只需为 rules 属性传入约定的验证规则，并将 FormItem 的 name 属性设置为需要验证的特殊键值即可。更多高级用法可参考 async-validator。

```vue
<template>
  <Form
    ref="formRef"
    :model="model"
    :rules="rules"
    label-width="120px"
    label-position="Left"
    :hideRequiredMark="true"
  >
    <FormItem label="name" name="name">
      <Input v-model="model.name" />
    </FormItem>
    <FormItem label="age" name="age">
      <Input v-model="model.age" />
    </FormItem>
    <FormItem label=" ">
      <Space>
        <Button type="primary" @click="submit">创建</Button>
        <Button @click="reset">重置</Button>
      </Space>
    </FormItem>
  </Form>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  Form2 as Form,
  FormItem2 as FormItem,
  Input,
  Button,
  Space,
} from "@xhs/delight";

const model = reactive({
  name: "",
  age: "",
});

const formRef = ref();

const rules = {
  name: [
    { required: true, message: "Please input Activity name", trigger: "blur" },
    { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" },
  ],
  age: { required: true, message: "age is required" },
};

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log("create");
    })
    .catch((e) => {
      console.log(e, "error");
    });
};

const reset = () => {
  formRef.value.resetFields();
};
</script>
```

## 嵌套结构校验

```vue
<template>
  <Form
    ref="formRef"
    :model="model"
    label-width="120px"
    label-position="Left"
    :hideRequiredMark="true"
  >
    <FormItem
      label="name"
      name="user.name"
      :rules="[
        {
          required: true,
          message: 'Please input Activity name',
          trigger: 'blur',
        },
        { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
      ]"
    >
      <Input v-model="model.user.name" />
    </FormItem>
    <FormItem
      label="age"
      name="user.age"
      :rules="{ required: true, message: 'age is required' }"
    >
      <Input v-model="model.user.age" />
    </FormItem>
    <FormItem label=" ">
      <Space>
        <Button type="primary" @click="submit">创建</Button>
        <Button @click="reset">重置</Button>
      </Space>
    </FormItem>
  </Form>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  Form2 as Form,
  FormItem2 as FormItem,
  Input,
  Button,
  Space,
} from "@xhs/delight";

const model = reactive({
  user: {
    name: "",
    age: "",
  },
});

const formRef = ref();

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log("create");
    })
    .catch((e) => {
      console.log(e, "error");
    });
};

const reset = () => {
  formRef.value.resetFields();
};
</script>
```

## 自定义校验规则

```vue
<template>
  <Form
    ref="formRef"
    :model="model"
    :rules="rules"
    label-width="120px"
    label-position="Left"
    :hideRequiredMark="true"
  >
    <FormItem label="Password" name="pass">
      <Input v-model="model.pass" />
    </FormItem>
    <FormItem label="Confirm" name="checkPass">
      <Input v-model="model.checkPass" type="password" />
    </FormItem>
    <FormItem label=" ">
      <Space>
        <Button type="primary" @click="submit">创建</Button>
        <Button @click="reset">重置</Button>
      </Space>
    </FormItem>
  </Form>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  Form2 as Form,
  FormItem2 as FormItem,
  Input,
  Button,
  Space,
} from "@xhs/delight";

const model = reactive({
  pass: "",
  checkPass: "",
});

const formRef = ref();

const validatePass = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("Please input the password"));
  } else {
    if (model.checkPass !== "") {
      if (!formRef.value) return;
      formRef.value.validateField("checkPass");
    }
    callback();
  }
};
const validatePass2 = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("Please input the password again"));
  } else if (value !== model.pass) {
    callback(new Error("Two inputs don't match!"));
  } else {
    callback();
  }
};

const rules = {
  pass: [{ validator: validatePass, trigger: "blur" }],
  checkPass: [{ validator: validatePass2, trigger: "blur" }],
};

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log("create");
    })
    .catch((e) => {
      console.log(e, "error");
    });
};

const reset = () => {
  formRef.value.resetFields();
};
</script>
```

## 自定义表单控件

```vue
<template>
  <Form
    ref="formRef"
    :model="model"
    :rules="rules"
    label-width="120px"
    label-position="Left"
    :hideRequiredMark="true"
  >
    <FormItem label="Name" name="name">
      <Input v-model="model.name" />
    </FormItem>
    <FormItem label="Grade" name="grade">
      <div class="custom-form">
        <div>科目：<Input v-model="model.grade.course" /></div>
        <div style="margin-top: 10px">
          分数：<Input v-model="model.grade.score" />
        </div>
      </div>
    </FormItem>
    <FormItem label=" ">
      <Space>
        <Button type="primary" @click="submit">创建</Button>
        <Button @click="reset">重置</Button>
      </Space>
    </FormItem>
  </Form>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  Form2 as Form,
  FormItem2 as FormItem,
  Input,
  Button,
  Space,
} from "@xhs/delight";

const formRef = ref();
const model = reactive({
  name: "",
  grade: {
    course: "",
    score: "",
  },
});

const check = (rule, value, callback) => {
  if (!value.course || !value.score) {
    callback(new Error("grade must not be empty"));
  } else if (value.score < 60) {
    return callback(new Error("The grade is too low."));
  } else {
    callback();
  }
};

const rules = {
  name: [
    { required: true, message: "Please input Activity name", trigger: "blur" },
  ],
  grade: [{ validator: check, trigger: "blur" }],
};

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log("create");
    })
    .catch((e) => {
      console.log(e, "error");
    });
};

const reset = () => {
  formRef.value.resetFields();
};
</script>

<style>
.custom-form > div {
  display: flex;
  align-items: center;
}
</style>
```

## 添加/删除表单项

除了一次通过表单组件上的所有验证规则外。您也可以动态地通过验证规则或删除单个表单字段的规则。

```vue
<template>
  <Form
    ref="formRef"
    :model="dynamicValidateForm"
    label-width="120px"
    label-position="Left"
    :hideRequiredMark="true"
  >
    <FormItem
      name="email"
      label="Email"
      :rules="[
        {
          required: true,
          message: 'Please input email address',
          trigger: 'blur',
        },
        {
          type: 'email',
          message: 'Please input correct email address',
          trigger: ['blur', 'change'],
        },
      ]"
    >
      <Input v-model="dynamicValidateForm.email" />
    </FormItem>
    <FormItem
      v-for="(domain, index) in dynamicValidateForm.domains"
      :key="domain.key"
      :label="'Domain' + index"
      :name="'domains.' + index + '.value'"
      :rules="{
        required: true,
        message: 'domain can not be null',
        trigger: 'blur',
      }"
    >
     <div>
        <Input v-model="domain.value" />
        <Button @click="removeDomain(domain)" style="margin-left: 20px;">Delete</Button>
     </div>
    </FormItem>
    <FormItem label=" ">
      <Space>
        <Button type="primary" @click="submitForm(formRef)">Submit</Button>
        <Button @click="addDomain">New domain</Button>
        <Button @click="resetForm(formRef)">Reset</Button>
      </Space>
    </FormItem>
  </-form>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { Form2 as Form, FormItem2 as FormItem, Input, Button, Space } from '@xhs/delight'

  const formRef = ref()
  const dynamicValidateForm = reactive({
    domains: [
      {
        key: 1,
        value: '',
      },
    ],
    email: '',
  })

  const removeDomain = (item) => {
    const index = dynamicValidateForm.domains.indexOf(item)
    if (index !== -1) {
      dynamicValidateForm.domains.splice(index, 1)
    }
  }

  const addDomain = () => {
    dynamicValidateForm.domains.push({
      key: Date.now(),
      value: '',
    })
  }

  const submitForm = (formEl) => {
    if (!formEl) return
    formEl.validate()
      .then(() => {
        console.log('submit!')
      })
      .catch(() => {
        console.log('error')
      })
  }

  const resetForm = (formEl) => {
    if (!formEl) return
    formEl.resetFields()
  }

</script>
```

## API

### Form Props |属性 | 说明 | 类型 | 默认值|

| :- | :- | :- | :- |
|hideRequiredMark|是否隐藏必填字段标签旁边的红色星号。|boolean|false|
|hideOptionalText|是否隐藏可选字段标签旁边的可选文字。|boolean|true|
|labelPosition|表单域标签的位置，当设置为 left 或 right 时，则也需要设置 label-width 属性|'left' &#124; 'right' &#124; 'top'| 'right' |
|inline|行内表单模式|boolean|false|
|model|表单数据对象|`Record`|-|
|rules|表单验证规则|FormRule|
|labelWidth|标签的长度，例如 '50px'。作为 Form 直接子元素的 form-item 会继承该值。|string &#124; number | - |
|pushDown|校验的错误信息是否占据空间|boolean|true|
|showMessage|是否显示校验错误信息|boolean|true|

### Form 方法 |方法名 | 说明 | 类型|

| :- | :- | :- |
|validate|对整个表单的内容进行验证。返回一个 Promise|() => Promise|
|clearValidate|清理某个字段的表单验证信息。|(namePath?: string &#124; string[]) => void|
|resetFields|重置该表单项，将其值重置为初始值，并移除校验结果|(namePath?: string &#124; string[]) => void|
|validateField|验证具体的某个字段。|(namePath?: string &#124; string[]) => Promise|

### FormItem Props |属性 | 说明 | 类型 | 默认值|

| :- | :- | :- | :- |
|label|标签文本|string|-|
|name|model 的键名。在定义了 validate、resetFields 的方法时，该属性是必填的|string|-|
|required|是否为必填项，如不设置，则会根据校验规则确认|boolean|false|
|help|表单元素的提示信息|string|-|
|tooltipProps|透传给 help 显示的 Tooltip 组件（具体属性请看 Tooltip 组件属性）。 需要同时开启 help 才会生效。|`Tooltip.props`|-|
|description|表单元素内容的描述|string|-|
|pushDown|校验的错误信息是否占据空间|boolean|true|
|rules|表单验证规则，更多内容可以参考 async-validator | FormItemRule &#124; FormItemRule[] |-|
|error|表单域验证错误时的提示信息。设置该值会导致表单验证状态变为 error，并显示该错误信息。|string|-|
|validateStatus|表单域验证的状态。设置该值会使表单验证状态受控|'' &#124;'error' &#124;'validating' &#124;'success' |''|

### FormItem 方法 |方法名 | 说明 | 类型|

| :- | :- | :- |
|clearValidate|对该表单项进行重置，将其值重置为初始值并移除校验结果|() => void|
|resetFields|移除该表单项的校验结果|() => void|

### FormItem 插槽 |插槽 | 说明|

| :- | :- |
|label|表单元素内容的标签|
|help|表单元素的提示信息|
|description|表单元素内容的描述|
|onError|表单元素内容的错误提示信息|
