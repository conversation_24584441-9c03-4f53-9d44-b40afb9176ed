

##    基本使用

  - 安装 `@xhs/launcher-plugin-eaglet@5.0.0`
  - 参考 [APM 埋点平台使用文档](https://wiki.xiaohongshu.com/pages/viewpage.action?pageId=162919240&ticket=ST-166a0a486898e3b1b10bfd442cbb17e7) 创建点位

用户反馈组件的使用方式：

```vue
<template>
  <div class="demo-feedback">
    <div class="demo-feedback-bottom-end">
      <Feedback
        :title="title"
        :sub-title="subTitle"
        :rate-description="rateDescription"
        :rate-texts="rateTexts"
        :message-description="messageDescription"
        @send="send"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Feedback } from '@xhs/delight'

  const title = 'Hi 小红薯 👋'
  const subTitle = '欢迎向我们提出任何反馈，帮助我们打造更好的产品。（当然，夸奖更好）'
  const rateDescription = '你觉得 Delight 整体体验如何？'
  const rateTexts = {
    0: '打个分吧',
    1: '还需努力',
    2: '马马虎虎',
    3: '继续加油',
    4: '整得不错',
    5: '真棒！'
  }
  const messageDescription = '提个小建议'

  function send(feedback) {
    const metrics = {
      type: 'InfraDelightNps',
      value: {
        score: feedback.score,
        message: feedback.message,
      },
    }

    window.eaglet?.push(metrics, 'ApmJSONTracker')
  }
</script>

<style scoped>
  .demo-feedback {
    display: flex;
    height: 100%;
  }
  .demo-feedback-bottom-end {
    margin-top: auto;
    margin-left: auto;
  }
</style>
```

##    控制发送按钮禁用状态

通过设置 isBtnDisabled 来控制发送按钮的禁用状态。

```vue
<template>
  <div class="demo-feedback">
    <div class="demo-feedback-bottom-end">
      <Feedback
        :title="title"
        :inline="true"
        :sub-title="subTitle"
        :rate-description="rateDescription"
        :rate-texts="rateTexts"
        :message-description="messageDescription"
        :is-btn-disabled="isBtnDisabled"
        @send="send"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Feedback } from '@xhs/delight'

  const isBtnDisabled = ref(false)

  const title = 'Hi 小红薯 👋'
  const subTitle = '欢迎向我们提出任何反馈，帮助我们打造更好的产品。（当然，夸奖更好）'
  const rateDescription = '你觉得 Delight 整体体验如何？'
  const rateTexts = {
    0: '打个分吧',
    1: '还需努力',
    2: '马马虎虎',
    3: '继续加油',
    4: '整得不错',
    5: '真棒！'
  }
  const messageDescription = '提个小建议'

  function send(feedback) {
    const metrics = {
      type: 'InfraDelightNps',
      value: {
        score: feedback.score,
        message: feedback.message,
      },
    }

    window.eaglet?.push(metrics, 'ApmJSONTracker')
  }
</script>

<style scoped>
  .demo-feedback {
    display: flex;
    height: 100%;
  }
  .demo-feedback-bottom-end {
    margin-top: auto;
    margin-left: auto;
  }
</style>
```

##    API 参考

通过设置 Feedback 的属性来描述反馈组件：

|属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|title|用户反馈组件的主标题|string|-|
|subTitle|用户反馈组件的副标题|string|-|
|rateDescription|用户反馈组件评分模块的描述文案|string|-|
|rateAllowHalf|用户反馈组件评分模块是否半分递增|boolean|false|
|rateTexts|用户反馈组件评分模块各分值对应的文案|Record&#60;number, string&#62;|-|
|messageDescription|用户反馈组件留言模块的描述文案|string|-|
|messagePlaceholder|用户反馈组件留言模块的占位文案|string|'请输入...'|
|sendText|用户反馈组件发送按钮上的文案|string|'发送'|
|sentText|用户反馈组件发送后的文案|string|'感谢反馈！'|
|inline|新的样式（不支持响应式）|boolean|false|
|collapseIcon|弹窗为关闭时候的 icon（delight 提供的 icon）|delight icons|-|
|expandIcon|弹窗为展开时候的 icon（delight 提供的 icon）|delight icons|-|
|isBtnDisabled| 显示控制发送按钮禁用状态 | boolean | undefined | 

### 事件 |属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|send|用户反馈组件点击发送的回调事件|(feedback: &#123; score: number; message: number &#125;): void|-|

### 插槽 |插槽|说明|scoped 属性|
| :- | :- | :- |
|extraInfo|inline 模式下星星下方的插槽| `{ score: number }` |
