

## 何时使用 需要在多个可选项中进行多选时。

比起 Select 和 TreeSelect，穿梭框占据更大的空间，可以展示可选项的更多信息。

穿梭选择框用直观的方式在两栏中移动元素，完成选择行为。

##    基本用法

```vue
<template>
  <Transfer
    v-model:selected-keys="selectedKeys"
    :data-source="mockData"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Transfer } from '@xhs/delight'

  const selectedKeys = ref(['1', '2'])

  const mockData = [];

  for (let i = 0; i < 20; i++) {
    mockData.push({
      key: i.toString(),
      title: 'content' + (i + 1),
      disabled: Math.random() >= 0.5
    });
  }

</script>
```

##    树形结构

```vue
<template>
  <Transfer
    :selectedKeys="selectedKeys"
    :data-source="transferData"
    :filterable="true"
    :filter="handleFilter"
    @change="handleChange"
  >
    <template #list="{data,onChange,selected}">
      <Tree
        :tree-data="getTreeData(data)"
        :checkable="true"
        :default-expand-all="true"
        checkStrictly
        @check="onChange"
        :checkedKeys="selected"
      />
    </template>
  </Transfer>
</template>

<script setup>
  import { ref,toRefs } from 'vue'
  import { Transfer, Tree } from '@xhs/delight'

  const selectedKeys = ref(['0-0-3-0'])
  const selected111 = ref(['0-0-3-0'])
  const handleChange = (keys) => {
    console.log('keys',keys)
  }
  const handleFilter = (v, item) => {
    return item.title.toLowerCase().includes(v.toLowerCase())
  }

  const treeData = ref([
    {
      title: '0-0',
      key: '0-0',
      children: [
        {
          title: '0-0-0',
          key: '0-0-0',
          children: [
            {
              title: '0-0-0-0',
              key: '0-0-0-0',
              isLeaf: true,
            },
            {
              title: '0-0-0-1',
              key: '0-0-0-1',
              isLeaf: true,
            },
          ],
        },
        {
          title: '0-0-1',
          key: '0-0-1',
          children: [
            {
              title: '0-0-1-0',
              key: '0-0-1-0',
              isLeaf: true,
            }
          ],
        },
        {
          title: '0-0-3',
          key: '0-0-3',
          children: [
            {
              title: '0-0-3-0',
              key: '0-0-3-0',
              isLeaf: true,
            },
            {
              title: '0-0-3-1',
              key: '0-0-3-1',
              isLeaf: true,
            },
          ],
        },

      ]
    },
  ]);

  const getTransferData = (treeData = [], transferData= []) => {
    treeData.forEach((item) => {
      if (item.children){
        transferData.push({title: item.title, key: item.key});
        getTransferData(item.children, transferData)
      } else transferData.push({title: item.title, key: item.key});
    });
    return transferData;
  };
  const transferData = getTransferData(treeData.value)

  const getTreeData = (data = []) => {
    const keys = data.map((item) => item.key);
    const travel = (_treeData = []) => {
      const treeDataSource = [];
      _treeData.forEach((item) => {
        if (item.children || keys.includes(item.key)) {
          if(item.children && item.children.length > 0){
            treeDataSource.push({
              title: item.title,
              key: item.key,
              children: travel(item.children),
            });
          } else {
            treeDataSource.push({
              title: item.title,
              key: item.key,
              isLeaf: true
            });
          }
        }
      });
      return treeDataSource;
    };
    return travel(treeData.value);
  };

</script>
```

##    标题

```vue
<template>
  <Transfer
    v-model:selected-keys="selectedKeys"
    :data-source="mockData"
    :titles="['标题1', '标题2']"
  /> 
</template>

<script setup>
  import { ref } from 'vue'
  import { Transfer } from '@xhs/delight'

  const selectedKeys = ref(['1', '2'])

  const mockData = [];

  for (let i = 0; i < 20; i++) {
    mockData.push({
      key: i.toString(),
      title: 'content' + (i + 1),
    });
  }

</script>
```

##    带搜索框

`filterable: boolean | boolean[]`  filterable 是 boolean 的时候，控制左右两个面板的搜索输入框的显示于隐藏，

为 boolean[] 的时候，可独立控制面板的搜索输入框的显示于隐藏

设置`remote`为true时开启远程筛选，此时filter函数只接收一个参数filterValue 

```vue
<template>
  <Transfer
    v-model:selected-keys="selectedKeys"
    :data-source="mockData"
    filterable
    :filter="handleFilter"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Transfer } from '@xhs/delight'

  const selectedKeys = ref([])

  const mockData = [];

  for (let i = 0; i < 20; i++) {
    mockData.push({
      key: i.toString(),
      title: 'content' + (i + 1),
    });
  }

  const handleFilter = (v, item) => {
    return item.title.toLowerCase().includes(v.toLowerCase())
  }

</script>
```

##    禁用

```vue
<template>
  <Transfer
    v-model:selected-keys="selectedKeys"
    :data-source="mockData"
    disabled
  />  
</template>

<script setup>
  import { ref } from 'vue'
  import { Transfer } from '@xhs/delight'

  const selectedKeys = ref(['1', '2'])

  const mockData = [];

  for (let i = 0; i < 20; i++) {
    mockData.push({
      key: i.toString(),
      title: 'content' + (i + 1),
    });
  }

</script>
```

##    Transfer API 参考

```
type TransferKey = string | number

interface DataSource {
  key: TransferKey
  title: string
  disabled?: boolean
}
```

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|dataSource|数据源|DataSource[]|[]|
|disabled|是否禁用|boolean|false|
|filter|接收 inputValue option 两个参数，当 option 符合筛选条件时，应返回 true，反之则返回 false。|`(inputValue, option) => boolean`|-|
|pagination (开发中)|使用分页样式，自定义渲染列表下无效|boolean &#124; `{ pageSize: number }`|false|
|render (开发中)|每行数据渲染函数，该函数的入参为 dataSource 中的项，返回值为 element。或者返回一个普通对象，其中 label 字段为 element，value 字段为 title| `Function(record)` | - |
|selectedKeys(v-model)|设置哪些项应该被选中|(string &#124; number)[]|[]|
|filterable|是否显示搜索框|boolean \| boolean[] |false|
|remote|是否远程筛选|boolean |false|
|titles|标题集合，顺序从左至右|string[]|[]|

### 事件 |事件|说明|类型|
| :- | :- | :- |
|change|选项在两栏之间转移时的回调函数|(keys: (string &#124; number)[]) => void|
|scroll|选项列表滚动时的回调函数|(direction: 'left' &#124; 'right', event) => void|
|search|搜索框内容时改变时的回调函数|(direction: 'left' &#124; 'right', value) => void|
