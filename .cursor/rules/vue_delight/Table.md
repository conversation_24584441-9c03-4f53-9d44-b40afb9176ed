
import { Link } from '@xhs/delight'

##    基本使用

`columns` 中支持通过以下字段配置不同的功能：
 - 通过 `tooltip` 设置当前列表头的提示文案
 - 通过 `renderTooltip` 自定义当前列表头的提示文案的渲染方式（唯一自定义方式）
 - 通过 `align` 设置当前列的对齐方式

```vue
<template>
  <Space>
    <Checkbox label="显示斑马纹" v-model:checked="stripe" />
    <Checkbox label="显示悬浮效果" v-model:checked="hover" />
    <Checkbox label="宽度自适应" v-model:checked="tableLayout" />
    <Checkbox label="显示表头" v-model:checked="showHeader" />
  </Space>
  <br />
  <br />
  <Table2
    :columns="columns"
    :data-source="dataSource"
    :tableLayout="tableLayout ? 'auto' : 'fixed'"
    :stripe="stripe"
    :hover="hover"
    :show-header="showHeader"
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2, Checkbox, Space, Popover, Icon, Text } from '@xhs/delight'
  import { Help } from '@xhs/delight/icons'

  const stripe = ref(false)
  const hover = ref(false)
  const tableLayout = ref(false)
  const showHeader = ref(true)

  const columns = [
    {
      title: '商品信息',
      dataIndex: 'good',
      width: 200,
      tooltip: '这是一段提示文案',
      align: 'center',
    },
    {
      title: '标签',
      dataIndex: 'tag',
    },
    {
      title: '简介',
      dataIndex: 'description',
      // tsx 写法
      renderTooltip: ({ title }) => <Popover
          placement={'top'}
          arrow={true}
          v-slots={{
            content: () => <div
                style={{ padding: 'var(--size-space-small) var(--size-space-large)' }}
              >
                <Text type={'description'}>
                  {'小红和小白，两个一起做车，你猜谁吐了。。。。。。。小白兔！'}
                </Text>
              </div>
          }}
        >
          <Icon icon={Help} color={'primary'}/>
        </Popover>,
    },
    {
      title: '管理员',
      dataIndex: 'manager',
    },
    {
      title: '操作',
      dataIndex: 'operation',
    },
  ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

    const getRowClassName = ({ rowIndex }) => {
        if (rowIndex === 1) {
            return 'woc niubi'
        }

        return ''
    }
</script>
```

##    自定义样式

- 使用表格属性 `rowClassName` 设置行类名。
- 使用列属性 `className` 设置列类名，或具体的某一个或某一些单元格类名。
- 使用列属性 `attrs: { style: {} }` 设置列内联样式，或具体的某一个或某一些单元格内联样式。

注意： 上述三个属性皆是 函数

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    tableLayout="auto"
    :rowClassName="getRowClassName"
  />
</template>

<script setup lang="ts">
  import { Table2 } from '@xhs/delight'

  const columns = [
    {
      title: '商品信息',
      dataIndex: 'good',
      width: 200,
    },
    {
      title: '标签',
      dataIndex: 'tag',
    },
    {
      title: '简介',
      dataIndex: 'description',
      width: 200,
      className: () => 'custom-columms-class-name'
    },
    {
      title: '申请天数',
      dataIndex: 'time',
      className: ({ rowData, rowIndex }) => {
        const { time } = rowData
        if (time >= 4) {
            return 'custom-cell-class-name'
        }
        return ''
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      attrs: ({ rowData }) => {
        return {
            style: { color: 'var(--color-primary)' }
        }
      }
    },
  ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        time: i * 2,
        operation: '编辑',
      })
    )

    const getRowClassName = ({ rowIndex }) => {
        if (rowIndex === 1) {
            return 'woc niubi'
        }

        return ''
    }
</script>

<style>
    .d-new-table .d-table__body .custom-columms-class-name {
        color: var(--color-orange-5)
    }
    .d-new-table .d-table__body .custom-cell-class-name {
        color: var(--color-red-7)
    }
</style>
```

##    固定表头

- 只要设置表格的高度后，表格内容高度超出后，滚动时表头会自动固定。
- 可通过 `scroll="{ y: number | string }"` 设置表格高度
- 也可通过 `maxHeight: number | string` 设置表格高度。

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    max-height="calc(100vh - 400px)"
    tableLayout="auto"
  />
</template>

<script setup lang="ts">
  import { Table2 } from '@xhs/delight'

  const columns = [
    {
        title: '商品信息',
        dataIndex: 'good',
        width: 200,
    },
    {
        title: '标签',
        dataIndex: 'tag',
    },
    {
        title: '简介',
        dataIndex: 'description',
        width: 200,
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
    },
    ]

    const dataSource = Array.from({ length: 20 }).map((_, i) => {
        return {
            key: i,
            good: '测试商品 ' + i,
            tag: '标签' + i,
            description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
            manager: '管理员' + i,
            operation: '编辑',
        }
    })

</script>
```

##    表头分组

- 通过column中的children字段设置
- children 和 dataIndex 互斥，因为合并表头不再有属于自己的列，也就无法配置列数据

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    :scroll="{ x: 1300 }"
  />
</template>

<script setup lang="ts">
  import { Table2 } from '@xhs/delight'

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
      fixed: 'left',
    },
    {
      title: '尺码',
      // fixed: 'left',
      children: [
        {
          title: '小号',
          children: [
            {
              title: 'XS',
              children: [
                {title: '155/40A', dataIndex:'a155',width:100,resizable:true},
                {title: '160/42A', dataIndex:'a160'},
              ]
            },
            {
              title: 'S',
              children: [
                {title: '165/44A', dataIndex:'a165'},
              ]
            }
          ]
        },
        {
          title: '中号',
          children: [
            {
              title: 'M',
              children: [
                {title: '170/46A', dataIndex:'a170'},
              ]
            },
            {
              title: 'L',
              children: [
                {title: '175/48A', dataIndex:'a175'},
              ]
            }
          ]
        },
        {
          title: '大号',
          children: [
            {
              title: 'XL',
              children: [
                {title: '180/50A', dataIndex:'a180'},
              ]
            },
            {
              title: 'XXL',
              children: [
                {title: '185/52A', dataIndex:'a185'},
              ]
            },
            {
              title: 'XXXL',
              children: [
                {title: '190/54A', dataIndex:'a190'},
              ]
            },
          ]
        },
      ]
    },
    {
      title: '数量',
      dataIndex: 'number',
    },
    {
      title: '单价',
      children: [
        {
          title: '原价',
          dataIndex: 'price'
        },
        {
          title: '促销价',
          children: [
            {
              title: 'Q1',
              dataIndex: 'priceQ1'
            },
            {
              title: 'Q2',
              dataIndex: 'priceQ2'
            },
            {
              title: 'Q3',
              dataIndex: 'priceQ3'
            },
            {
              title: 'Q4',
              dataIndex: 'priceQ4'
            }
          ]
        },
      ]
    }
  ]

  const dataSource = Array.from({ length: 5 }).map((_, i) => {
      return {
          key: i,
          name: '82短袖' + i,
          'a155': i + 1,
          'a160': i + 2,
          'a165': i + 3,
          'a170': i + 4,
          'a175': i + 5,
          'a180': i + 6,
          'a180': i + 7,
          'a185': i + 8,
          'a190': i + 9,
          number: 9*i+45,
          price: (i+1)*10,
          priceQ1: (i+1)*2,
          priceQ2: (i+1)*4,
          priceQ3: (i+1)*6,
          priceQ4: (i+1)*8,
      }
  })

</script>
```

##    固定列

必须指定每一个固定列的 fixed 属性。可选 `left` 、`right`。

也可以配合 `scroll={ x: number | string }` 设置 table 的宽度，让其大于容器的宽度，方可滚动

fixed=left 的时候，要求 left 左方所有列都必须设置宽度（自身可以不设置），right 同理。

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    :scroll="{ x: 1500 }"
  />
</template>

<script setup lang="ts">
  import { Table2 } from '@xhs/delight'

  const columns = [
    {
      title: '商品信息',
      dataIndex: 'good',
      width: 200,
      fixed: 'left'
    },
    {
      title: '标签',
      dataIndex: 'tag',
      width: 100,
      fixed: 'left'
    },
    {
      title: '简介',
      dataIndex: 'description',
    },
    {
      title: '管理员',
      dataIndex: 'manager',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 100,
    },
  ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

</script>
```

##    拖动调整列宽

将需要调整列宽的列配置为`resizable`, 并可设置拖动调整后的最小列宽`resizeMinWidth`。

注意：如果开启拖动调整列宽的功能，也需要指定 `width` 这个字段。如果不传，该列不具备伸缩功能。`width` 设置的值一定要大于`resizeMinWidth`。

有滚动条的情况:

调整列宽，只有该列宽度变化，其他列宽保持不变。这种情况下，每列都需要手动指定宽度 `width`。

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
  />
</template>

<script setup lang="ts">
  import { Table2 } from '@xhs/delight'

  const columns = [
    {
      title: '商品信息商品信息商品信息商品信息商品信息商品信息',
      dataIndex: 'good',
      width: 800,
      resizable: true,
      resizeMinWidth: 500
    },
    {
      title: '标签',
      dataIndex: 'tag',
      width: 500,
      resizable: true,
      resizeMinWidth: 300
    },
    {
      title: '简介',
      dataIndex: 'description',
      resizable: true,
      width: 500,
      resizeMinWidth: 300
    },
    {
      title: '管理员',
      dataIndex: 'manager',
      width: 300,
      resizable: true,
      resizeMinWidth: 100
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 245
    },
  ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

</script>
```

无滚动条,自适应宽的情况: 

其他列宽度会被浏览器自动调整，即会看到调整的当列宽度变化，其他列的宽度也跟着自适应变化。

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
  />
</template>

<script setup lang="ts">
  import { Table2 } from '@xhs/delight'

  const columns = [
    {
      title: '商品信息',
      dataIndex: 'good',
      width: 300,
      resizable: true,
      resizeMinWidth: 250
    },
    {
      title: '标签标签标签标签标签标签标签标签标签',
      dataIndex: 'tag',
      width: 100,
      resizable: true
    },
    {
      title: '简介简介简介简介简介简介简介简介简介简介简介简介',
      dataIndex: 'description',
      resizable: true,
      width: 300
    },
    {
      title: '管理员',
      dataIndex: 'manager',
    },
    {
      title: '操作',
      dataIndex: 'operation',
    },
  ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案' + i,
        manager: '管理员管理员管理员' + i,
        operation: '编辑',
      })
    )

</script>
```

通过绑定事件，并将参数存入localStorage，可以使列宽拖拽带有“记忆功能”。

```vue
<template>
   <Table2
      :columns="columns"
      :data-source="dataSource"
      @resizeWidthEnd="saveWidth"
    />
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Table2 } from '@xhs/delight'

  const columns = ref([
    {
      title: '商品信息商品信息商品信息商品信息商品信息商品信息',
      dataIndex: 'good',
      width: 800,
      resizable: true,
      resizeMinWidth: 500
    },
    {
      title: '标签',
      dataIndex: 'tag',
      width: 500,
      resizable: true,
      resizeMinWidth: 300
    },
    {
      title: '简介',
      dataIndex: 'description',
      resizable: true,
      width: 500,
      resizeMinWidth: 300
    },
    {
      title: '管理员',
      dataIndex: 'manager',
      width: 300,
      resizable: true,
      resizeMinWidth: 100
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 245
    },
  ])

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

    const saveWidth = (payload) => {
      const { dataIndex, width } = payload
      const key = 'delight-table2-width-collection'
      const cache = localStorage.getItem(key) ? JSON.parse(localStorage.getItem(key)) : {}
      cache[dataIndex] = width
      localStorage.setItem(key, JSON.stringify(cache))
      console.log('cache', cache)
    }

    onMounted(() => {
      const key = 'delight-table2-width-collection'
      if (localStorage.getItem(key)) {
        const cache = JSON.parse(localStorage.getItem(key))
        console.log('cache', cache)
        columns.value = columns.value.map(item => {
          if (cache[item.dataIndex]) {
            return {
              ...item,
              width: cache[item.dataIndex]
            }
          }
          return item
        })
      }
    })

</script>
```

##    总结栏

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    :max-height="600"
    tableLayout="auto"
  >
    <template #summary>
      <TSummary style="background: gray;">
        <td :colspan="columns.length">统计总共有{{ dataSource.length }}行</td>
      </TSummary>
      <TSummary>
        <td colspan="1">自定义Summary Cell宽度</td>
        <td colspan="4">我是第二个统计行</td>
      </TSummary>
    </template>
  </Table2>
</template>

<script setup lang="ts">
  import { Table2 } from '@xhs/delight'
  import { ref, onMounted } from 'vue'

  const { TSummary } = Table2

  const columns = ref([])

  const dataSource = ref([])

    onMounted(() => {
      dataSource.value = Array.from({ length: 20 }).map((_, i) => {
        return {
            key: i,
            good: '测试商品 ' + i,
            tag: '标签' + i,
            description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
            manager: '管理员' + i,
            operation: '编辑',
        }
      })
      columns.value = [
        {
            title: '商品信息',
            dataIndex: 'good',
            width: 200,
        },
        {
            title: '标签',
            dataIndex: 'tag',
        },
        {
            title: '简介',
            dataIndex: 'description',
            width: 200,
        },
        {
            title: '管理员',
            dataIndex: 'manager',
        },
        {
            title: '操作',
            dataIndex: 'operation',
        },
        ]
    })

</script>
```

##    自定义单元格

建议使用插槽，插槽名字就是 columns 中的 dataIndex 字段（必须是唯一值）

当然你也可以使用 render 函数，类型：render: `({ rowData, rowIndex }) => JSX.Element`

优先级：render 自定义当前列 > 插槽名为[dataIndex] > 插槽名为td

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    tableLayout="auto"
  >
    <template #tag="{ rowData, rowIndex }">
      <Tag :color="['orange', 'red', 'blue', 'cyan'][rowIndex]">{{ rowData.tag }}</Tag>
    </template>
    <template #td="{rowData, rowIndex, colIndex, dataIndex }">
      <Text ellipsis>1111 {{ dataIndex }}</Text>
    </template>
  </Table2>
</template>

<script setup lang="ts">
  import { Table2, Tag, Link, Text } from '@xhs/delight'

  const columns = [
    {
        title: '商品信息',
        dataIndex: 'good',
        width: 200,
    },
    {
        title: '标签',
        dataIndex: 'tag',
        width: 100,
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
        width: 100
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        render: ({ rowData, rowIndex }) => (
          <Link>{ rowData.operation }</Link>
        )
    },
    ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

</script>
```

##    自定义表头

建议使用插槽，插槽名字就是 columms 中的 title 字段（必须是唯一值，建议使用英文命名）

当然你也可以使用 renderTh 函数，类型：renderTh: `({ colIndex }) => JSX.Element`

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    tableLayout="auto"
  >
    <template #good-name>
      <div style="display: flex; align-items: center;"><Icon :icon="Ambulance" /> 🆘</div>
    </template>
  </Table2>
</template>

<script setup lang="ts">
  import { Table2, Tag, Link, Icon } from '@xhs/delight'
   import { Ambulance } from '@xhs/delight/icons'

  const columns = [
    {
        title: 'good-name',
        dataIndex: 'good',
        width: 200,
    },
    {
        title: '标签',
        dataIndex: 'tag',
        width: 100,
        renderTh: () => (
          <Tag color='pink'>标签</Tag>
        )
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
        width: 100
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100
    },
    ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

</script>
```

##    合并单元格

单元格的合并是直接采用原生 table 标签上的 rowspan 和 colspan 特性，大家不了解此特性可以 点击学习

表格支持行/列合并，使用 columms 里的 customCell 属性， colSpan 或者 rowSpan 设值为 0 时，设置的表格不会渲染。

表头的合并，直接在 columms 写上 colSpan 即可。

注意：当你设置了某列占据了很多列，被占据的列一定要设置 colSpan: 0，否则会出现一行有多余的列出现。

```vue
<template>
  <Table2
    :columns="columns"
    :row-class-name="rowClassName"
    :data-source="dataSource"
  >
  </Table2>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2 } from '@xhs/delight'


  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        customCell: ({ rowIndex }) => ({
          // 最后一行的占据了 5 列，那被占据的那些列需要设置 colSpan: 0 表示不渲染
          colSpan: rowIndex < 3 ? 1 : 5
        })
    },
    {
        title: '标签',
        dataIndex: 'tag',
        className: ({ rowIndex }) => [0, 1].includes(rowIndex) ? 'row-span-skyblue' : '',
        customCell: ({ rowIndex }) => {
          if (rowIndex === 3) {
            // 响应上方的列占据了，那此列不可以渲染，需要设置 colSpan: 0
            return { colSpan: 0 }
          }

          // 第一行的单元格要占据 2 行
          if (rowIndex === 0) {
            return { rowSpan: 2 }
          }
          // 第二行的不可以渲染，因为第二行的位置被 第一行 占据了，
          if (rowIndex === 1) {
            return { rowSpan: 0 }
          }
        }
    },
    {
        title: '简介',
        dataIndex: 'description',
        customCell: ({ rowIndex }) => {
          if (rowIndex === 3) {
            return { colSpan: 0 }
          }
        }
    },
    {
        title: '管理员',
        dataIndex: 'manager',
        colSpan: 2,
        customCell: ({ rowIndex }) => {
          if (rowIndex === 3) {
            return { colSpan: 0 }
          }
        }
    },
    {
        title: '操作',
        dataIndex: 'operation',
        colSpan: 0,
        width: 100,
        customCell: ({ rowIndex }) => {
          if (rowIndex === 3) {
            return { colSpan: 0 }
          }
        }
    },
    ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

  const rowClassName = ({ rowIndex }) => {
    return rowIndex === 3 ? 'col-span-pink' : ''
  }
</script>

<style>
.d-new-table .d-table__body td.row-span-skyblue {
  background-color: skyblue;
}
.d-new-table .d-table__body tr.col-span-pink {
  background-color: pink;
}
</style>
```

##    可展开和收起

`expandedRowRender` 插槽自定义展开后的内容

`expand-row-keys` 属性受控，可以控制哪些行展开

`expandOnRowClick` 点击行是否展开收缩，默认 false

`expandIcon` 用于自定义展开图标，值为 true 显示默认图标，值为 false 不显示图标，值为函数则表示完全自定义图标

```vue
<template>
 <p>expandRowKeys: {{ expandRowKeys }}</p>
 <SegmentControl v-model="expandControl" :options="options" @change='change' style="margin-bottom: 10px" />
  <Table2
    v-model:expand-row-keys="expandRowKeys"
    :columns="columns"
    :data-source="dataSource"
    :expandIcon="expandIcon"
    :scroll="{ x: 1800 }"
  >
    <template #expandedRowRender="{ rowData }">
      <article class="more-detail">
        <p class="title"><b>名称:</b></p>
        <p class="content">{{ rowData.good }}</p>
        <p class="title"><b>管理员:</b></p>
        <p class="content">{{ rowData.manager }}</p>
        <p class="title"><b>简介:</b></p>
        <p class="content">{{ rowData.description }}</p>
      </article>
    </template>
  </Table2>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2, Checkbox, SegmentControl, Icon } from '@xhs/delight'
  import { AnguishedFace } from '@xhs/delight/icons'
  
  const expandRowKeys = ref([1])
  const expandControl = ref(true)
  const expandIcon = ref(true)

  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        width: 200,
        fixed: 'left'
    },
    {
        title: '标签',
        dataIndex: 'tag',
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        fixed: 'right'
    }
    ]

  const options = [
    { label: '显示展开列', value: true },
    { label: '隐藏展开列', value: false },
    { label: '自定义展开列', value: 'custom' },
  ]
  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

  const change = (v) => {
    if (typeof v === 'boolean') {
      expandIcon.value = v
    } else {
      // 自定义展开列 icon
      expandIcon.value = ({ rowData, rowIndex }) => {
        return <Icon icon={AnguishedFace} color='danger' />
      }
    }
  }

</script>

<style>
  .more-detail {
    display: grid;
    grid-template-columns: 100px auto;
    padding: 12px;
  }
  .more-detail .title, 
  .more-detail .content  {
    margin: 2px 0;
  }
</style>
```

##    可选择

通过 selected 、rowSelection 来配置可选行：

- 此时 dataSource 中的每一行数据都需要配置唯一的 key 才能保证功能的正确响应
- selected 控制和获取当前表格中行的展开状态
- selectedChange 通知选种状态变化时选中行的完整信息
- selectedAll 通知全部选中状态的变化
- selectType 控制单选或者多选，可选 `multiple` | `single`，默认 `multiple`

rowSelection 控制每行 Checkbox 的属性
- allCheckDisabled 是否禁用全选 Checkbox
- getCheckboxProps 接受一个函数，返回值将会全部映射到 Checkbox 或者 Radio 组件上

```vue
<template>
  <p @click="click">selected: {{ selected }}</p>
  <Table2
    v-model:selected="selected"
    :columns="columns"
    :data-source="dataSource"
    :row-selection="rowSelection"
    @selectedChange='handleSelectedChange'
  >
    <template #operation="{ rowIndex }">
      <Link @click="() => handleDelete(rowIndex)">删除</Link>
    </template>
  </Table2>
  <Pagination v-model="page" :total="300" @change="changePage" style="margin-top: 10px" />
</template>

<script setup lang="ts">
  import { ref, onMounted, reactive } from 'vue'
  import { Table2, Link, Pagination } from '@xhs/delight'

  const rowSelection = ref({
    getCheckboxProps: (rowData) => ({
      disabled: rowData.key === '11',
      tooltip: rowData.key === '11' ? 'disable tooltip' : undefined
    })
  })
  const selected = ref([])

  function handleSelectedChange(selectedKey, selectedData, currentSelectedData, selectType) {
    console.log(selectedKey, selectedData, currentSelectedData, selectType)
  }

  const click = () => {
    dataSource.value = []
    selected.value = []
  }

  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        width: 200,
    },
    {
        title: '标签',
        dataIndex: 'tag',
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100
    },
  ]

  const dataSource = ref([])
  const page = ref(1)

  const genData = (v) => {
    return Array.from({ length: 4 }).map(
      (good, i) => ({
        key: i + v.toString(),
        good: i + v.toString(),
        tag: '标签' + i + v.toString(),
        description: '第一页' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )
  }
  

  dataSource.value = genData(1)

  const changePage = (v) => {
    dataSource.value = genData(v)
  }

  const handleDelete = (index) => {
    dataSource.value.splice(index, 1)
    // 必须给 dataSource 一个新的对象，才会激活内部的 watch 监听
    dataSource.value = [...dataSource.value]
  }

</script>
```

##    空状态

empty 可以通过 **插槽** 或者 **属性** 的方式自定义。

优先级：插槽 > 属性

```vue
<template>
  <Table2 :columns="columns" />

  <Table2 :columns="columns">
    <template #empty>
      <Result status="empty" title="暂无内容" />
    </template>
  </Table2>

  <Table2 :columns="columns" :empty="empty" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2, Result } from '@xhs/delight'

  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        width: 200,
        fixed: 'left'
    },
    {
        title: '标签',
        dataIndex: 'tag',
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        fixed: 'right'
    },
    ]

  const empty = (
    <Result status="constructing" title="暂无内容" />
  )
</script>
```

##    加载中

loading 设置为 true，便可出现 加载中 状态，也可以用过 loading 插槽自定义加载效果。

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    loading
  >
  </Table2>

  <br />
  <br />

  <Table2
    :columns="columns"
    :data-source="dataSource"
    loading
  >
    <template #loading>
      <div>
        <Spinner tip="自定义加载状态"/>
      </div>
    </template>
  </Table2>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2, Spinner } from '@xhs/delight'

  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        width: 200,
    },
    {
        title: '标签',
        dataIndex: 'tag',
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100
    },
    ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )
</script>
```

##    可拖拽

dragSort="row" 开启行拖拽排序

注意：行拖拽排序暂时不支持树形结构

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    dragSort="row"
    @drag-sort="dragSort"
    :disabled-drag-keys='disabledDragKeys'
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2 } from '@xhs/delight'

  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        width: 200,
    },
    {
        title: '标签',
        dataIndex: 'tag',
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100
    },
  ]

  const disabledDragKeys = [1]

  const dataSource = ref(Array.from({ length: 4 }).map(
      (good, i) => ({
        key: i,
        good: i,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    ))

  // 拖拽排序结束后，会触发此事件
  // newIndex 目前位置下标  oldIndex 当前位置下标 newData 排序后的 dataSource
  const dragSort = ({ newIndex, oldIndex, newData }) => {
    dataSource.value = newData
  }

</script>
```

#### 有手柄列的行拖拽排序 设置参数 dragSort='row-handler' 的同时，还需要添加手柄列：`{ dataIndex: dragRowColumnKey, render: () =>  }`

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    :row-selection="rowSelection"
    :disabled-drag-keys='disabledDragKeys'
    dragSort="row-handler"
    @drag-sort="dragSort"
  >
  </Table2>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2, Icon, dragRowColumnKey } from '@xhs/delight'
  import { Drag } from '@xhs/delight/icons'

  const rowSelection = ref({
    getCheckboxProps: (rowData) => ({
      disabled: rowData.key === 1,
      tooltip: rowData.key === 1 ? 'disable tooltip' : undefined
    })
  })

  // 禁用拖拽
  const disabledDragKeys = [1]


  const columns = [
      
      {
        title: '商品信息',
        dataIndex: 'good',
        fixed: true,
      },
      // 拖拽手柄永远在第一列
      { 
        title: '排序',
        dataIndex: dragRowColumnKey, 
        width: 80,
        render: () => <Icon icon={Drag} /> 
      },
      {
        title: '标签',
        dataIndex: 'tag',
      },
      {
        title: '简介',
        dataIndex: 'description',
      },
      {
        title: '管理员',
        dataIndex: 'manager',
        fixed: 'right',
      },
      {
        title: '操作',
        dataIndex: 'operation',
      },
    ]

  const dataSource = ref([
      'Nike Air Max 90 Premium',
      'Nike Air Max 90',
      'Nike Air Force 1 07 PRM',
      'Nike Air Force 1 Mid 07 WB',
    ]
      .map(
        (good, i) => ({
          key: i,
          good,
          tag: '标签' + i,
          description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
          manager: '管理员' + i,
          operation: '编辑',
        })
      ))

  // 拖拽排序结束后，会触发此事件
  // newIndex 目前位置下标  oldIndex 当前位置下标 newData 排序后的 dataSource
  const dragSort = ({ newIndex, oldIndex, newData }) => {
    dataSource.value = newData
  }

</script>
```

##    树形结构

通过 expanded、rowExpansion 来配置可选行：

- rowExpansion.treeNodeColumnIndex 配置展开 icon 在哪一列出现
- treeExpandAndFoldIcon 自定义树形结构展开收缩 icon，类型：`({ rowData, rowIndex }) => JSX.Element | VNode` （不支持 treeNodeColumnIndex = -1 的场景）

注意：内部会对数据进行拍平操作，会额外增加 `level`、`rawData`、`parent`、`isLeaf` 等字段。请用户不要在 dataSource 中出现这些字段，防止树形结构展示不对的问题。

```vue
<template>
  <p>expandedKeys: {{ expandedKeys }}</p>
  <Checkbox label="自定义展开 icon" @update:checked="change" />
  <Table2
    v-model:expandedKeys="expandedKeys"
    :columns="columns"
    :data-source="dataSource"
    :row-expansion="rowExpansion"
    :tree-expand-and-fold-icon="expandIcon"
  >
  </Table2>
</template>

<script setup lang="tsx">
  import { ref, computed } from 'vue'
  import { Table2, Checkbox, Icon } from '@xhs/delight'
  import { AstonishedFace } from '@xhs/delight/icons'

  // 指定展开 icon 在哪一列
  const rowExpansion = {
    treeNodeColumnIndex: 0
  }
  const expandedKeys = ref([])
  const expandIcon = ref()

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
    },
    {
      title: 'Age',
      dataIndex: 'age',
      width: '12%',
      defaultSortOrder: 'asc',
      sorter: true,
    },
    {
      title: 'Address',
      dataIndex: 'address',
      width: '30%',
    },
  ]

  const dataSource = [
    {
      key: 1,
      name: 'John Brown sr.',
      age: 60,
      address: 'New York No. 1 Lake Park',
      children: [
        {
          key: 11,
          name: 'John Brown',
          age: 42,
          address: 'New York No. 2 Lake Park',
        },
        {
          key: 12,
          name: 'John Brown jr.',
          age: 30,
          address: 'New York No. 3 Lake Park',
          children: [
            {
              key: 121,
              name: 'Jimmy Brown',
              age: 16,
              address: 'New York No. 3 Lake Park',
            },
          ],
        },
        {
          key: 13,
          name: 'Jim Green sr.',
          age: 72,
          address: 'London No. 1 Lake Park',
          children: [
            {
              key: 131,
              name: 'Jim Green',
              age: 42,
              address: 'London No. 2 Lake Park',
              children: [
                {
                  key: 1311,
                  name: 'Jim Green jr.',
                  age: 25,
                  address: 'London No. 3 Lake Park',
                },
                {
                  key: 1312,
                  name: 'Jimmy Green sr.',
                  age: 18,
                  address: 'London No. 4 Lake Park',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      key: 2,
      name: 'Joe Black',
      age: 32,
      address: 'Sidney No. 1 Lake Park',
    },
    {
      key: 3,
      name: 'Tom Ford',
      age: 48,
      address: 'Sidney No. 1 Lake Park',
    },
  ]

  const change = (v) => {
    if (v) {
      expandIcon.value = ({ rowData, rowIndex }) => <Icon icon={AstonishedFace} color="warning" />
    } else {
      expandIcon.value = undefined
    }
  }

</script>
```

##    树形结构-懒加载

开启 loadData 后，dataSource 中没有 children 字段，自动开启懒加载

注意：暂时不支持 `treeNodeColumnIndex = -1` 的场景

```vue
<template>
  <p>expandedKeys: {{ expandedKeys }}</p>
  <Checkbox label="自定义展开 icon" @update:checked="change" />
  <Table2
    v-model:expandedKeys="expandedKeys"
    :columns="columns"
    :data-source="dataSource"
    :load-data="loadData"
    :row-expansion="rowExpansion"
    :tree-expand-and-fold-icon="expandIcon"
  >
  </Table2>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2, Icon, Checkbox } from '@xhs/delight'
  import { AstonishedFace } from '@xhs/delight/icons'

  // 指定展开 icon 在哪一列
  const rowExpansion = {
    treeNodeColumnIndex: 0
  }
  const expandedKeys = ref([])

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
    },
    {
      title: 'Age',
      dataIndex: 'age',
      width: '12%',
    },
    {
      title: 'Address',
      dataIndex: 'address',
      width: '30%',
    },
  ]

  const dataSource = [
    {
      key: 1,
      name: 'John Brown sr.',
      age: 60,
      address: 'New York No. 1 Lake Park',
    },
    {
      key: 2,
      name: 'Joe Black',
      age: 32,
      address: 'Sidney No. 1 Lake Park',
    },
    {
      key: 3,
      name: 'Tom Ford',
      age: 48,
      address: 'Sidney No. 1 Lake Park',
      children: []
    },
  ]

  // treeNode 当前点击行的 rowData 数据
  const loadData = (treeNode) => {
    return new Promise((resolve) => {
        if (treeNode.key === '1aa') {
          resolve([])
          return
        }

        setTimeout(() => {
          // resolve 必须是一个 数组，会直接赋值给 parentNode.children
          resolve([
            {
              key: treeNode.key + 'a',
              name: 'John Brown',
              age: 42,
              address: 'New York No. 2 Lake Park',
            },
            {
              key: treeNode.key + 'b',
              name: 'John Brown jr.',
              age: 30,
              address: 'New York No. 3 Lake Park',
              children: []
            }
          ])
        }, 1000)
    })
  }

  const expandIcon = ref()

  const change = (v) => {
    if (v) {
      expandIcon.value = ({ rowData, rowIndex }) => <Icon icon={AstonishedFace} color="danger" />
    } else {
      expandIcon.value = undefined
    }
  }

</script>

```

##    筛选

通过 `columns` 中 `filterable` 设置

如果没有开启远程筛选，每次筛选时会触发 `filter` 事件

`filterable` 对象属性:
- `filter: (value: string | number, record: DataSource) => boolean`   筛选函数
- `popoverProps`   筛选器中popover组件的props
- `type: 'single' | 'multiple' | 'input'`   筛选器类型，单选、多选、搜索框,默认多选
- `options: {label: string; value: string; renderIcon?: () => JSX.Element}[]`   筛选器类型为单选或多选时的options
- `search: boolean`   是否开启选项关键词搜索options,默认false
- `clickPopoverOutsideToFilter: boolean`   点击popover外部是否立即触发筛选
- `icon`   自定义筛选icon

```vue
<script lang="tsx">
  import { h, defineComponent, ref } from 'vue'
  import { Table2 as Table, Tag, Popover, Icon, Text, Pagination } from '@xhs/delight'
  import { Help,Search } from '@xhs/delight/icons'

  export default defineComponent({
    setup() {
      const selected = ref([])
      const columns1 = [
        {
          title: '商品信息',
          dataIndex: 'good',
          tooltip: '这是一段提示文案',
          minWidth: 150,
          sorter: (a, b) => a.good > b.good ? 1 : -1,
          filterable: {
            options: [
              {label: '0A8', value: '0', renderIcon: () => <Icon icon={Help}/>},
              {label: '1', value: '1'},
              {label: '2', value: '2'},
              {label: '3', value: '3'},
              {label: '4', value: '4'}
            ],
            filter: (value, record) => record.good.indexOf(value) !== -1,
            search: true,
            popoverProps: {
              placement: "bottom-start"
            },
            icon: Search
          }
        },
        {
          title: '标签',
          dataIndex: 'tag',
          fixed: true,
          // 常规写法
          renderTh: ({ title }) => h(
            Tag,
            { size: 'small', color: 'blue' },
            () => title,
          ),
          filterable: {
            options: [
              {label: 'A800000000000000000000000', value: 'A80'},
              {label: 'A90', value: 'A90'},
              //{label: 'A', value: 'A'},
              //{label: 'B', value: 'B'}
            ],
            filter: (value, record) => record.good.indexOf(value) === 0,
            type: 'single',
            popoverProps: {
              placement: "top-start"
            }
          }
        },
        {
          title: '简介',
          dataIndex: 'description',
          // tsx 写法
          renderTooltip: ({ title }) => <Popover
              placement={'top'}
              arrow={true}
              v-slots={{
                content: () => <div
                    style={{ padding: 'var(--size-space-small) var(--size-space-large)' }}
                  >
                    <Text type={'description'}>
                      {'小红和小白，两个一起做车，你猜谁吐了。。。。。。。小白兔！'}
                    </Text>
                  </div>
              }}
            >
              <Icon icon={Help} color={'primary'}/>
            </Popover>,
        },
        {
          title: '管理员',
          dataIndex: 'manager',
          filterable: {
            filter: (value, record) => record.good.indexOf(value) === 0,
            type: 'input'
          }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center',
          fixed: 'right',
          // tsx 写法
          render: ({ rowData }) => <Text
            class={'d-text-nowrap'}
            link={true}
            onClick={() => console.log(rowData)}
          >
            {rowData.operation}
          </Text>,
        },
      ]
      
      const rowSelection = {
        getCheckboxProps: v => ({
          selectable: v.key !== '11'
        })
      }

      const dataSource1 = ref([])
      const page = ref(1)

      const genData = (v) => {
        return Array.from({ length: 4 }).map(
          (good, i) => ({
            key: i + v.toString(),
            good: i + v.toString(),
            tag: '标签' + i + v.toString(),
            description: '第一页' + i,
            manager: '管理员' + i,
            operation: '编辑',
          })
        )
      }
        

      dataSource1.value = genData(1)

      const changePage = (v) => {
        dataSource1.value = genData(v)
      }

      const handleFilter = ({dataIndex, filterValue, filterRecord}) => {
        // dataIndex是当前列
        // filterValue是当前列的筛选值，数组类型
        // filterRecord记录所有列的筛选值
        console.log(dataIndex, filterValue, filterRecord)
      }
      return () => (
        <div>
          <Table
            v-model={[ selected.value, 'selected' ]}
            columns={columns1}
            dataSource={dataSource1.value} 
            row-selection={ rowSelection }
            onFilter={ handleFilter }
          />
          <Pagination v-model={ page.value } total={300} onChange={changePage} style={{marginTop: '10px'}} />
        </div>
      )
    }
  })
</script>
```

通过`${dataIndex}-filter`设置具名作用域插槽，可以解构`filterValue`,`setFilterValue`,`submitFilter`,`resetFilter`四个参数。
- `filterValue`是当前列的筛选值，数组类型
- `setFilterValue`是设置`filterValue`的方法，`(value: (string | number)[]) => void`
- `submitFilter`确认筛选的方法
- `resetFilter`重置筛选的方法

```vue
<template>
  <Table2 :columns="columns" :data-source="dataSource">
    <template #good-filter={filterValue,setFilterValue,submitFilter,resetFilter}>
      <div class='filter'>
        <div class='d-th-filter-input-wrapper'>
          <Input
            class='d-th-filter-input'
            @change='(val)=>setFilterValue([val])'
            placeholder='输入关键词过滤'
            @enter='submitFilter'
          />
        </div>
      </div>
      <div class='d-th-filter-divider-wrapper'>
        <Divider :style="{ width: '100%' }"/>
      </div>
      <div class='d-th-filter-button-wrapper'>
        <Button :style="{ marginRight: '8px' }" type='light' @click='resetFilter'>
          重置
        </Button>
        <Button type='primary' @click='submitFilter'>
          确定
        </Button>
      </div>
    </template>
  </Table2>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Table2, Spinner, Input,Divider, Button} from '@xhs/delight'

  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        width: 200,
        filterable: {
          filter: (value, record) => record.good.includes(value),
          clickPopoverOutsideToFilter: true
        }
    },
    {
        title: '标签',
        dataIndex: 'tag',
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100
    },
    ]

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )
</script>

```

##    远程筛选

必须设置 `filterable` 开启筛选。当你设置了 `filterMethod` 为 props 后，Table本身不会做筛选，筛选规则完全依赖于 `filterMethod` 回调。

使用场景：

- 如果你的筛选依赖 Table组件，请使用 `filter` 事件来监听筛选变化。
- 如果你的筛选不依赖 Table组件，请传入 `filterMethod` props。在 `filterMethod` 回调里指定你的筛选规则（e.g. 请求后端筛选）。

```vue
<script lang="tsx">
  import { h, defineComponent, ref } from 'vue'
  import { Table2 as Table, Tag, Popover, Icon, Text, Pagination } from '@xhs/delight'
  import { Help } from '@xhs/delight/icons'

  export default defineComponent({
    setup() {
      const selected = ref([])
      const loading = ref(false)
      const columns1 = [
        {
          title: '商品信息',
          dataIndex: 'good',
          tooltip: '这是一段提示文案',
          minWidth: 150,
          sorter: (a, b) => a.good > b.good ? 1 : -1,
          filterable: {
            options: [
              {label: '0', value: '0', renderIcon: () => <Icon icon={Help}/>},
              {label: '1', value: '1'},
              {label: '2', value: '2'},
              {label: '3', value: '3'},
              {label: '4', value: '4'}
            ],
            filter: (value, record) => record.good.indexOf(value) !== -1,
            search: true,
            popoverProps: {
              placement: "bottom-start"
            }
          }
        },
        {
          title: '标签',
          dataIndex: 'tag',
          fixed: true,
          // 常规写法
          renderTh: ({ title }) => h(
            Tag,
            { size: 'small', color: 'blue' },
            () => title,
          ),
          filterable: {
            options: [
              {label: 'A80', value: 'A80'},
              {label: 'A90', value: 'A90'},
              {label: 'A', value: 'A'},
              {label: 'B', value: 'B'}
            ],
            filter: (value, record) => record.good.indexOf(value) === 0,
            type: 'single',
            popoverProps: {
              placement: "top-start"
            }
          }
        },
        {
          title: '简介',
          dataIndex: 'description',
          // tsx 写法
          renderTooltip: ({ title }) => <Popover
              placement={'top'}
              arrow={true}
              v-slots={{
                content: () => <div
                    style={{ padding: 'var(--size-space-small) var(--size-space-large)' }}
                  >
                    <Text type={'description'}>
                      {'小红和小白，两个一起做车，你猜谁吐了。。。。。。。小白兔！'}
                    </Text>
                  </div>
              }}
            >
              <Icon icon={Help} color={'primary'}/>
            </Popover>,
        },
        {
          title: '管理员',
          dataIndex: 'manager',
          filterable: {
            filter: (value, record) => record.good.indexOf(value) === 0,
            type: 'input'
          }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center',
          fixed: 'right',
          // tsx 写法
          render: ({ rowData }) => <Text
            class={'d-text-nowrap'}
            link={true}
            onClick={() => console.log(rowData)}
          >
            {rowData.operation}
          </Text>,
        },
      ]
      
      const rowSelection = {
        getCheckboxProps: v => ({
          selectable: v.key !== '11'
        })
      }

      const dataSource1 = ref([])
      const page = ref(1)

      const genData = (v) => {
        return Array.from({ length: 4 }).map(
          (good, i) => ({
            key: i + v.toString(),
            good: i + v.toString(),
            tag: '标签' + i + v.toString(),
            description: '第一页' + i,
            manager: '管理员' + i,
            operation: '编辑',
          })
        )
      }
        
      dataSource1.value = genData(1)

      const changePage = (v) => {
        dataSource1.value = genData(v)
      }

      const handleRemoteFilter = ({dataIndex, filterRecord}) => {
        // dataIndex是当前列
        // filterRecord记录所有列的筛选值
        console.log(dataIndex, filterRecord)
        loading.value = true
        setTimeout(() => {
          // 后端查询逻辑
          loading.value = false
          dataSource1.value = genData(page.value).filter(v => v[dataIndex].indexOf(filterRecord[dataIndex][0]) !== -1)
        },1000)
      }

      return () => (
        <div>
          <Table
            v-model={[ selected.value, 'selected' ]}
            columns={columns1}
            dataSource={dataSource1.value} 
            row-selection={ rowSelection }
            filterMethod={ handleRemoteFilter }
            loading={loading.value}
          />
          <Pagination v-model={ page.value } total={300} onChange={changePage} style={{marginTop: '10px'}} />
        </div>
      )
    }
  })
</script>
```

##    排序

必须设置 sorter 才会开启排序，sorter可以传入布尔值或者函数。
sortDirections 默认为['asc', 'desc']，会在 “升序、降序、不做排序”轮询。sortDirections的最大长度为3。
defaultSortOrder只能在一列进行设置，如果多列设置了defaultSortOrder，默认情况下取第一个defaultSortOrder。
sort 事件只有在手动点击排序时，才会被触发。

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    :tableLayout="tableLayout ? 'auto' : 'fixed'"
    :stripe="stripe"
    :hover="hover"
    :show-header="true"
  />
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Table2, Checkbox, Space } from '@xhs/delight'

  const stripe = ref(false)
  const hover = ref(false)
  const tableLayout = ref(false)
  const dataSource = ref([])

  const columns = ref([
    {
      title: '商品信息排序',
      dataIndex: 'good',
      sorter:true,
      sortDirections: ['desc', 'asc'],
    },
    {
      title: '标签',
      dataIndex: 'tag',
      sorter:true,
      sortDirections: ['desc', 'asc'],
    },
    {
      title: '简介',
      dataIndex: 'description',
    },
    {
      title: '管理员',
      dataIndex: 'manager',
    },
    {
      title: '操作',
      dataIndex: 'operation',
    },
  ])

    const getRowClassName = ({ rowIndex }) => {
        if (rowIndex === 1) {
            return 'woc niubi'
        }

        return ''
    }

    onMounted(() => {
      dataSource.value = [
        5,
        1,
        2,
        3
      ]
      .map(
        (good, i) => ({
          key: i,
          good,
          tag: '标签' + i,
          description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
          manager: '管理员' + i,
          operation: '编辑',
        })
      )
    })
</script>
```

##    远程排序

必须设置 sorter 开启排序。当你设置了 onSort为 props 后，Table本身不会做排序，排序规则完全依赖于 onSort 回调。

注意这里的 onSort 是props，而不是 sort 事件。使用场景：

1）如果你的排序依赖 Table组件，请使用 sort 事件来监听排序变化。
2）如果你的排序不依赖 Table组件，请传入 onSort props。在 onSort 回调里指定你的排序规则（e.g. 请求后端排序）。

```vue
<template>
  <Table2
    :columns="columns"
    :data-source="dataSource"
    :tableLayout="tableLayout ? 'auto' : 'fixed'"
    :stripe="stripe"
    :hover="hover"
    :show-header="true"
    :onSort="handleSort"
    :loading="loading"
  />
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Table2, Checkbox, Space } from '@xhs/delight'

  const stripe = ref(false)
  const hover = ref(false)
  const tableLayout = ref(false)
  const dataSource = ref([])
  const loading = ref(false)

  const columns = ref([
    {
      title: '商品信息排序',
      dataIndex: 'good',
      sorter:true,
    },
    {
      title: '标签',
      dataIndex: 'tag',
      sorter:true,
      sortDirections: ['desc', 'asc'],
    },
    {
      title: '简介',
      dataIndex: 'description',
    },
    {
      title: '管理员',
      dataIndex: 'manager',
    },
    {
      title: '操作',
      dataIndex: 'operation',
    },
  ])

  const result = [
      '97.26',
      '2.24',
      '55.19',
      '4.58'
    ]

    .map(
      (good, i) => ({
        key: i,
        good,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

    const getRowClassName = ({ rowIndex }) => {
        if (rowIndex === 1) {
            return 'woc niubi'
        }

        return ''
    }

      const handleSort = ({ dataIndex, sortOrder } = {}) => {
        // dataIndex 是列的关键字
        // sortOrder 是 'asc' | 'desc'
        console.log(dataIndex, sortOrder)

        if (sortOrder === 'asc') {
          loading.value = true
          setTimeout(() => {
            loading.value = false
            dataSource.value = result.map(v => v).sort((a, b) => a[dataIndex] - b[dataIndex])
          }, 2000)
        }

        if (sortOrder === 'desc') {
          loading.value = true
          setTimeout(() => {
            loading.value = false
            dataSource.value = result.map(v => v).sort((a, b) => b[dataIndex] -  a[dataIndex])
          }, 2000)
        }

        if (sortOrder === undefined) {
          loading.value = true
          setTimeout(() => {
            loading.value = false
            dataSource.value = result
          }, 2000)
        }

      }

    onMounted(() => {
      dataSource.value = result
    })
</script>
```

##    虚拟滚动

设置 `virtualize` 属性便会开启虚拟列表，同时需要设置 `maxHeight` 或者 `scroll={ y: xx }` 属性，让 Table 可以滚动起来

`virtualizeOptions` 属性设置虚拟滚动的一些特性
- itemSize 行高
- count 实际渲染多少个 dom
- buffer 设置缓冲区渲染多少个 dom

注意：
  - 目前暂时不支持 `非固定高度` 的场景，tableLayout 必须是 `fixed`。
  - 数据量少于 100 个的时候，可以不用开启虚拟滚动。
  - 开启虚拟列表了，建议设置 buffer，不然可能快速滚动会出现白屏现象。
  - 开启虚拟列表只是解决一次性渲染大量数据导致首屏加载慢的问题，但是如果你的列表每条 dom 很复杂，虚拟列表滚动动态切换 dom 可能会出现白屏问题。

```vue
<template>
  <p>selected: {{ selected }}</p>

  <Button @click="scrollToElement" style="margin-bottom: 5px;">滚动到指定元素</Button>

  <Table2
    ref="tableRef"
    v-model:selected="selected"
    :columns="columns"
    :data-source="dataSource"
    :row-selection="rowSelection"
    :max-height="200"
    virtualize
    :scroll="{ x: 2000 }"
    :virtualizeOptions="{
      itemSize: 46.5,
      count: 20,
      buffer: 5
    }"
  >
  </Table2>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Table2, Button } from '@xhs/delight'

  const rowSelection = {}
  const selected = ref([])
  const tableRef = ref()

  const columns = [
    {
        title: '商品名称',
        dataIndex: 'good',
        width: 200,
        fixed: 'left',
    },
    {
        title: '标签',
        dataIndex: 'tag',
        width: 300,
        resizable: true,
    },
    {
        title: '简介',
        dataIndex: 'description',
    },
    {
        title: '管理员',
        dataIndex: 'manager',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        fixed: 'right'
    },
  ]

  const dataSource = ref([])

  dataSource.value = Array.from({ length: 1000 })
    .map(
      (_, i) => ({
        key: i,
        good: i,
        tag: '标签' + i,
        description: '描述文案描述文案描述文案描述文案描述文案描述文案' + i,
        manager: '管理员' + i,
        operation: '编辑',
      })
    )

  // 注意：当下仅支持 虚拟列表 下可使用
  // key 是 datesource 中的唯一值，
  // offset 是偏移量，距离表头的偏移量
  const scrollToElement = () => {
    tableRef.value.scrollToElement({
      key: 100,
      offset: 90,
    })
  }
</script>
```

##    API 参考

通过设置 Table 的属性来产生不同的表格样式：（建议看 demo，每个功能都列举了对应的所有 api）

|属性|说明|类型|默认值|版本|
| :- | :- | :- | :- | :- |
|columns|表格的列|`Columns[]`|-|
|dataSource|表格的数据|`DataSource[]`|-|
|selected (v-model:selected)|表格选中行的 keys|`(string \| number)[]`|-|
|selectAndRowClick|可选择时点击checkbox或radio是否触发rowClick事件|`boolean`|`true`|
|selectType|单选或者多选|`multiple \| single`|`multiple`|
|filterMethod|远程筛选方法|`(data: {dataIndex: string; filterRecord: FilterRecord}) => void`|-|
|loading|页面是否加载中|`boolean`|`false`|
|empty|页面空状态|`string \| vnode`|`'暂无数据'`|
|showHeader|是否显示表头|`boolean`|`true`|
|tableLayout|表格布局方式|`fixed \| auto`|`fixed`|
|stripe|是否开启斑马线|`boolean`|`false`|
|hover|是否开启鼠标 hover 高亮某一行|`boolean`|`false`|
|rowClassName|自定义行 class |`(e: { rowData, rowIndex }) => string`|-|
|rowKey|唯一值 key，用于标识每行数据的|`string`|`'key'`|
|maxHeight|开启表头固定，表格最大高度|`number \| string`|-|
|scroll|设置表格的宽高|`{ x: number \| string; y: number \| string }`|-|
|sticky|表头吸顶（默认吸附最近可滚动的父元素）|`boolean`|`false`|<Tag color="green">1.2.10</Tag>|
|expandRowKeys(v-model)|受控自定义展开行|`(string \| number)[]`|[]|
|expandOnRowClick|行点击，是否展开|`boolean`|`false`|
|expandIcon|是否显示展开列的 icon|`boolean \| (e: { rowData, rowIndex }) => vnode \| JSX.Element` |`true`|
|rowSelection|可选择的配置项|`{ allCheckDisabled?: boolean; allCheckVisible?: boolean; getCheckboxProps?: () => {} }`|-|
|expandedKeys(v-model)|受控树形结构展开收缩|`(string \| number)[]`|[]|
|rowExpansion|树形结构展开 icon 出现在某列|`{ treeNodeColumnIndex?: number }`|-|
|loadData|树形结构懒加载|`(treeNode: DataSource) => Promise<DataSource[]>`|-|
|treeExpandAndFoldIcon|自定义树形结构展开 icon|`({ rowData, rowIndex }) => JSX.Element \| VNode`|-|
|dragSort|开启排序|`'row'`|-|
|sortableProps|Sortable 属性|`Sortable.Options`|-|<Tag color="green">1.2.17</Tag>|
|disabledDragKeys|禁用拖拽|`(string \| number)[]`|-|
|virtualize|是否开启虚拟滚动|`boolean`|`false`|
|virtualizeOptions|虚拟滚动的配置项|`{ itemSize?: number; buffer?: number; count?: number }`|-|
|onlyHeadShowResizeLine|设置列宽可拖动时，仅Head中展示分割线|`boolean`|`false`|
|isClickRowSelectEnabled|是否开启点击行可选中（按住shift可多选/反选）|`boolean`|`false`|
|sortablePreventOnFilter|控制 Sortable.js 在过滤掉不可拖拽的行时，是否阻止默认的浏览器行为（如文本选择）|`boolean`|`true`|


### Column

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|minWidth|设置当前列的最小宽度（需把tableLayout设置为auto）|string|-|
|sortDirections|支持的排序方式。最大长度为3。默认情况按照升序、降序、不排序进行轮询。|array|['asc', 'desc']|
|defaultSortOrder|默认排序顺序，可选 'asc' &#124; 'desc'|string|-|
|sorter| 列排序是否开启，可配置自定义排序规则。| boolean | Function | (a: Record&lt;string &#124; any&gt;, b: Record&lt;string &#124; any&gt;) => number  &#124; boolean |-|

### Table 事件

|事件|说明|类型|
| :- | :- | :- |
|sort|手动点击排序时，会触发该事件|`(e: MouseEvent, { dataIndex, sortOrder }) => void`|
|filter|手动点击筛选时，会触发该事件|`({ dataIndex, filterValue, filterRecord }) => void`|
|row-click|行点击事件|`(e: MouseEvent, { rowIndex, rowData }) => void`|
|cell-click|单元格点击事件|`(e: MouseEvent, { rowIndex, colIndex, rowData } & Columns) => void`|
|row-expand|行展开收起事件|`({ rowIndex, rowData, key }) => void`|
|resizeWidthEnd| 拖动调整列宽结束事件|`({ dataIndex, sortOrder }) => void` |
|selectedChange| 选中值发生改变事件 | `(selectedKey: (string \| number)[], selectedData: DataSource[], currentSelectedData: DataSource[], selectType: boolean) => void` |  
|selectedAll| 全选状态发生改变事件 | `(selectedKey: (string \| number)[], selectedData: DataSource[], currentSelectedData: DataSource[], selectType: boolean) => void` |  
|row-mouseenter|行鼠标移入事件|`(e: MouseEvent, { rowIndex, rowData }) => void`|
|row-mouseleave|行鼠标移出事件|`(e: MouseEvent, { rowIndex, rowData }) => void`|


### Table 方法

|方法|说明|版本
| :- | :- | :- |
|scrollToElement|滚动到指定元素|
|sort|排序，接受参数为`{ dataIndex: string, order?: 'asc' \| 'desc' }`| <Tag color="green">1.2.14</Tag>
|filter|筛选，接受参数为`{ dataIndex: string; filterValue: (string \| number)[] }`| <Tag color="green">1.2.14</Tag>
|resetFilter|重置筛选，如果不传递参数则重置所有列的筛选，也可以传递一个类型为 `string[]` 的参数，是各列dataIndex的数组，会重置对应列的筛选| <Tag color="green">1.2.14</Tag>


### Table 插槽

|名称|说明|属性|版本
| :- | :- | :- | :- |
|footer|表格尾部|DataSource[]|
|append|插入至表格最后一行之后的内容， 如果需要对表格的内容进行无限滚动操作，可能需要用到这个 slot。 若表格有合计行，该 slot 会位于合计行之上（虚拟滚动场景暂时不支持）|DataSource[]|<Tag color="green">1.3.7</Tag>
