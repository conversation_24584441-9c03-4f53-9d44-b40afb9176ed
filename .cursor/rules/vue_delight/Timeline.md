

### 何时使用 - 当有一系列信息需按时间排列时，可正序和倒序。
- 需要有一条时间轴进行视觉上的串联时。

##    基本使用

基本的时间轴。

```vue
<template>
  <Timeline>
    <TimelineItem label="Label 1" timestamp="2015-09-01">Create a services site</TimelineItem>
    <TimelineItem label="Label 1" timestamp="2015-09-02">Solve initial network problems</TimelineItem>
    <TimelineItem timestamp="2015-09-03">Technical testing</TimelineItem>
    <TimelineItem timestamp="2015-09-04">Network problems being solved</TimelineItem>
    <TimelineItem timestamp="2015-09-05">Network problems being solved</TimelineItem>
  </Timeline>
</template>

<script setup>
  import { Timeline, TimelineItem } from '@xhs/delight'
</script>
```

##    圆圈颜色

color 预设了 `success`、`danger`、`warning`、`primary` 四种颜色，当然你也可以传入任何其他符合 css 规范的颜色值。

```vue
<template>
  <Timeline>
    <TimelineItem label="label1" timestamp="2015-09-01" color="success">Create a services site</TimelineItem>
    <TimelineItem label="label2" timestamp="2015-09-02" color="danger">Solve initial network problems</TimelineItem>
    <TimelineItem timestamp="2015-09-03" color="warning">Technical testing</TimelineItem>
    <TimelineItem timestamp="2015-09-04" color="primary">Network problems being solved</TimelineItem>
    <TimelineItem timestamp="2015-09-05" color="yellow">Network problems being solved</TimelineItem>
  </Timeline>
</template>

<script setup>
  import { Timeline, TimelineItem } from '@xhs/delight'
</script>
```

##    反转

```vue
<template>
  <Button @click="handleClick" style="margin-bottom: 20px">reverse</Button>

  <Timeline :reverse="reverse">
    <TimelineItem label="label1" timestamp="2015-09-01" color="success">Create a services site</TimelineItem>
    <TimelineItem label="label2" timestamp="2015-09-02" color="danger">Solve initial network problems</TimelineItem>
    <TimelineItem timestamp="2015-09-03" color="warning">Technical testing</TimelineItem>
    <TimelineItem timestamp="2015-09-04" color="primary">Network problems being solved</TimelineItem>
    <TimelineItem timestamp="2015-09-05" color="yellow">Network problems being solved</TimelineItem>
  </Timeline>
</template>

<script setup>
  import { ref } from 'vue'
  import { Timeline, TimelineItem, Button } from '@xhs/delight'

  const reverse = ref(false)

  const handleClick = () => {
    reverse.value = !reverse.value
  }
</script>
```

##    模式

mode 分为 `left`、`alternate`、`right` 三种模式

timePosition 表示时间在时间轴上面的位置，有 `same` 、`relative`

```vue
<template>
  <Space direction="vertical">
    <p>mode: <SegmentControl v-model="mode" :options="options" /></p>
    <p>time-position : <SegmentControl v-model="position" :options="options1" /></p>
  </Space>

  <Timeline :mode="mode" :time-position="position">
    <TimelineItem label="label1" timestamp="2015-09-01" color="success" >Create a services site</TimelineItem>
    <TimelineItem label="label2" timestamp="2015-09-02" color="danger">Solve initial network problems</TimelineItem>
    <TimelineItem timestamp="2015-09-03" color="warning">Technical testing</TimelineItem>
    <TimelineItem timestamp="2015-09-04" color="primary">Network problems being solved</TimelineItem>
    <TimelineItem timestamp="2015-09-05" color="yellow">Network problems being solved</TimelineItem>
  </Timeline>
</template>

<script setup>
  import { ref } from 'vue'
  import { Space, Timeline, TimelineItem, SegmentControl } from '@xhs/delight'

  const mode = ref('left')
  const position = ref('')

  const options = [
    { label: 'left', value: 'left' },
    { label: 'alternate', value: 'alternate' },
    { label: 'right', value: 'right' },
  ]

  const options1 = [
    { label: 'same', value: 'same' },
    { label: 'relative', value: 'relative' },
  ]
</script>
```

##    自定义时间轴点

```vue
<template>
  <Timeline>
    <TimelineItem label="label1" timestamp="2015-09-01">
      <template #dot>
        <Icon :icon="AnguishedFace" color="success" />
      </template>
      Create a services site
    </TimelineItem>
    <TimelineItem label="label2" timestamp="2015-09-02">
      <template #dot>
        <Icon :icon="Ambulance" color="info" />
      </template>
      Solve initial network problems
    </TimelineItem>
    <TimelineItem timestamp="2015-09-03">
      <template #dot>
        <Icon :icon="AstonishedFace" color="warning" />
      </template>
      Technical testing
    </TimelineItem>
    <TimelineItem timestamp="2015-09-04">
      <template #dot>
        <Icon :icon="BabyTaste" color="danger" />
      </template>
      Network problems being solved
    </TimelineItem>
    <TimelineItem timestamp="2015-09-05">
      <template #dot>
        <Icon :icon="BabyOne" style="color: red" />
      </template>
      Network problems being solved
    </TimelineItem>
  </Timeline>
</template>

<script setup>
  import { Timeline, TimelineItem, Icon } from '@xhs/delight'
  import { AnguishedFace, Ambulance, AstonishedFace, BabyTaste, BabyOne } from '@xhs/delight/icons'
</script>
```

##    API 参考

#### Timeline Props |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|mode|通过设置 mode 可以改变时间轴和内容的相对位置|left &#124; alternate &#124; right|-|
|reverse|节点排序|boolean|false|
|timePosition|设置时间的位置|same &#124; relative|-|

#### TimelineItem Props |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|color|指定圆圈颜色 `success`、`danger`、`warning`、`primary`，或自定义的色值|string|-|
|label|设置标签|string|-|
|timestamp|时间戳|string|-|

#### TimelineItem Slots |插槽名|说明|
| :- | :- |
|dot|自定义时间轴点|
|label|自定义 label 标签|
|-|Timeline-Item 的内容|
