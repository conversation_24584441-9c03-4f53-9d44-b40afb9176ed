

##    基本使用

- 通过 `options` 设置元信息

```vue
<template>
  <List :options="options"/>
</template>

<script setup lang="ts">
  import { List } from '@xhs/delight'

  const options = Array
    .from(
      { length: 4 },
      (_, i) => (
        {
          avatar: 'https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png',
          avatarAlign: 'start',
          title: '逍遥游',
          description: '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”',
          actions: Array.from({ length: 4 }, (_, i) => ({ name: '操作 ' + (i + 1), onClick: () => console.log(i + 1) }))
        }
      ),
    )
</script>
```

##    自定义元信息内容

- 通过 `slots.avatar` 自定义头像
- 通过 `slots.title` 自定义标题
- 通过 `slots.description` 自定义内容
- 通过 `slots.actions` 自定义操作栏

```vue
<template>
  <List :options="options">
    <template #avatar="{ avatar }">
      <Avatar :src="avatar" size="extra-small" />
    </template>
    <template #avatar="{ title }">
      <Text type="h6" bold>
        {{title}}
      </Text>
    </template>
    <template #description="{ description }">
      <Text :max-lines="2" ellipsis>
        {{description}}
      </Text>
    </template>
    <template #actions>
      <Text size="small" link>
        {{'...'}}
      </Text>
    </template>
  </List>
</template>

<script setup lang="ts">
  import { List, Avatar, Text } from '@xhs/delight'

  const options = Array
    .from(
      { length: 4 },
      (_, i) => (
        {
          avatar: 'https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png',
          avatarAlign: 'start',
          title: '逍遥游',
          description: '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”',
          actions: Array.from({ length: 4 }, (_, i) => ({ name: '操作 ' + (i + 1), onClick: () => console.log(i + 1) }))
        }
      ),
    )
</script>
```

##    完全自定义元信息

- 通过 `slots.option` 完全自定义元信息

```vue
<template>
  <List :options="options">
    <template #option="{ description }">
      <Text :max-lines="2" ellipsis>
        {{description}}
      </Text>
    </template>
  </List>
</template>

<script setup lang="ts">
  import { List, Text } from '@xhs/delight'

  const options = Array
    .from(
      { length: 4 },
      (_, i) => (
        {
          avatar: 'https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png',
          avatarAlign: 'start',
          title: '逍遥游',
          description: '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”',
          actions: Array.from({ length: 4 }, (_, i) => ({ name: '操作 ' + (i + 1), onClick: () => console.log(i + 1) }))
        }
      ),
    )
</script>
```

##    列表元信息边缘间距

通过 `space` 设置列表元信息边缘间距：

```vue
<template>
  <List :options="options" space="small"/>
  <List :options="options"/>
  <List :options="options" space="large"/>
</template>

<script setup lang="ts">
  import { List } from '@xhs/delight'

  const options = Array
    .from(
      { length: 2 },
      (_, i) => (
        {
          avatar: 'https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png',
          avatarAlign: 'start',
          title: '逍遥游',
          description: '北冥有鱼...',
          actions: Array.from({ length: 4 }, (_, i) => ({ name: '操作 ' + (i + 1), onClick: () => console.log(i + 1) }))
        }
      ),
    )
</script>
```

### 加载更多 - 通过 `loadMore` 设置加载更多
- 通过 `loading` 设置加载状态
- 通过 `onLoad` 接收加载事件

```vue
<template>
  <List
    :options="options"
    :loadMore="loadMore"
    :loading="loading"
    @load="handleLoadMore"
  />
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { List } from '@xhs/delight'

  const loadMore = ref(true)
  const loading = ref(false)
  const size = ref(1)

  function handleLoadMore() {
    loading.value = true

    setTimeout(
      () => {
        size.value += 1
        loading.value = false

        if (size.value > 1) {
          loadMore.value = false
        }
      },
      1000
    )
  }

  const options = computed(
    () => Array
      .from(
        { length: size.value },
        (_, i) => (
          {
            avatar: 'https://picasso-static.xiaohongshu.com/fe-platform/b57931f302f08a4075493b44b99b7a250b160fa2.png',
            avatarAlign: 'start',
            title: '逍遥游',
            description: '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”',
            actions: Array.from({ length: 4 }, (_, i) => ({ name: '操作 ' + (i + 1), onClick: () => console.log(i + 1) }))
          }
        ),
      )
    )
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|space|列表中元信息的边缘间距|'unset' &#124; 'small' &#124; 'default' &#124; 'large'|'default'|
|options|列表中元信息的数据|[AllMetaProps](https://delight.devops.xiaohongshu.com/cmp/meta)[]|-|
|loadMore|列表是否可以加载更多|boolean|false|
|loading|列表是加载更多按钮的加载状态|boolean|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|load|加载更多按钮单击的回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|option|完全自定义列表中的元信息|
|avatar|列表中元信息的头像|
|title|列表中元信息的标题|
|description|列表中元信息的描述内容|
|actions|列表中元信息的操作栏|