

##    基本使用

通过 `DatePicker` 选择单个日期，通过 `DateRangePicker` 选择日期区间：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value"/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value" autofocus/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value" hide-indicator/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value" select-only/>
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue"/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
    <DateRangePicker v-model="rangeValue" select-only/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })
</script>
```

##    placeholder

通过 `placeholder` 设置提示文本：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" placeholder="请选择日期"/>
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" :placeholder="{ start: '请选择开始日期', end: '请选择结束日期' }"/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref()
  const rangeValue = ref()
</script>
```

##    预设时间范围

通过 `ranges` 设置常用的时间点或范围，提高用户体检。

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" placeholder="带确定按钮的时间选择器" confirm/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value" :ranges="range"/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value" :ranges="range"  confirm/>
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" :placeholder="{ start: '请选择开始日期', end: '请选择结束日期' }" :ranges="ranges"/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
    <DateRangePicker v-model="rangeValue" :placeholder="{ start: '请选择开始日期', end: '请选择结束日期' }" :ranges="ranges" confirm @click:endCell="endCellClick"/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'
  import dayjs from 'dayjs'
  const range = { "今天": dayjs(), "一周后": dayjs().add(7, 'day'), "一个月": dayjs().add(30, 'day'), "一个季度": dayjs().add(90, 'day') }
  const ranges = { "今天": [dayjs(), dayjs()], "近一周": [dayjs(), dayjs().add(7, 'day')] }

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })
  const endCellClick = () => {
    console.log('end cell click')
  }
</script>
```

##    前缀

通过 `prefix` 或 `slots.prefix` 设置前缀：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value"  prefix="日期"/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value">
      <template #prefix>
        <Icon :icon="Alarm"/>
      </template>
    </DatePicker>
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" prefix="日期"/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
    <DateRangePicker v-model="rangeValue">
      <template #prefix>
        <Icon :icon="Alarm"/>
      </template>
    </DateRangePicker>
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text, Icon } from '@xhs/delight'
  import { Alarm } from '@xhs/delight/icons'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })
</script>
```

##    后缀

通过 `suffix` 或 `slots.suffix` 设置后缀：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value"  suffix="日期"/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value">
      <template #suffix>
        <Icon :icon="Alarm"/>
      </template>
    </DatePicker>
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" suffix="日期"/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
    <DateRangePicker v-model="rangeValue">
      <template #suffix>
        <Icon :icon="Alarm"/>
      </template>
    </DateRangePicker>
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text, Icon } from '@xhs/delight'
  import { Alarm } from '@xhs/delight/icons'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })
</script>
```

##    多选

通过 `multiple` 设置多选，多选时通过 `multiLine` 设置多行展示：
 - 仅 DatePicker 支持多选

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" multiple placeholder="请选择日期"/>
    <Text>当前选中日期：{{ value }}</Text>
    <DatePicker v-model="value" multiple multi-line placeholder="请选择日期"/>
    <Text>当前选中日期：{{ value }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref('2022-11-11')

</script>
```

##    清除输入

通过 `clearable` 开启清除输入功能：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" clearable/>
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" clearable/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })
</script>
```

##    必填

通过 `required` 设置为必填项：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" required/>
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" required/>
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" :validate="validate"/>
    <Text>必须选择每月 10 日之后的日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" :validate="rangeValidate"/>
    <Text>开始日期必须选择每月 10 日之后的日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })

  function validate({ fullValue }) {
    console.log(fullValue)
    return fullValue?.date() >= 10
  }
  function rangeValidate({ fullValue }) {
    return fullValue?.start?.date() >= 10
  }
</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" :validate="validate" validate-timing="immediate"/>
    <Text>必须选择每月 10 日之后的日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" :validate="rangeValidate" validate-timing="immediate"/>
    <Text>开始日期必须选择每月 10 日之后的日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })

  function validate({ fullValue }) {
    return fullValue?.date() >= 10
  }
  function rangeValidate({ fullValue }) {
    return fullValue?.start?.date() >= 10
  }
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker ref="time" v-model="value" :validate="validate" validate-timing="manual"/>
    <Button type="primary" @click="manualValidate">必须选择每月 10 日之后的日期：{{result}}</Button>
    <DateRangePicker ref="timeRange" v-model="rangeValue" :validate="rangeValidate" validate-timing="manual"/>
    <Button type="primary" @click="manualRangeValidate">开始日期必须选择每月 10 日之后的日期：{{rangeResult}}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Button } from '@xhs/delight'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })

  const time = ref()
  const result = ref()
  const timeRange = ref()
  const rangeResult = ref()

  function validate({ fullValue }) {
    return fullValue?.date() >= 10
  }
  function rangeValidate({ fullValue }) {
    return fullValue?.start?.date() >= 10
  }

  function manualValidate() {
    time.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }

  function manualRangeValidate() {
    timeRange.value
      ?.validate()
      .then(
        res => {
          rangeResult.value = res
        }
      )
  }
</script>
```

##    单位

通过 `unit` 设置单位，可以设置年： `year` 、季度：`quarter`、月： `month` 、周： `week` 、日： `date` 、小时： `hour` 、分钟： `minute` 、秒： `second` ，默认 `date`：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value1" unit="year" placeholder="请选择年份" />
    <Text>当前选中日期{{ value1 }}</Text>
    <DatePicker v-model="value2" unit="quarter" placeholder="请选择季度" />
    <Text>当前选中日期{{ value2 }}</Text>
    <DatePicker v-model="value3" unit="month" placeholder="请选择月份" />
    <Text>当前选中日期{{ value3 }}</Text>
    <DatePicker v-model="value4" unit="week" placeholder="请选择周" />
    <Text>当前选中日期{{ value4 }}</Text>
    <DatePicker v-model="value5" placeholder="请选择日期" />
    <Text>当前选中日期{{ value5 }}</Text>
    <DatePicker v-model="value6" unit="hour" placeholder="请选择日期" />
    <Text>当前选中日期{{ value6 }}</Text>
    <DatePicker v-model="value7" unit="minute" placeholder="请选择日期" />
    <Text>当前选中日期{{ value7 }}</Text>
    <DatePicker v-model="value8" unit="second" placeholder="请选择日期" />
    <Text>当前选中日期{{ value8 }}</Text>
    <DateRangePicker v-model="rangeValue1" unit="year"  :placeholder="{ start: '请选择开始年份', end: '请选择结束年份' }" />
    <Text>当前选中日期{{ rangeValue1 }}</Text>
    <DateRangePicker v-model="rangeValue2" unit="quarter"  :placeholder="{ start: '请选择开始季度', end: '请选择结束季度' }" />
    <Text>当前选中日期{{ rangeValue2 }}</Text>
    <DateRangePicker v-model="rangeValue3" unit="month"  :placeholder="{ start: '请选择开始月份', end: '请选择结束月份' }" />
    <Text>当前选中日期{{ rangeValue3 }}</Text>
    <DateRangePicker v-model="rangeValue4" unit="week"  :placeholder="{ start: '请选择开始周', end: '请选择结束周' }" />
    <Text>当前选中日期{{ rangeValue4 }}</Text>
    <DateRangePicker v-model="rangeValue5" :placeholder="{ start: '请选择开始日期', end: '请选择结束日期' }" />
    <Text>当前选中日期{{ rangeValue5 }}</Text>
    <DateRangePicker v-model="rangeValue6" unit="hour" :placeholder="{ start: '请选择开始日期', end: '请选择结束日期' }" />
    <Text>当前选中日期{{ rangeValue6 }}</Text>
    <DateRangePicker v-model="rangeValue7" unit="minute" :placeholder="{ start: '请选择开始日期', end: '请选择结束日期' }" />
    <Text>当前选中日期{{ rangeValue7 }}</Text>
    <DateRangePicker v-model="rangeValue8" unit="second" :placeholder="{ start: '请选择开始日期', end: '请选择结束日期' }" />
    <Text>当前选中日期{{ rangeValue8 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value1 = ref('2022')
  const value2 = ref('2022-11-11')
  const value3 = ref('2011-11')
  const value4 = ref('2011-11-07')
  const value5 = ref('2011-11-11')
  const value6 = ref('2011-11-11 12')
  const value7 = ref('2011-11-11 12:00')
  const value8 = ref('2011-11-11 12:00:00')
  const rangeValue1 = ref({ start: '2022', end: '2033' })
  const rangeValue2 = ref({ start: '2022-09-01', end: '2022-11-01' })
  const rangeValue3 = ref({ start: '2022-11', end: '2022-12' })
  const rangeValue4 = ref({ start: '2022-11-07', end: '2022-12-12' })
  const rangeValue5 = ref({ start: '2022-11-11', end: '2022-12-12'})
  const rangeValue6 = ref({ start: '2022-11-11 12', end: '2022-12-12 12' })
  const rangeValue7 = ref({ start: '2022-11-11 12:00', end: '2022-12-12 12:00' })
  const rangeValue8 = ref({ start: '2022-11-11 12:00:00', end: '2022-12-12 12:00:00' })
</script>
```

##    格式

通过 `format` 设置数据格式，默认 `YYYY-MM-DD HH:mm:ss`（根据 unit 截取掉多余部分）：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value1" unit="second" format="YYYY/MM/DD HH:mm:ss"/>
    <Text>当前选中日期：{{ value1 }}</Text>
    <DatePicker v-model="value2" unit="second" format="YYYY-MMM-DD HH:mm:ss"/>
    <Text>当前选中日期：{{ value2 }}</Text>
    <DatePicker v-model="value3" unit="second"/>
    <Text>当前选中日期：{{ value3 }}</Text>
    <DateRangePicker v-model="rangeValue1" unit="second" format="YYYY/MM/DD HH:mm:ss"/>
    <Text>当前选中日期：{{ rangeValue1 }}</Text>
    <DateRangePicker v-model="rangeValue2" unit="second" format="YYYY-MMM-DD HH:mm:ss"/>
    <Text>当前选中日期：{{ rangeValue2 }}</Text>
    <DateRangePicker v-model="rangeValue3" unit="second"/>
    <Text>当前选中日期：{{ rangeValue3 }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value1 = ref('2022/11/11 12:00:00')
  const value2 = ref('2022-Nov-12 12:00:00')
  const value3 = ref('2022-10-12 12:00:00')
  const rangeValue1 = ref({ start: '2022/11/11 12:00:00', end: '2022/12/12 12:00:00' })
  const rangeValue2 = ref({ start: '2022-Nov-01 12:00:00', end: '2022-Nov-07 12:00:00' })
  const rangeValue3 = ref({ start: '2022-10-12 12:00:00', end: '2022-11-12 12:00:00' })
</script>
```

##    自定义显示时间文案

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" unit="quarter" :format-text="formatText" />
    <Text>当前选中日期：{{ value }}</Text>
    <DateRangePicker v-model="rangeValue" unit="quarter" :format-text="formatText" />
    <Text>当前选中日期：{{ rangeValue }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, DatePicker, DateRangePicker, Text } from '@xhs/delight'

  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-06-18', end: '2022-11-11' })

  const formatText = (v) => {
    return v.get('year') + ' - ' + '第 ' + v.get('quarter') + ' 季度'
  }
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <DatePicker block/>
  <DateRangePicker class="--space-m-top-default" block/>
</template>

<script setup lang="ts">
  import { DatePicker, DateRangePicker } from '@xhs/delight'
</script>
```

##    不可选择日期和时间

可用 dateDisabled 和 disabledTime 分别禁止选择部分日期和时间，其中 disabledTime 要配合 unit = hour 、minute、second  一起使用

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker :date-disabled="dateDisabled"/>
    <DateRangePicker :date-disabled="dateDisabled"/>

    <DatePicker :disabled-time="disabledTime" unit="second" />
    <DateRangePicker :disabled-time="disabledRangeTime" unit="second" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Text, DatePicker, DateRangePicker } from '@xhs/delight'

  const value = ref('2022-11-08')
  const rangeValue = ref({ start: '2022-11-10', end: '2022-11-20' })

  function dateDisabled(d) {
    return d.date() >= 1 && d.date() <= 7
  }

  function disabledTime() {
    return {
      disabledHours: () => [2, 3],
      disabledMinutes: () => [4, 5],
      disabledSeconds: () => [6, 7]
    }
  }

  function disabledRangeTime(now, type) {
    if (type === 'start') {
      return {
        disabledHours: () => [1, 2],
        disabledMinutes: () => [1, 2],
        disabledSeconds: () => [1, 2]
      }
    }

    return {
      disabledHours: () => [3, 4],
      disabledMinutes: () => [3, 4],
      disabledSeconds: () => [3, 4]
    }
  }
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker disabled/>
    <DateRangePicker disabled/>
    <DateRangePicker v-model="rangeValue1" :disabled="[true, false]"/>
    <DateRangePicker v-model="rangeValue2" :disabled="[false, true]"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from "vue"
  import { Space, DatePicker, DateRangePicker } from '@xhs/delight'
  const rangeValue1 = ref({ start:'2023-01-14' })
  const rangeValue2 = ref({ end:'2023-09-14' })
</script>
```

##    只读

通过 `readonly` 设置只读：

```vue
<template>
  <Space direction="vertical" align="start">
    <DatePicker v-model="value" readonly clearable/>
    <DateRangePicker v-model="rangeValue" readonly clearable/>
    <DateRangePicker v-model="rangeValue1" readonly/>
    <DateRangePicker v-model="rangeValue2" readonly/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from "vue"
  import { Space, DatePicker, DateRangePicker } from '@xhs/delight'
  const rangeValue1 = ref({ start:'2023-01-14' })
  const rangeValue2 = ref({ end:'2023-09-14' })
  const value = ref('2022-11-11')
  const rangeValue = ref({ start: '2022-11-11', end: '2022-12-11' })
</script>
```

##    API 参考

```
type DisabledTime = (now: Dayjs, type?: 'start' | 'end') => {
  disabledHours?: () => number[]
  disabledMinutes?: (selectedHour?: number) => number[]
  disabledSeconds?: (selectedHour?: number, selectedMinute?: number) => number[]
}
```

通过设置 DatePicker 的属性来产生不同的日期选择框样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|日期选择框的选中值|string &#124; string[]|-|
|unit|日期选择框的最小单位|'year' &#124; 'quarter' &#124; 'month' &#124; 'week' &#124; 'date' &#124; 'hour' &#124; 'minute' &#124; 'second'|-|
|format|日期选择框的模版|string|'YYYY-MM-DD HH:mm:ss'（根据 unit 截取掉多余部分）|
|formatText|日期选择显示文案|(v: Dayjs) => string|-|
|dayStartOfWeek|每周的第一天开始于周几，0 - 周日，1 - 周一，以此类推。|0 &#124; 1 &#124; 2 &#124; 3 &#124; 4 &#124; 5 &#124; 6|1|
|placeholder|日期选择框的提示文本|string|-|
|ranges|设置快捷日期选择按钮|&#123; [range:string]: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation) &#125;|-|
|confirm|是否显示确定按钮|boolean|false|
|prefix|日期选择框的前缀内容|string|-|
|suffix|日期选择框的后缀内容|string|-|
|clearable|开启清除选择内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则|(args: &#123; modelValue?: string &#124; string[]; fullValue: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation) &#124; [Dayjs](https://day.js.org/docs/zh-CN/installation/installation)[] &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|hideIndicator|不展示右侧指示器（日历图标）|boolean|false|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean|false|
|readonly|只读|boolean|false|
|dateDisabled|日期选择框禁用日期|(s: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation)) => boolean|-|
|disabledTime|不可选择的时间|DisabledTime|-|
|selectOnly|仅允许通过选择的方式选取日期|boolean|false|
|multiple|日期选择框是否可以多选|boolean|false|
|multiLine|日期选择框是否多行展示选中值|boolean|false|
|maxTagCount|日期选择框展示选中选项的最大数量|number|-|
|maxTagWidth|日期选择框展示选中选项的最大宽度，过长的内容会被省略并在 `Tooltip` 中展示完整内容|number|-|
|popoverClass|日期选择框中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|popoverStyle|日期选择框中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|

### DatePicker 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|日期选择框选中值变化的回调事件|(e: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation) &#124; [Dayjs](https://day.js.org/docs/zh-CN/installation/installation)[]) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|

### DatePicker 插槽 |插槽|说明|
| :- | :- |
|prefix|日期选择框的前缀内容|
|suffix|日期选择框的后缀内容|

### DatePicker TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => boolean|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'error'|
|validateError|校验报错|string|

### DateRangePicker API 参考 通过设置 DateRangePicker 的属性来产生不同的日期范围选择框样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|日期范围选择框的选中值|&#123; start?: string; end?: string; &#125;|-|
|unit|日期范围选择框的最小单位|'year' &#124; 'quarter' &#124; 'month' &#124; 'date' &#124; 'hour' &#124; 'minute' &#124; 'second'|-|
|format|日期范围选择框的模版|string|'YYYY-MM-DD HH:mm:ss'（根据 unit 截取掉多余部分）|
|formatText|日期选择显示文案|(v: Dayjs) => string|-|
|dayStartOfWeek|每周的第一天开始于周几，0 - 周日，1 - 周一，以此类推。|0 &#124; 1 &#124; 2 &#124; 3 &#124; 4 &#124; 5 &#124; 6|1|
|placeholder|日期范围选择框的提示文本|&#123; start?: string; end?: string; &#125;|-|
|ranges|设置快捷日期选择按钮|&#123; [range:string]: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation)[] &#125;|-|
|confirm|是否显示确定按钮|boolean|false|
|prefix|日期范围选择框的前缀内容|string|-|
|suffix|日期范围选择框的后缀内容|string|-|
|clearable|开启清除选择内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则|(args: &#123; modelValue?: &#123; start?: string; end?: string; &#125;; fullValue: &#123; start?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); end?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); &#125; &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean &#124; [boolean, boolean]|false|
|readonly|只读|boolean|false|
|dateDisabled|日期范围选择框禁用日期|(s: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation)) => boolean|-|
|disabledTime|不可选择的时间|DisabledTime|-|
|selectOnly|仅允许通过选择的方式选取日期范围|boolean|false|
|popoverClass|日期范围选择框中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|popoverStyle|日期范围选择框中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|

### DateRangePicker 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|日期范围选择框选中值变化的回调事件|(e: &#123; start?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); end?: [Dayjs](https://day.js.org/docs/zh-CN/installation/installation); &#125;) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input:start|开始日期输入的回调事件|(e: Event) => void|-|
|focus:start|开始日期获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur:start|开始日期失去焦点的回调事件|(e: FocusEvent) => void|-|
|input:end|结束日期输入的回调事件|(e: Event) => void|-|
|focus:end|结束日期获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur:end|结束日期失去焦点的回调事件|(e: FocusEvent) => void|-|
|click:endCell| 点击结束日期Calendar Cell时的回调事件 | (e: MouseEvent) => void | - |
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|
|blur|整体失去焦点的回调事件|(e: FocusEvent) => void|-|

### DateRangePicker 插槽 |插槽|说明|
| :- | :- |
|prefix|日期范围选择框的前缀内容|
|suffix|日期范围选择框的后缀内容|

### DateRangePicker TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => boolean|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'error'|
|validateError|校验报错|string|