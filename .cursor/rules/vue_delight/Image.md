

## 何时使用 需要预览多张图片、图片旋转、图片缩放、图片下载时使用

##    基本使用

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs(images)
}
</script>
```

##    设置图片标题

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]
const setImageTitle = (params) => {
  const { index, transform } = params
  return '图片' + (index + 1) + '-' + '缩放比例: ' + transform.scale
}

const onClick = () => {
  viewImgs(images, {
    title: setImageTitle,
  })
}
</script>
```

##    设置初始图片索引

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs(images, {
    initIndex: 2,
  })
}
</script>
```

##    设置最大最小缩放比例

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs(images, {
    minScale: 0.5,
    maxScale: 4,
  })
}
</script>
```

##    开启拖动图片

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs(images, {
    movable: true,
  })
}
</script>
```

##    开启滚轮缩放

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs(images, {
    zoomOnWheel: true,
  })
}
</script>
```

##    点击遮罩关闭

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs(images, {
    closeOnMask: true,
  })
}
</script>
```

##    自定义操作

`通过配置 actionsLayout 字段来设置操作的排列顺序以及渲染内容, 'step'、'scale'、'rotate'、'download' 是内置的几个操作，你也可以增加其他操作，
操作之间会使用短横线分隔开，如果你需要将多个操作聚合在一起，可以使用数组;如果不需要操作栏，则设置成空数组即可。`

```vue
<template>
  <Button type="primary" @click="onClick" >点击预览</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"
import { OneToOne } from '@xhs/delight/icons'

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs(images, {
    actionsLayout: [
      'step',
      'scale',
      [
        'rotate',
        'download',
        {
          icon: OneToOne,
          callback: (event, { initTransform, setTransform }) => {
            setTransform(initTransform)
          }
        }
      ]
    ]
  })
}
</script>
```

##    全局设置默认配置

通过调用 `viewImgs.config` 方法修改默认的预览配置项

```vue
<template>
  <Button type="primary" @click="onClick" >修改成全局默认可以移动图片、滚轮缩放图片、点击蒙层关闭效果</Button>
</template>

<script setup lang="ts">
import { Button, viewImgs } from "@xhs/delight"
import { OneToOne } from '@xhs/delight/icons'

const images = [
  'https://picasso-static.xiaohongshu.com/fe-platform/a3a757d3f13ce9aa979e18bbb0d9f54b247e2f45.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e710f1fe89a2438dc1b637069eca1ec7e0a99248.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/dceb80af4550b2db2e645db8c3646ecc4701f60c.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/08991c593031ef2758d09cd635ae13c900ac873a.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/38c0b476c8c3b9fc9aa334d0be37787658814997.jpg',
  'https://picasso-static.xiaohongshu.com/fe-platform/e1d2f58b1c71590b45ea3d9d118476e0b4a7b59d.jpg',
]

const onClick = () => {
  viewImgs.config({
    movable: true,
    zoomOnWheel: true,
    closeOnMask: true,
  })

  viewImgs(images)
}
</script>
```

##    API 参考

#### viewImgs 属性 | 属性            | 说明  | 类型       | 默认值  |
| :-------------- | :----------------------------- | :--------- | :-------------------------------------- |
| title | 设置预览器组件的标题 | string \| ((params:CurrParams) => string) | - |
| zIndex | 设置预览组件的层级 | number | 99 |
| initIndex | 设置初始展示的图片索引 | number | 0 |
| minScale | 设置可缩放的最小比例 | number | 0.2 |
| maxScale | 设置可缩放的最大比例 | number | 5 |
| movable | 是否可以通过鼠标拖动图片 | boolean | false |
| zoomOnWheel | 是否可以通过鼠标滚轮缩放图片 | boolean | false |
| closeOnMask | 是否可以点击遮罩层关闭预览组件 | boolean | false |
| esc | 是否开启按下 esc 快捷键关闭预览组件 | boolean | true |
|preload|预加载图片（注意：只有当调用此函数后，会加载剩余图片）|boolean|false|
| actionsLayout | 设置操作栏的排序以及展示内容 | (ActionType \| CustomAction \| (ActionType \| CustomAction)[])[] | ['step', 'scale', ['rotate', 'download']] |

#### CurrParams 类型 | 属性            | 说明  | 类型       |
| :-------------- | :----------------------------- | :--------- |
| url | 当前查看的图片url | string |
| index | 当前图片的索引值 | number |
| initTransform | 初始的图片配置 | Transform |
| transform | 当前的图片配置 | Transform |
| setTransform | 设置可缩放的最大比例 | (trans: Partial\) => void |

#### Transform 类型 | 属性            | 说明  | 类型       |
| :-------------- | :----------------------------- | :--------- |
| scale | 图片缩放的比例 | number |
| rotate | 图片旋转的度数 | number |
| offsetX | 图片x轴的偏移量 | number |
| offsetY | 图片y轴的偏移量 | number |
| enableTransition | 是否开启动画 | boolean |

#### ActionType 类型
```
type ActionType = 'scale' | 'rotate' | 'download' | 'step'
```

#### CustomAction 类型 | 属性            | 说明  | 类型       |
| :-------------- | :----------------------------- | :--------- |
| icon | 渲染的icon | 参考 Icon 组件 |
| disabled | 是否禁用点击 | boolean |
| callback | 点击时触发的回调函数 | (event:MouseEvent, params:CurrParams) => void |
| render | 自定义渲染的内容，优先级最高 | (params:CurrParams) => VNode \| JSX.Element |
