

##    基本使用

通过 `modelValue` 设置 `boolean` 类型的值，用于切换单个状态的 on / off：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch v-model="value" autofocus/>
    <Text>当前值：{{ value }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Switch, Text } from '@xhs/delight'

  const value = ref(false)
</script>
```

##    尺寸

通过 `size` 设置尺寸：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch size="small"/>
    <Switch/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Switch } from '@xhs/delight'
</script>
```

##    展开描述

通过 `expand` 设置 `Switch` 展开描述，展开后描述会通过换行的方式完整展示：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch description="describe content" expand/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Switch } from '@xhs/delight'
</script>
```

##    选中状态

通过 `checked` 设置选中状态，由于 `Switch` 没有 group 的概念，`checked` 与 `modelValue` 有着相同的含义和值：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch/>
    <Switch checked/>
    <Switch v-model:checked="checked"/>
    <Text>当前选中状态：{{ checked }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Switch, Text } from '@xhs/delight'

  const checked = ref(true)
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <Switch checked block/>
</template>

<script setup lang="ts">
  import { Switch } from '@xhs/delight'
</script>
```

##    必填

通过 `required` 设置为必填项，默认在失焦 / 点击 / 手动校验后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch ref="switch1" required/>
    <Text>当前校验状态：{{ switch1?.status }}</Text>
    <Switch ref="switch2" checked required/>
    <Text>当前校验状态：{{ switch2?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Switch, Text } from '@xhs/delight'

  const switch1 = ref()
  const switch2 = ref()
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch ref="s" :validate="validate"/>
    <Text>当前校验状态：{{ s?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Switch, Text } from '@xhs/delight'

  const s = ref()

  function validate({ modelValue, fullValue }) {
    return fullValue.checked === false
  }
</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch ref="s" required validate-timing="immediate"/>
    <Text>当前校验状态：{{ s?.status }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Switch, Text } from '@xhs/delight'

  const s = ref()
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch ref="s" required validate-timing="manual"/>
    <Text>当前校验状态：{{ s?.status }}</Text>
    <Button type="primary" @click="manualValidate">result：{{ result }}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Switch, Button, Text } from '@xhs/delight'

  const s = ref()
  const result = ref()

  function manualValidate() {
    s.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }
</script>
```

##    加载中

通过 `loading` 设置加载中，加载中的同时被会禁用：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch size="small" loading/>
    <Switch loading/>
    <Switch size="small" checked loading/>
    <Switch checked loading/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Switch } from '@xhs/delight'
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <Space direction="vertical" align="start">
    <Switch disabled/>
    <Switch checked disabled/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Switch } from '@xhs/delight'
</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素：

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 Switch 的静态描述文本"
      on-error="这是一段 Switch 的静态错误提示"
    >
      <Switch required/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :on-error="onError"
    >
      <Switch required/>
    </FormItem>
    <FormItem name="3">
      <Switch required/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue, fullValue }">当前输入内容为：{{ fullValue?.modelValue }}</template>
      <template v-slot:onError="{ modelValue, fullValue }">必填项</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { Switch, Form, FormItem } from '@xhs/delight'

  function description({ modelValue, fullValue }) {
    return '当前输入内容为：' + (fullValue?.modelValue === undefined ? '' : fullValue.modelValue)
  }

  function onError({ modelValue, fullValue }) {
    return '必填项'
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    API 参考

```

type SwitchSize = 'small' | 'default'

type validateTiming = 'immediate' | 'blur' | 'manual'

interface AllSwitchProps {
  modelValue?: boolean
  size?: SwitchSize
  name?: string
  value?: boolean
  checked?: boolean
  autofocus?: boolean
  required?: boolean
  validate?:(args: { modelValue: boolean; fullValue: AllSwitchProps }) => string | boolean | Promise<string | boolean>
  validateDelay?: number
  validateTiming?: ValidateTiming
  validating?: boolean
  block?: boolean
  extra?: any
  loading?: boolean
  disabled?: boolean
  'onUpdate:modelValue'?: (e: boolean) => void
  'onUpdate:checked'?: (e: boolean) => void
  onChange?: (e?: AllSwitchProps) => void
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
  onFocus?: (e: FocusEvent) => void
  onBlur?: (e: FocusEvent) => void
}

```

通过设置 Switch 的属性来产生不同的单选框样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue|开关的状态对应的值|boolean|false|
|size|开关的尺寸|'small' &#124; 'default'|'default|'-|
|name|开关对应的 input[type="switch"] 的 name 属性|string|-|
|value|开关的值|boolean|-|
|description|开关的描述|string|-|
|expand|开关的描述是否展开展示|boolean|false|
|checked|开关的状态|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则|(args: &#123; modelValue: boolean; fullValue: AllSwitchProps &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|block|展示为块级元素|boolean|false|
|loading|加载中|boolean|false|
|disabled|禁用|boolean|false|
|extra|可配置的额外属性，会随 `onChange` 事件返回|object|-|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:modelValue|开关的状态对应的值变化的回调事件|(e: boolean) => void|-|
|change|开关的状态对应的值变化的回调事件|(e: AllSwitchProps) => void|-|
|update:checked|开关状态变化的回调事件|(e: boolean) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|

### SWITCH 插槽 |插槽|说明|
| :- | :- |
|description|开关的描述|

### TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|