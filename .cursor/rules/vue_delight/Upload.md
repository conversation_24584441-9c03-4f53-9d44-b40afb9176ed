
import { Link } from '@xhs/delight'

### 何时使用 上传是将信息（文件、图片、视频等）通过网页或者上传工具发布到远程服务器上的过程。

- 当需要上传一个或一些文件时。
- 当需要展现上传的进度时。
- 当需要使用拖拽交互时。

使用前请了解：uploader@3 小红书自建上传方案

##    点击上传

```vue
<template>
  <Upload 
    v-model="fileList"
    :custom-request="customRequest"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const fileList = ref([])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }
  
  // 使用 Uploader 之前请看上文介绍，bizName scene 请找 哈克 申请属于你自己业务的，不要乱用
  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          // 图片场景下必须传 url，用于回显  onSuccess 钩子接受的参数最后也会保留在 response 字段中
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }
</script>
```

##    展示上传列表

```vue
<template>
  <Upload 
    v-model="fileList"
    :custom-request="customRequest"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const fileList = ref([ 
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-1", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    } 
  ])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }
  
  // 使用 Uploader 之前请看上文介绍，bizName scene 请找 哈克 申请属于你自己业务的，不要乱用
  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }
</script>
```

##    照片墙

```vue
<template>
  <Upload 
    v-model="fileList"
    list-type="picture-card"
    :custom-request="customRequest"
    @preview="handlePreview"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload, viewImgs } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const fileList = ref([ 
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-1", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    },
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-2", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    },
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-3", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    },
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-4", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    },
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-5", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    }
  ])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比，腾讯云返回的 percent 是 0.x
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }

  // file: UploadFile  files: UploadFile[]
  function handlePreview(file, files) {
    const index = files.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      viewImgs(
        files.map(item => item.url), 
        {
          initIndex: index,
        }
      )
    }
  }
</script>
```

##    拖拽上传

```vue
<template>
  <Upload 
    v-model="fileList"
    drag
    :custom-request="customRequest"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const fileList = ref([ 
    { 
      "name": "meinv.png", 
      "percent": 20, 
      "status": "uploading", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-1", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    } 
  ])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }
</script>
```

##    限制文件类型

`accept` 接受上传的文件类型，accept 具体类型可参考 input 标签原生属性

```vue
<template>
  <Upload 
    v-model="fileList"
    tip="仅支持 png 格式"
    accept=".png"
    :custom-request="customRequest"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const fileList = ref([])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }
</script>
```

##    多选

`multiple` 设置为 true，便开启多选，同时设置 limit 个数，限制上传文件个数，超出 limit 限制会导致上传失败

```vue
<template>
  <Upload 
    v-model="fileList"
    tip="最多上传 2 个文件"
    multiple
    :limit="2"
    :custom-request="customRequest"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const fileList = ref([])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }
</script>
```

##    禁用

```vue
<template>
  <Upload 
    v-model="fileList"
    disabled
    :custom-request="customRequest"
  />

  <br />

  <Upload 
    v-model="fileList"
    list-type="picture-card"
    disabled
    :custom-request="customRequest"
  />

  <br />

  <Upload 
    v-model="fileList"
    disabled
    drag
    :custom-request="customRequest"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const fileList = ref([])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }
</script>
```

##    自定义交互按钮图标

- showUploadList 控制交互图标是否显示，可传 boolean 值或者对象，详情见 API 文档
- removeIcon、previewIcon、reuploadIcon、downloadIcon 可分别自定义图片墙和文件列表的交互按钮图标

```vue
<template>
  <p>控制交互图标是否显示：</p>
  <Upload 
    v-model="fileList"
    list-type="picture-card"
    :custom-request="customRequest"
    :show-upload-list="{
      showDownloadIcon: true,
      showReuploadIcon: true
    }"
    @preview="handlePreview"
  />

  <p>自定义交互按钮图标：</p>
  <Upload 
    v-model="fileList"
    list-type="picture-card"
    :custom-request="customRequest"
    @preview="handlePreview"
  >
    <template #previewIcon>
      <Icon :icon="AnguishedFace" />
    </template>
    <template #removeIcon>
      <Icon :icon="AngryFace" />
    </template>
  </Upload>
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload, viewImgs } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'
  import { Icon } from '@xhs/delight'
  import { AnguishedFace, AngryFace } from '@xhs/delight/icons'

  const fileList = ref([ 
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-1", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    },
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-2", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    },
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-3", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    }
  ])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比，腾讯云返回的 percent 是 0.x
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }

  // file: UploadFile  files: UploadFile[]
  function handlePreview(file, files) {
    const index = files.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      viewImgs(
        files.map(item => item.url), 
        {
          initIndex: index,
        }
      )
    }
  }
</script>
```

##    自定义文件上传列表

使用 files 插槽进行完全自定义文件上传列表

```vue
<template>
  <Upload 
    v-model="fileList"
    :custom-request="customRequest"
  >
    <template #files="{ files }">
      <Space v-for="(file, index) in files" :key="file.uid" block>
        <Icon :icon="CollectionFiles" size="large" />
        <Text>{{ file.name }}</Text>
        <Button type="light" :icon="Close" @click="() => handleRemove(index)" />
      </Space
    </template>
  </Upload>
</template>

<script setup>
  import { ref } from 'vue'
  import { Upload, Button, Space, Icon, Text } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'
  import { CollectionFiles, Close } from '@xhs/delight/icons'

  const fileList = ref([ 
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-1", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    },
    { 
      "name": "meinv.png", 
      "percent": 100, 
      "status": "success", 
      "size": 486, 
      "raw": "[object File]", 
      "uid": "d-1656486012340-112", 
      "url": "https://picasso-static.xiaohongshu.com/fe-platform/1f49998c8a09c411160293386eebf5b6348bbae7.png" 
    } 
  ])

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }
  
  // 使用 Uploader 之前请看上文介绍，bizName scene 请找 哈克 申请属于你自己业务的，不要乱用
  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function customRequest({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }

  function handleRemove(index) {
    fileList.value.splice(index, 1)
  }
</script>
```

##    API 参考

### Props 属性 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|accept|接受上传的文件类型，详见|string|-|
|customRequest|通过覆盖默认的上传行为，可以自定义自己的上传实现|Function|-|
|disabled|是否禁用|boolean|false|
|modelValue (v-model)|已经上传的文件列表|`UploadFile[]`|[]|
|multiple|是否支持多选文件。开启后按住 ctrl 可选择多个文件。|boolean|false|
|listType|上传列表的内建样式，可选 `picture-card`|string| - |
|limit|上传数量的限制|number|-|
|align|设置照片墙上传按钮的位置，默认位于照片墙的末尾|'start' &#124; 'end'|'end'|
|hideAfterLimit|超过上传的 limit 值时，是否隐藏上传按钮|boolean|false|
|showUploadList|可为 boolean 值或一个对象，值为对象时可自定义设置单个交互按钮是否可见，值为 boolean 值时设置照片墙是否可见|boolean &#124; &#123; showPreviewIcon?: boolean, showRemoveIcon?: boolean, showReuploadIcon?: boolean, showDownloadIcon?: boolean &#125;|true|
|showUploadListClearer|是否展示清除选项|boolean|true|
|drag|是否开启拖拽上传|boolean|false|
|tip|提示文案|string|-|
|tooltipProps|透传属性给 Tooltip 组件（Tooltip只有在图片墙模式下，上传文件失败才会出现）|object|-|
|required|必须上传（仅仅配合表单使用，做未选择效验的）|boolean|false|
|readonly|只读模式，不展示上传触发器和删除 icon （仅在 listType="picture-card" 场景下生效）|boolean|false|

### Events 事件 | Event 名称            | 描述  | 参数       |
| :--------------| :--------| :------|
| change | 当文件上传后，状态发生变化的时候，触发的事件 | (file: UploadFile, files: UploadFile[]) => void |
| preview | 预览事件 | (file: UploadFile, files: UploadFile[]) => void |
| remove | 删除事件 | (file: UploadFile) => void |
| download | 下载事件 | (file: UploadFile) => void |
| reupload | 重传事件 | (file: UploadFile) => void |
| exceed | 文件上传超出 limit 限制，会触发 exceed 事件| () => void |
| clear | 清除事件 | () => void |

### 插槽 |插槽|说明|scoped|
| :- | :- | :- |
|default|触发文件选择框的内容|-|
|tip|提示说明文字（picture-card 模式下不支持 tip 自定义）|-|
|files|缩略图模板的内容，可用于自定义文件展示（picture-card 模式下不支持文件自定义展示）| files: UploadFile[] |
|removeIcon|自定义删除按钮的图标|-|
|previewIcon|自定义预览按钮的图标|-|
|downloadIcon|自定义下载按钮的图标|-|
|reuploadIcon|自定义重传按钮的图标|-|

### UploadFile 继承自 `File`，附带额外属性用于渲染。
```

type UploadStatus = 'ready' | 'uploading' | 'success' | 'fail'

interface DFile extends File {
  uid: string
}

type UploadFile = {
  name: string
  percent?: number
  status: UploadStatus
  size: number
  response?: unknown  // onSuccess 钩子第一个参数，将会被保存到 response 中
  uid: string
  url?: string
  raw?: DFile
  error?: string
}

```

customRequest callback is passed an object with:

  - file: File
  - onProgress: `(event: { percent: number }): void`
  - onError: `(event: { error?: string }): void`
  - onSuccess: `(event: { url?: string; [props: string]: any }): void`