

##    基本使用

用于数字输入：

可以指定 min max 去限制计数器的最小值和最大值

```vue
<template>
  <Space direction="vertical" align="start">
    <InputNumber v-model="value" :min="0" :max="100" />
    当前值：{{ value }}
    <InputNumber v-model="value1" />
    当前值：{{ value1 }}
  </Space>
</template>

<script setup>
  import { ref } from 'vue'
  import { Space, InputNumber } from '@xhs/delight'

  const value = ref()
  const value1 = ref()
</script>
```

##    隐藏步进器

- innerButtons 为 true 时隐藏步进器，仅 hover 时才显示
- controls 控制是否展示步进器

```vue
<template>
  <Space direction="vertical" align="start">
    <InputNumber v-model="value" inner-buttons />
    当前值：{{ value }}
    <InputNumber v-model="value1" :controls="false" />
    当前值：{{ value1 }}
  </Space>
</template>

<script setup>
  import { ref } from 'vue'
  import { Space, InputNumber } from '@xhs/delight'

  const value = ref()
  const value1 = ref()
</script>
```

##    数字输入框渐隐

通过 `fade` 设置渐隐风格：

渐隐风格在非交互状态时会隐去边框、背景和一些内部指示器如加减按钮
 - 通过 showIndicators 强制展示内部指示器
 - 通过 blankHighlight 在没有输入内容时展示高亮

```vue
<template>
  <Space direction="vertical" align="start">
    <InputNumber v-model="value" fade />
    当前值：{{ value }}
    <InputNumber v-model="value1" :fade="{ showIndicators: true }" />
    当前值：{{ value1 }}
    <InputNumber v-model="value2" :fade="{ blankHighlight: true }" />
    当前值：{{ value2 }}
    <InputNumber v-model="value3" fade required validate-timing="immediate"/>
    当前值：{{ value3 }}
  </Space>
</template>

<script setup>
  import { ref } from 'vue'
  import { Space, InputNumber } from '@xhs/delight'

  const value = ref()
  const value1 = ref()
  const value2 = ref()
  const value3 = ref()
</script>
```

##    placeholder

通过 `placeholder` 设置提示文本：

```vue
<template>
  <InputNumber placeholder="提示文本"/>
</template>

<script setup lang="ts">
  import { InputNumber } from '@xhs/delight'
</script>
```

##    增减步长

通过 `step` 设置增减的步长：

```vue
<template>
  <InputNumber v-model="value" :step="0.1"/>
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref(100)
</script>
```

##    精度

```vue
<template>
  <InputNumber v-model="value" :precision="2" @change="change" />
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref(12.12)

  const change = (v) => {
    console.log('change', v)
  }
</script>
```

##    千分位显示

通过thousands设置，注意开启千分位后，精度配置将失效,且最多只有三位精度。

```vue
<template>
  <InputNumber v-model="value" thousands @change="change" />
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref(1234567)

  const change = (v) => {
    console.log('change', v)
  }
</script>
```

##    清除输入

通过 `clearable` 开启清除输入功能：

```vue
<template>
  <InputNumber v-model="value" clearable/>
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref(100)
</script>
```

##    前缀

通过 prefix 或 slots.prefix 设置前缀

```vue
<template>
  <InputNumber prefix="人民币" v-model="value" />
  <br />
  <br />
  <InputNumber v-model="value">
    <template #prefix>
      <Icon :icon="Fire"/>
    </template>
  </InputNumber>
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber, Icon } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'

  const value = ref(100)
</script>
```

##    后缀

通过 suffix 或 slots.suffix 设置后缀

```vue
<template>
  <InputNumber suffix="欧元" v-model="value" />
  <br />
  <br />
  <InputNumber v-model="value">
    <template #suffix>
      %
    </template>
  </InputNumber>
  <p>当前值：{{ value }}</p>
</template>

<script setup>
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref(100)
</script>
```

##    必填

通过 `required` 设置为必填项：

```vue
<template>
  <InputNumber v-model="value" required/>
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref()
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则：

```vue
<template>
  <InputNumber v-model="value" :validate="validate"/>
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref()

  function validate({ modelValue }) {
    return modelValue && modelValue < 10
  }
</script>
```

##    立即校验（包括必填、限制输入长度、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <InputNumber v-model="value" :validate="validate" validate-timing="immediate"/>
  <p>当前值：{{ value }}</p>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputNumber } from '@xhs/delight'

  const value = ref()

  function validate({ modelValue }) {
    return modelValue && modelValue < 10
  }
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space>
    <InputNumber ref="inputNumber" v-model="value" required validate-timing="manual"/>
    当前值：{{ value }}
    <Button type="primary" @click="manualValidate">result: {{result}}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, InputNumber, Button } from '@xhs/delight'

  const inputNumber = ref()
  const value = ref()
  const result = ref()

  function manualValidate() {
    inputNumber.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <InputNumber placeholder="提示文本" block/>
</template>

<script setup lang="ts">
  import { InputNumber } from '@xhs/delight'
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <InputNumber placeholder="提示文本" clearable disabled/>
</template>

<script setup lang="ts">
  import { InputNumber } from '@xhs/delight'
</script>
```

##    只读

通过 `readonly` 设置只读：

```vue
<template>
  <InputNumber :modelValue="100" placeholder="提示文本" clearable readonly/>
</template>

<script setup lang="ts">
  import { InputNumber } from '@xhs/delight'
</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素：

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 InputNumber 的静态描述文本"
      on-error="这是一段 InputNumber 的静态错误提示"
    >
      <InputNumber required placeholder="提示文本" clearable :validate="validate"/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :on-error="onError"
    >
      <InputNumber required placeholder="提示文本" clearable :validate="validate"/>
    </FormItem>
    <FormItem name="3">
      <InputNumber required placeholder="提示文本" clearable :validate="validate"/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue }">当前输入内容为：{{ modelValue }}</template>
      <template v-slot:on-error="{ modelValue }">{{
        modelValue !== undefined
          ? '当前输入内容为：' + modelValue + '，选项要求必须为 2 的倍数'
          :'必填项'
      }}</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { InputNumber, Form, FormItem } from '@xhs/delight'

  function description({ modelValue }) {
    return '当前输入内容为：' + (modelValue || '')
  }

  function onError({ modelValue }) {
    return  modelValue !== undefined
        ? '当前输入内容为：' + modelValue + '，选项要求必须为 2 的倍数'
        :'必填项'
  }

  function validate({ modelValue }) {
    return modelValue % 2 === 0
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    API 参考

通过设置 InputNumber 的属性来产生不同的数字输入框样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue|数字输入框的内容|number|-|
|placeholder|数字输入框的提示文本|string|-|
|innerButtons|hovre 时才展示步进器|boolean|false|
|controls|是否展示步进器|boolean|true|
|prefix|数字输入框的前缀内容|string|-|
|suffix|数字输入框的后缀内容|string|-|
|monospace|数字输入框使用等宽样式（仅对数字生效）|boolean|false|
|step|数字输入框增减的步长|number|1|
|precision|精度|number|-|
|clearable|开启清除输入内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则|(args: &#123; modelValue?: number &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean|false|
|readonly|只读|boolean|false|
|min|设置计数器允许的最小值|number|-Infinity|
|max|设置计数器允许的最大值|number|Infinity|
|fade|渐隐风格|boolean &#124; &#123; blankHighlight?: boolean &#125;|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:modelValue|数字输入框中值变化的回调事件|(e: number &#123; undefined) => void|-|
|change|数字输入框中值变化的回调事件|(e: number &#123; undefined) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|
|enter|键盘回车的回调事件|(e: KeyboardEvent) => void|-|
|increase|步进器上箭头的点击回调事件|(e: MouseEvent) => void|-|
|decrease|步进器下箭头的点击回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|prefix|数字输入框的前缀内容|
|suffix|数字输入框的后缀内容|

### Ref API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|