

##    基本使用

通过 `title` 设置折叠面板的标题, 通过 `content` 或者 `slots.default` 设置折叠面板的内容：

```vue
<template>
  <Collapse title="复兴公园" content="复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。"/>
  <Collapse title="复兴公园">
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
</template>

<script setup lang="ts">
  import { Collapse } from '@xhs/delight'
</script>
```

##    图标

通过 `icon` 设置折叠面板的图标：

```vue
<template>
  <Collapse :icon="ApplicationTwo" title="复兴公园">
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
</template>

<script setup lang="ts">
  import { Collapse } from '@xhs/delight'
  import { ApplicationTwo } from '@xhs/delight/icons'
</script>
```

##    折叠图标

通过 `collapseIcon` 设置折叠面板的折叠图标：

```vue
<template>
  <Collapse title="复兴公园" :collapse-icon="collapse ? Plus : Minus" v-model:collapse="collapse">
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Collapse } from '@xhs/delight'
  import { Plus, Minus } from '@xhs/delight/icons'

  const collapse = ref(false)
</script>
```

##    副标题

通过 `subTitle` 设置折叠面板的副标题：

```vue
<template>
  <Collapse title="复兴公园" sub-title="上海市黄浦区雁荡路105号">
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
  <Collapse :icon="ApplicationTwo" title="复兴公园" sub-title="上海市黄浦区雁荡路105号">
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
</template>

<script setup lang="ts">
  import { Collapse } from '@xhs/delight'
  import { ApplicationTwo } from '@xhs/delight/icons'
</script>
```

##    缩进

通过 `indent` 设置折叠面板的内容缩进至与标题对齐：

```vue
<template>
  <Collapse :icon="ApplicationTwo" title="复兴公园" sub-title="上海市黄浦区雁荡路105号" indent>
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
</template>

<script setup lang="ts">
  import { Collapse } from '@xhs/delight'
  import { ApplicationTwo } from '@xhs/delight/icons'
</script>
```

##    禁用

通过 `disabled` 设置折叠面板禁用：

```vue
<template>
  <Collapse title="复兴公园" disabled>
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
  <Collapse :collapse="false" title="复兴公园" disabled>
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
</template>

<script setup lang="ts">
  import { Collapse } from '@xhs/delight'
</script>
```

##    自定义标题

通过 `slots.title` 自定义折叠面板的标题：

```vue
<template>
  <Collapse title="12">
    <template #title>
      <div :style="{ display: 'flex', alignItems: 'center' }">
        复兴公园
        <Icon :icon="Fire" theme="filled" :style="{ color: 'var(--color-red-6)', marginLeft: 'var(--size-space-small)' }"/>
        <Icon :icon="Fire" theme="filled" :style="{ color: 'var(--color-red-6)' }"/>
        <Icon :icon="Fire" theme="filled" :style="{ color: 'var(--color-red-6)' }"/>
      </div>
    </template>
    复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
  </Collapse>
</template>

<script setup lang="ts">
  import { Collapse, Text, Icon } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'
</script>
```

##    折叠面板组

```vue
<template>
  <CollapseGroup>
    <Collapse title="复兴公园" :collapse="false">
      复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
    </Collapse>
    <Collapse title="复兴公园">
      复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
    </Collapse>
  </CollapseGroup>
</template>

<script setup lang="ts">
  import { Collapse, CollapseGroup } from '@xhs/delight'
</script>
```

##    手风琴效果

```vue
<template>
  <CollapseGroup accordion>
    <Collapse title="复兴公园" :collapse="false">
      复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
    </Collapse>
    <Collapse title="复兴公园">
      复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
    </Collapse>
  </CollapseGroup>
</template>

<script setup lang="ts">
  import { Collapse, CollapseGroup } from '@xhs/delight'
</script>
```

##    Collapse API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|collapse (v-model)|折叠面板折叠状态|boolean|true|
|icon|折叠面板的前缀[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|(p: IconProps) => string|-|
|collapseIcon|折叠面板的折叠[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|(p: IconProps) => string|-|
|title|折叠面板的标题|string|-|
|content|折叠面板的内容|string|-|
|indent|折叠面板内容是否与标题对齐（针对有前缀图标时）|boolean|false|
|disabled|折叠面板是否禁用|boolean|false|
|collapsedHeight|收缩状态下，内容区展示的高度|number|0|

### Collapse 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### Collapse 插槽 |插槽|说明|作用域|
| :- | :- | :- |
|default|折叠面板的内容|-|
|title|折叠面板的标题|-|
|icon|叠面板的前缀图标|
|collapse-icon|折叠 icon|`{ collapse }`|

##    CollapseGroup API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|accordion|折叠面板组是否为手风琴效果（同一时间最多只允许展开一个折叠面板）|boolean|false|

### CollapseGroup 插槽 |插槽|说明|
| :- | :- |
|default|折叠面板组中的折叠面板|