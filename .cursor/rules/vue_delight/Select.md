

##    基本使用

通过 `options` 设置下拉选项：

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value" :options="options"/>
    <Select v-model="value" :options="options" autofocus/>
    <Select v-model="value" :options="options" hide-indicator/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const value = ref('option B')

  const options = [
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
          tooltipTheme: 'light',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
    {
      divider: true,
    },
    {
      label: '选项 A',
      value: 'option A',
      tooltip: 'xxxx',
      tooltipPlacement: 'bottom',
    },
  ]
</script>
```

##    选择器渐隐

通过 `fade` 设置渐隐风格：

渐隐风格在非交互状态时会隐去边框、背景和一些内部指示器如向下箭头
 - 通过 showIndicators 强制展示内部指示器
 - 通过 blankHighlight 在没有输入内容时展示高亮

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value" :options="options" fade/>
    <Select v-model="value" :options="options" :fade="{ showIndicators: true }"/>
    <Select v-model="value" :options="options" :fade="{ blankHighlight: true }"/>
    <Select v-model="value" :options="options" fade required validate-timing="immediate"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const value = ref('option B')

  const options = [
    {
      label: '选项 A',
      value: 'option A'
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    placeholder

通过 `placeholder` 设置提示文本：

```vue
<template>
  <Select placeholder="提示文本" :options="options"/>
</template>

<script setup lang="ts">
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    前缀

通过 `prefix` 或 `slots.prefix` 设置前缀：

```vue
<template>
  <Space direction="vertical">
    <Select prefix="标题" :options="options"/>
    <Select :options="options">
      <template #prefix>
        <Icon :icon="Search"/>
      </template>
    </Select>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Select, Icon } from '@xhs/delight'
  import { Search, Home } from '@xhs/delight/icons'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    后缀

通过 `suffix` 或 `slots.suffix` 设置后缀：

```vue
<template>
  <Space direction="vertical">
    <Select suffix="@xiaohongshu.com" :options="options"/>
    <Select :options="options">
      <template #suffix>
        <Icon :icon="Search"/>
      </template>
    </Select>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Select, Icon } from '@xhs/delight'
  import { Search, Home } from '@xhs/delight/icons'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    清除输入

通过 `clearable` 开启清除输入功能：

```vue
<template>
  <Select v-model="value" clearable :options="options" @clear="handleClear"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  const handleClear = () => {
    console.log('clear', value.value)
  }
</script>
```

##    多选

通过 `multiple` 设置多选：

```vue
<template>
  <Select v-model="value" :options="options" multiple :max-tag-count="4"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref(['option A'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    多行展示

通过 `multiLine` 设置多行展示：

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    prefix="标题"
    suffix="@xiaohongshu.com"
    placeholder="提示文本"
    clearable
    multiple
    multi-line
    filterable
    :style="{ width: '400px' }"
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref(['option A', 'option B', 'option C', 'option D'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    筛选选项

通过 `filterable` 设置选项可筛选，同时可以通过 `filterable` 设置筛选用的输入框类型：

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value1" :options="options" prefix="标题" clearable multiple filterable :max-tag-count="4"/>
    <Select v-model="value2" :options="options" prefix="标题" clearable multiple filterable filter-type="textarea" :filter-value="filterValue" :max-tag-count="4"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref(['option A'])
  const value2 = ref(['option A'])

  const filterValue = ref('只要输入的内容足够长就会换行')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义筛选方法

通过 `filter` 自定义筛选方法：

```vue
<template>
  <Select v-model="value" :options="options" filterable :filter="filter"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '选项 B',
      value: 'option B',
    },
    {
      label: '选项 C',
      value: 'option C',
    },
    {
      label: '选项 D',
      value: 'option D',
    },
    {
      label: '选项 E',
      value: 'option E'
    }
  ]

  function filter(filter, opt) {
    return opt.label.includes(filter)
  }
</script>
```

##    远端筛选

通过 `remote` 设置远端筛选：
- 远端筛选时需通过 `filter` 来变更 `options`，此时 `filter` 只有一个入参 `filterValue`

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    multiple
    filterable
    :filter="filter"
    :loading="loading"
    remote
    :max-tag-count="4"
  >
    <template #empty>
      <div style="padding: var(--size-space-large) 0;">
        <Result title="请输入筛选项进行搜索"/>
      </div>
    </template>
  </Select>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select, Result, useDebounce } from '@xhs/delight'


  const value = ref(['option A'])
  const options = ref([])

  const loading = ref(false)

  const filter = useDebounce(
    filterValue => {
      if (!!filterValue) {
        loading.value = true

        setTimeout(
          () => {
            options.value = Array.from({ length: 10 }, (_, i) => filterValue + '-' + i)
            loading.value = false
          },
          3000
        )
      }
    },
    { delay: 300 },
  )
</script>
```

##    自定义筛选区域

通过 `slots.top` 、 `slots.bottom` 、 `slots.stickTop` 、 `slots.stickBottom` 自定义筛选区域，其中 `slots.stickTop` 、 `slots.stickBottom` 会吸附在 `Dropdown` 顶部 / 底部：

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value1" v-model:filter-value="filterValue" :options="options" :max-dropdown-height="200">
      <template #top>
        <Input
          :style="{
            width: '100%',
            borderRadius: 'var(--size-radius-default) var(--size-radius-default) 0 0'
          }"
          v-model="filterValue"
        />
      </template>
    </Select>
    <Select v-model="value2" v-model:filter-value="filterValue" :options="options" :max-dropdown-height="200">
      <template #bottom>
        <Input
          :style="{
            width: '100%',
            borderRadius: '0 0 var(--size-radius-default) var(--size-radius-default)'
          }"
          v-model="filterValue"
        />
      </template>
    </Select>
    <Select v-model="value3" v-model:filter-value="filterValue" :options="options" :max-dropdown-height="200">
      <template #stickTop>
        <Input
          :style="{
            width: '100%',
            borderRadius: 'var(--size-radius-default) var(--size-radius-default) 0 0'
          }"
          v-model="filterValue"
        />
      </template>
    </Select>
    <Select v-model="value4" v-model:filter-value="filterValue" :options="options" :max-dropdown-height="200">
      <template #stickBottom>
        <Input
          :style="{
            width: '100%',
            borderRadius: '0 0 var(--size-radius-default) var(--size-radius-default)'
          }"
          v-model="filterValue"
        />
      </template>
    </Select>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select, Input } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref('option A')
  const value2 = ref('option A')
  const value3 = ref('option A')
  const value4 = ref('option A')

  const filterValue = ref('A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    创建新选项

通过`allowCreate`设置创建新选项，注意`filterable`必须设置为`true`
```vue
<template>
  <Space direction="vertical">
    <Select v-model="value1" :options="options" prefix="标题" allowCreate clearable multiple  multiLine :max-tag-width="150"  filterable :max-tag-count="4"/>
    <Select v-model="value2" :options="options" prefix="标题" allowCreate clearable multiple  multiLine :max-tag-width="150"  filterable filter-type="textarea" :filter-value="filterValue" :max-tag-count="4"/>
    <Select v-model="value3" :options="options" prefix="标题" allowCreate clearable filterable/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref(['option A'])
  const value2 = ref(['option A'])
  const value3 = ref(['option A'])

  const filterValue = ref('只要输入的内容足够长就会换行')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    必填

通过 `required` 设置为必填项：

```vue
<template>
  <Select v-model="value" :options="options" required/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref()

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则（不能排除存在选项同值的情况，参数不区分单选、多选均为 Option[] 类型的数组）：

```vue
<template>
  <Select v-model="value" :options="options" :validate="validate"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function validate({ modelValue, fullValue }) {
    return fullValue.every(({ value }) => value !== 'option A')
  }
</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <Select v-model="value" :options="options" :validate="validate" validate-timing="immediate"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function validate({ modelValue, fullValue }) {
    return fullValue.every(({ value }) => value !== 'option A')
  }
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space>
    <Select ref="select" v-model="value" :options="options" required validate-timing="manual"/>
    <Button type="primary" @click="manualValidate">result: {{result}}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select, Button } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const select = ref()
  const value = ref()
  const result = ref()

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function manualValidate() {
    select.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }
</script>
```

##    标签最大展示数量

通过 `maxTagCount` 设置标签最大展示数量：

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value1" :max-tag-count="1" :options="options" multiple filterable />
    <Select v-model="value2" :options="options" multiple filterable/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref(['option A', 'option B'])
  const value2 = ref(['option A', 'option B'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    标签最大展示宽度

通过 `maxTagWidth` 设置标签最大展示宽度，并在触发省略时展示 `Tooltip`：

```vue
<template>
  <Select v-model="value" :max-tag-width="150" :options="options" multiple filterable/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref(['option A', 'option B'])

  const options = [
    {
      label: '触发 Tooltip 的长长长长长长长长长长长长长长长选项',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '不触发 Tooltip 的选项',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    下拉选项最大展示宽度

通过 `maxDropdownWidth` 设置下拉选项最大展示宽度：

```vue
<template>
  <Select v-model="value" :max-dropdown-width="300" :options="options" multiple filterable :max-tag-count="4"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref(['option A', 'option B'])

  const options = [
    {
      label: '长长长长长长长长长长长长长长长选项',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '长长长长长长长长长长长长长长长三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    下拉选项最大展示高度

通过 `maxDropdownHeight` 设置下拉选项最大展示高度：

```vue
<template>
  <Select v-model="value" :max-dropdown-height="90" :options="options" multiple filterable :max-tag-count="4"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const value = ref(['option A', 'option E'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '选项 B',
      value: 'option B',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 C',
          value: 'option C',
        },
        {
          label: '选项 D',
          value: 'option D',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 E',
              value: 'option E',
            },
            {
              label: '选项 F',
              value: 'option F',
            },
            {
              label: '选项 G',
              value: 'option G',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    无数据

当 `options` 为空时默认展示无数据状态，可以通过 `slots.empty` 自定义无数据内容：

```vue
<template>
  <Space direction="vertical">
    <Select :options="options"/>
    <Select :options="options" empty-text="No Data" />
    <Select :options="options">
      <template #empty>
        <div style="padding: var(--size-space-large) 0;">
          <Result title="暂无内容"/>
        </div>
      </template>
    </Select>
    <Select :options="options" hide-empty-tip/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Select, Result } from '@xhs/delight'

  const options = []
</script>
```

##    加载中

通过 `loading` 设置加载中，可以通过 `slots.loading` 自定义加载中内容：

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value1" :options="options" loading/>
    <Select v-model="value2" :options="options" loading>
      <template #loading>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing" title="加载中"/>
        </div>
      </template>
    </Select>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select, Result } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref('option A')
  const value2 = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    顶部 / 底部附加项

通过 `slots.top` 、 `slots.bottom` 、 `slots.stickTop` 、 `slots.stickBottom` 设置顶部 / 底部附加项，其中 `slots.stickTop` 、 `slots.stickBottom` 会吸附在 `Dropdown` 顶部 / 底部：

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value1" :options="options" :max-dropdown-height="200">
      <template #top>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Select>
    <Select v-model="value2" :options="options" :max-dropdown-height="200">
      <template #bottom>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Select>
    <Select v-model="value3" :options="options" :max-dropdown-height="200">
      <template #stickTop>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Select>
    <Select v-model="value4" :options="options" :max-dropdown-height="200">
      <template #stickBottom>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Select>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select, Result } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref('option A')
  const value2 = ref('option A')
  const value3 = ref('option A')
  const value4 = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义 tag

通过 `slots.tag` 自定义 tag，tag 会接收到完整的 `option` 以及 `maxWidth` 、 `disabled` 、 `onClose` 共 4 个参数，同时还可以通过 `option.extra` 传递额外需要的数据：

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    multiple
    :max-tag-width="200"
    filterable
    clearable
    :max-tag-count="4"
  >
    <template v-slot:tag="{ option, maxWidth, disabled, onClose }">
      <Tag
        closeable
        :max-width="maxWidth"
        color="blue"
        size="small"
        @close="onClose"
      >
        <Icon
          v-if="option?.extra?.customTagIcon"
          size="large"
          class="--space-m-right-small"
          theme="filled"
          color="primary"
          :icon="option.extra.customTagIcon"
        />
        label: {{ option.label }} - value: {{ option.value }} - maxWidth: {{ maxWidth }} - disabled: {{ disabled }}
      </Tag>
    </template>
  </Select>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select, Tag, Icon } from '@xhs/delight'
  import { Home, Fire } from '@xhs/delight/icons'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
      extra: { customTagIcon: Fire }
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义 option

通过 `slots.option` 自定义 option，slot 会接收到完整的 `option` 以及 `markText`  、 `subOptions` 、 `active` 、 `subActive` 、 `disabled` 、 `hover` 、 `pressing` 共 8 个参数，同时还可以通过 `option.extra` 传递额外需要的数据：

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    multiple
    :max-tag-width="200"
    filterable
    clearable
    :max-tag-count="4"
  >
    <template v-slot:option="{ option, subOptions, active, subActive, disabled, hover, pressing }">
      <div
        :class="[
          'custom-option',
          '--color-bg-fill-light',
          (active || subActive)
            ? '--color-primary'
            : '--color-text-title',
          disabled && 'disabled',
          hover && 'hover',
          pressing && 'pressing'
        ]">
        <Icon theme="filled" color="current" :icon="option.extra?.customOptionIcon"/>
        <Text
          color="current"
          :style="{ flex: 1 }"
        >
          {{ option.label }}
        </Text>
        <Text
          v-if="subOptions.length"
          type="description"
          :style="{ 'margin-right': 'var(--size-space-step-default)' }"
        >
          {{subOptions.length}} +
        </Text>
      </div>
    </template>
  </Select>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select, Icon, Text } from '@xhs/delight'
  import { Home, Fire } from '@xhs/delight/icons'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
      extra: { customOptionIcon: Fire }
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>

<style>
.custom-option {
  display: flex;
  align-items: center;
  overflow: auto;
  border-radius: var(--size-radius-default);
  padding: var(--size-space-step-default);
  grid-gap: var(--size-space-step-default);
  gap: var(--size-space-step-default);
  cursor: pointer;
}
</style>
```

##    仅自定义 option 的部分区域

通过 `slots.optionPrefix` 、  `slots.optionContent`  、 `slots.optionSuffix` 自定义 option，slot 会接收到完整的 `option` 以及 `markText`  、 `subOptions` 、 `active` 、 `subActive` 、 `disabled` 、 `hover` 、 `pressing` 共 8 个参数，同时还可以通过 `option.extra` 传递额外需要的数据：

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    multiple
    :max-tag-width="200"
    filterable
    clearable
    :max-tag-count="4"
  >
    <template v-slot:option-prefix="{ active, subActive }">
      <span
        :class="[
          '--space-m-left-small',
          '--space-p-left-small',
          '--space-m-right-small',
          (active || subActive) && '--color-bg-primary',
        ]"
      />
    </template>
    <template v-slot:optionContent="{ option, subOptions, active, subActive, disabled, hover, pressing }">
      <div
        :class="[
          'custom-option',
          (active || subActive) && '--color-primary'
        ]">
        <Text
          color="current"
          :style="{ flex: 1 }"
        >
          {{ option.label }}
        </Text>
        <div
          v-if="subActive"
          :style="{ display: 'flex', flexDirection: 'column' }"
        >
          <Text type="description" :style="{ lineHeight: '10px' }">
            active: {{subOptions.filter( sub => (sub.active)).length}}
          </Text>
          <Text type="description" :style="{ lineHeight: '10px' }">
            subActive: {{subOptions.filter( sub => (sub.subActive)).length}}
          </Text>
        </div>
      </div>
    </template>
    <template v-slot:optionSuffix="{ subOptions }">
      <Badge
        v-if="subOptions.length"
        dot
      />
    </template>
  </Select>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select, Icon, Text, Badge } from '@xhs/delight'
  import { Home, Fire } from '@xhs/delight/icons'


  const value = ref('option D')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
      extra: { customOptionIcon: Fire }
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>

<style>
.custom-option {
  display: flex;
  align-items: center;
  overflow: auto;
  border-radius: var(--size-radius-default);
  padding: var(--size-space-step-default);
  grid-gap: var(--size-space-step-default);
  gap: var(--size-space-step-default);
  cursor: pointer;
}
</style>
```

##    紧凑布局

通过 `dense` 设置为紧凑布局：
 - 紧凑布局只支持一级选项

```vue
<template>
  <Select
    v-model="value"
    v-model:filter-value="filterValue"
    :options="options"
    multiple
    filterable
    dense
    max-dropdown-height="auto"
    :max-tag-count="4"
  >
    <template #top>
      <Space
        class="--space-p-top-default --space-p-right-default --space-p-left-default"
        direction="vertical"
        align="start"
        block
      >
        <template v-if="!filterValue">
          <Text
            :style="{ lineHeight: 'var(--size-space-large)' }"
            size="small"
            color="text-description"
          >
            最近使用
          </Text>
          <Space size="var(--size-space-step-default)">
            <template v-for="v of recentlyUsedValues">
              <Option :label="v" :value="v"/>
            </template>
          </Space>
          <Divider
            :style="{ width: '100%', backgroundColor: 'var(--color-border-default)' }"
          />
        </template>
        <Text
          :style="{ lineHeight: 'var(--size-space-large)' }"
          size="small"
          color="text-description"
        >
          {{ filterValue ? '搜索结果' : '所有类型' }}
        </Text>
      </Space>
    </template>
  </Select>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select, Text, Divider, Option } from '@xhs/delight'

  const value = ref('美食')
  const filterValue = ref()
  const recentlyUsedValues = ref(['美食', '宠物'])

  const options = [
    { label: '生活记录', value: '生活记录', disabled: true }, '时尚', '美食', '娱乐', '商业财经', '游戏', '素材', '宠物', '美妆', '家居家装', '教育', '出行', '潮流', '母婴', '其他', '影视', '情感', '职场', '人文', '健身减肥', '科技数码', '摄影', '体育运动', '萌娃', '婚嫁', '汽车', '音乐', '医疗健康', '星座命理', '搞笑', '资讯', '社科'
  ]
</script>
```

##    紧凑布局自定义 option

通过 `slots.option` 自定义 option，slot 会接收到完整的 `option` 以及 `markText`  、 `active` 、 `disabled` 、 `hover` 、 `pressing` 共 6 个参数，同时还可以通过 `option.extra` 传递额外需要的数据：

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    multiple
    filterable
    :max-tag-count="4"
    dense
  >
    <template #top>
      <Space
        class="--space-p-top-default --space-p-right-default --space-p-left-default"
        direction="vertical"
        align="start"
        block
      >
        <Text
          :style="{ lineHeight: 'var(--size-space-large)' }"
          size="small"
          color="text-description"
        >
          最近使用
        </Text>
        <Space size="var(--size-space-step-default)">
          <template v-for="v of recentlyUsedValues">
            <Option :label="v" :value="v"/>
          </template>
        </Space>
        <Divider
          :style="{ width: '100%', backgroundColor: 'var(--color-border-default)' }"
        />
        <Text
          :style="{ lineHeight: 'var(--size-space-large)' }"
          size="small"
          color="text-description"
        >
          所有类型
        </Text>
      </Space>
    </template>
    <template #option="{ option, markText, active, disabled }">
      <Tag :color="active ? 'blue' : 'grey'">
        <template v-if="disabled">
          <del>
            <component :is="markText(option.label)"/>
          </del>
        </template>
        <component v-else :is="markText(option.label)"/>
      </Tag>
    </template>
  </Select>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select, Text, Divider, Option, Tag } from '@xhs/delight'

  const value = ref('美食')
  const recentlyUsedValues = ref(['美食', '宠物'])

  const options = [
    { label: '生活记录', value: '生活记录', disabled: true }, '时尚', '美食', '娱乐', '商业财经', '游戏', '素材', '宠物', '美妆', '家居家装', '教育', '出行', '潮流', '母婴', '其他', '影视', '情感', '职场', '人文', '健身减肥', '科技数码', '摄影', '体育运动', '萌娃', '婚嫁', '汽车', '音乐', '医疗健康', '星座命理', '搞笑', '资讯', '社科'
  ]
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    prefix="标题"
    placeholder="提示文本"
    multiple
    filterable
    clearable
    suffix="@xiaohongshu.com"
    :max-tag-count="4"
    block
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select, Space } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <Space direction="vertical">
    <Select
      v-model="value1"
      :options="options"
      disabled
    />

    <Select
      v-model="value2"
      :options="options"
    />

    <Select
      v-model="value3"
      :options="options"
      multiple
    />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select, Space } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref('option A')
  const value2 = ref('option A')
  const value3 = ref(['option A'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
      disabled: true,
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      disabled: true,
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    只读

通过 `readonly` 设置只读：

```vue
<template>
  <Space direction="vertical">
    <Select
      v-model="value1"
      :options="options"
      readonly
      clearable
    />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select, Space } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value1 = ref('option A')
  const value2 = ref('option A')
  const value3 = ref(['option A'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义下拉菜单样式

通过 `dropdownClass` 、 `dropdownStyle` 自定义对应元素的样式，不同于 `maxDropdownWidth` 、 `maxDropdownHeight` 会向下透传， `dropdownClass` 、 `dropdownStyle` 仅对本级的 `Dropdown` 生效：

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    prefix="标题"
    placeholder="提示文本"
    filterable
    clearable
    suffix="@xiaohongshu.com"
    class="custom-select-class"
    :style="{ width: '360px' }"
    dropdown-class="custom-dropdown-class"
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素：

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 Select 的静态描述文本"
      on-error="这是一段 Select 的静态错误提示"
    >
      <Select :options="options" required multiple :validate="validate" :max-tag-count="4"/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :on-error="onError"
    >
      <Select :options="options" required multiple :validate="validate" :max-tag-count="4"/>
    </FormItem>
    <FormItem name="3">
      <Select :options="options" required multiple :validate="validate" :max-tag-count="4"/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue, fullValue }">当前输入内容为：{{ fullValue?.map(v => v.label).join() }}</template>
      <template v-slot:onError="{ modelValue, fullValue }">{{
        modelValue !== undefined
          ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，选项要求必须为 2 的倍数'
          :'必填项'
      }}</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { Select, Form, FormItem } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      label: '二级选项',
      children: [
        {
          label: '选项 B',
          value: 'option B',
        },
        {
          label: '三级选项',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function description({ modelValue, fullValue }) {
    return '当前输入内容为：' + fullValue?.map(v => v.label).join()
  }

  function onError({ modelValue, fullValue }) {
    return  modelValue !== undefined
        ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，选项要求长度必须为 2 的倍数'
        :'必填项'
  }

  function validate({ fullValue }) {
    return fullValue.length % 2 === 0
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    自定义 label、value、children 字段

```vue
<template>
  <Select
    v-model="value"
    :options="options"
    :field-names="{
      label: 'name',
      value: 'id',
      children: 'options'
    }"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import { Select } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
  
  const value = ref('option A')

  const options = [
    {
      name: '选项 A',
      id: 'option A',
    },
    {
      divider: true,
    },
    {
      icon: Home,
      name: '二级选项',
      options: [
        {
          name: '选项 B',
          id: 'option B',
        },
        {
          name: '三级选项',
          options: [
            {
              name: '选项 C',
              id: 'option C',
            },
            {
              name: '选项 D',
              id: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    虚拟滚动

当选项数量过多时可以通过 `virtualize` 开启虚拟滚动，并通过 `virtualizeOptions` 设置虚拟滚动的参数：
 - 通过 `virtualizeOptions` 的 `renderLimit` 可以设置同一时间真正渲染的选项数量，默认为 `20`
 - 那么代价是什么？
   - 一级以上的 `Select` 会急剧的降低虚拟滚动的性能，大多数情况下虚拟滚动请使用一级 `Select` 来展示数据，多级时建议使用 `Cascader`
   - 在开启虚拟滚动后下拉菜单中需要通过 `virtualizeOptions` 设置严格一致的选项高度和选项间距，默认为 `Select` 的默认样式，即 `optionHeight` 为 `32`，`optionGap` 为 `8`

```vue
<template>
  <Space direction="vertical">
    <Select v-model="value" :options="options" filterable virtualize :virtualize-options="{ renderLimit: 200 }"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Select } from '@xhs/delight'

  const value = ref()

  const options = Array.from({ length: 1000}, (_, i) => 'option ' + i)
</script>
```

##    Select API 参考

```

type Trigger = 'hover' | 'click' | 'manual'

interface OptionDivider {
  divider: string | boolean
}

interface Option {
  disabled?: boolean
  icon?: ((p: IconProps) => string) | JSX.Element | VNode
  name?: string
  label?: string
  value?: string | number | boolean
  tooltip?: string
  mark?: string
  extra?: Record<string, any>
  placement?: Placement
  offset?: OffsetOptions
  trigger?: Trigger
  visible?: boolean
  size?: DropdownSize
  children?: (Option | OptionDivider)[]
  maxDropdownWidth?: string | number
  maxDropdownHeight?: string | number
  dropdownClass?: any
  dropdownStyle?: any
  id?: string
  virtualize?: boolean
  virtualizeOptions?: { optionHeight?: number; optionGap?: number; renderLimit?: number; preCreate?: boolean }
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
  [props: string]: any
}

```

通过设置 Select 的属性来产生不同的选择器样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue|选择器的选中值|string &#124; number &#124; (string &#124; number)[]|-|
|revertable|选择器是否可以反选（单选时生效）|boolean|false|
|placeholder|选择器的提示文本|string|-|
|prefix|选择器的前缀内容|string|-|
|suffix|选择器的后缀内容|string|-|
|clearAfterSelect|选择后清除筛选内容（只针对多选场景下）|boolean|true|
|clearable|开启清除选择内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则，不能排除存在 `value` 相同的选项，`fullValue` 参数永远是一个数组|(args: &#123; modelValue?: string &#124; number &#124; (string &#124; number)[]; fullValue: AllSelectProps[] &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|hideIndicator|不展示右侧指示器（向下箭头）|boolean|false|
|hideEmptyTip|不展示无数据提示|boolean|false|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean|false|
|readonly|只读|boolean|false|
|fade|渐隐风格|boolean &#124; &#123; showIndicators?: boolean; blankHighlight?: boolean &#125;|false|
|fieldNames|自定义节点 label、value、children 的字段|`{ label: string; value: string; children: string }`|-|
|options|选择器中的选项|Option[\]|[]|
|multiple|选择器是否可以多选|boolean|false|
|multiLine|选择器是否多行展示选中值|boolean|false|
|allowCreate|选择器是否可以创建新选项（`filterable`必须设置为`true`）|boolean|false|
|filterable|选择器是否可以筛选|boolean|false|
|filterType|选择器筛选使用的输入框类型|'input' &#124; 'textarea'|'input'|
|filter|选择器自定义筛选方法，`filter` 为当前用户输入的文字，`option` 为选项信息（仅当 `remote` 为 `false` 时存在）|(filter: string, option?: Option) => boolean &#124; void|-|
|remote|选择器远端筛选|boolean|false|
|loading|选择器中下拉菜单展示 `loading` 状态|boolean|false|
|emptyText|无选项时显示的文字，也可以使用 empty 插槽设置自定义内容|string|'暂无数据'|
|maxTagCount|选择器展示选中选项的最大数量|number|-|
|maxTagWidth|选择器展示选中选项的最大宽度，过长的内容会被省略并在 `Tooltip` 中展示完整内容|number|-|
|maxDropdownWidth|选择器中下拉菜单最大宽度，过长的内容会被省略|number|-|
|maxDropdownHeight|选择器中下拉菜单最大高度，超过会滚动|number|-|
|showMaxCountPopup|是否显示最大数量的选项弹窗|boolean|true|
|dropdownClass|选择器中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|dropdownStyle|选择器中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|virtualize|虚拟滚动|boolean|false|
|virtualizeOptions|虚拟滚动参数，设置选项的高、选项间的间距、真实渲染的最大选项数|&#123; optionHeight?: number; optionGap?: number; renderLimit?: number; &#125;|&#123; optionHeight: 32, optionGap: 8, renderLimit: 100 &#125;|

### Select 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:modelValue|选择器选中值变化的回调事件，仅返回 `value`|(e: string &#124; number &#124; (string &#124; number)[]) => void|-|
|update:filterValue|选择器筛选值变化的回调事件|(e: string) => void|-|
|change|选择器选中值变化的回调事件，不能排除存在 `value` 相同的选项，`onChange` 返回的永远是一个数组|(e: Option[]) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|

### Select 插槽 |插槽|说明|
| :- | :- |
|prefix|选择器的前缀内容|
|suffix|选择器的后缀内容|
|default|自定义选择器选中内容（单选）|
|tag|自定义选择器标签（多选）|
|option|自定义选择器选项展示内容|
|optionPrefix|自定义选择器选项的前缀|
|optionContent|自定义选择器选项的内容区域|
|optionSuffix|自定义选择器选项的后缀|
|loading|自定义选择器 `loading` 时下拉菜单展示的内容|
|empty|自定义选择器选项为空时下拉菜单展示的内容|
|stickTop|选择器中 一级 下拉菜单的顶部附加项，不随内容滚动|
|top|选择器中 一级 下拉菜单的顶部附加项，随内容滚动|
|bottom|选择器中 一级 下拉菜单的底部附加项，随内容滚动|
|stickBottom|选择器中 一级 下拉菜单的底部附加项，不随内容滚动|

### Select TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|

##    Option API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|divider|是否为分割线（如果设置为 `true` 其他属性将不再生效）|boolean|false|
|id|选项的唯一键（下拉菜单中使用了 `label` + `value` 作为选项的默认唯一键，但当可能存在重复时可以额外声明 `id` 来保证内容发生变化时 `dom` 被正确更新）|string|-|
|icon|选项的[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|(p: IconProps) => string|-|
|name|选项的标题，优先使用 `label`|string|-|
|label|选项的标题|string|-|
|value|选项的值|string &#124; number|-|
|tooltip|选项的提示信息|string|-|
|tooltipTheme|tooltip的主题|'dark' &#124; 'light'|'dark'|
|tooltipPlacement|tooltip展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'top'|
|mark|`mark` 如果是选项 `label` 的子字符串，`label` 中对应文字会被高亮|string|-|
|extra|自定义需要通过 `option` 携带的数据，用法参考 `Select` 的 `自定义 Tag`|object|-|
|diabled|禁用状态|boolean|false|
|placement|下级 下拉菜单展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'right-start'|
|offset|下级 下拉菜单与目标元素的间距|number|0|
|trigger|下级 下拉菜单展示的触发方式|'hover' &#124; 'click'|'hover'|
|size|下级 下拉菜单的尺寸|'small' &#124; 'default' &#124; 'large'|'default'|
|children|下级 下拉菜单中的选项|Option[\]|[]|
|maxDropdownWidth|下级 下拉菜单最大宽度，过长的内容会被省略|number|-|
|maxDropdownHeight|下级 下拉菜单最大高度，超过会滚动|number|-|
|maxCountPopupClass|自定义最大数量浮层弹窗类名|string &#124; object|-|
|maxCountPopupStyle|自定义最大数量浮层弹窗样式|string &#124; object|-|
|dropdownClass|下级 下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|dropdownStyle|下级 下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|virtualize|下级 下拉菜单虚拟滚动|boolean|false|
|virtualizeOptions|下级 下拉菜单虚拟滚动参数，设置选项的高、选项间的间距、真实渲染的最大选项数|&#123; optionHeight?: number; optionGap?: number; renderLimit?: number; &#125;|&#123; optionHeight: 32, optionGap: 8, renderLimit: 20 &#125;|

### Option 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|