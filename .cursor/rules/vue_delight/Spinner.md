

##    尺寸

通过 `size` 设置加载中的尺寸：

```vue
<template>
  <Space>
    <Spinner/>
    <Spinner size="large"/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Spinner } from '@xhs/delight'
</script>
```

##    填色风格

通过 `theme` 设置加载中的填色风格：

```vue
<template>
  <Space direction="vertical">
    <Spinner/>
    <div style="background-color: var(--color-black); padding: var(--size-space-small);"><Spinner theme="reverse"/></div>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Spinner } from '@xhs/delight'
</script>
```

##    文案

通过 `tip` 设置加载中的尺寸：

```vue
<template>
  <Space direction="vertical">
    <Space>
      <Spinner tip="加载中"/>
      <Spinner size="large" tip="加载中"/>
    </Space>
    <Space style="background-color: var(--color-black); padding: var(--size-space-small);">
      <Spinner theme="reverse" tip="加载中"/>
      <Spinner size="large" theme="reverse" tip="加载中"/>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Spinner } from '@xhs/delight'
</script>
```

##    遮罩组件

通过 `slots.default` 设置需要遮罩加载状态的组件：

```vue
<template>
    <Spinner :spinning="loading" tip="加载中" size="large">
      <Collapse :collapse="false" title="复兴公园" :disabled="loading">
        复兴公园是位于中国上海市黄浦区雁荡路105号的一座公园，东至重庆南路、北接雁荡路，西接香山路、皋兰路，南至复兴中路，目前占地7.3公顷。复兴公园拥有超过一百年的历史，曾是上海租界内最大的公园，也是上海最著名一座法式公园，以安静高雅著称。
      </Collapse>
    </Spinner>

    <br />

    <Space>
      <Spinner :spinning="loading">
        <Button :disabled="loading" type="primary">Confirm</Button>
      </Spinner>
      <Button @click="changeLoading">Change Status</Button>
    </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Collapse, Spinner } from '@xhs/delight'

  const loading = ref(true)

  function changeLoading() {
    loading.value = !loading.value
  }

</script>
```

##    API 参考

通过设置 Spinner 的属性来产生不同的加载中样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|spinning|是否为加载中状态|boolean|true|
|size|加载中的尺寸|'default' &#124; 'large'|'default'|
|theme|加载中的填色风格|'default' &#124; 'reverse'|'default'|
|tip|加载中的文案|string|-|
|wrapperClassName|包装器的类属性，Spinner 有默认插槽的时候才会生效|string|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|需要遮罩加载状态的组件|
|tip|加载中的文案|
|indicator|loading 图标|