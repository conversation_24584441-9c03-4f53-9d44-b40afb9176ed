## 基本使用

通过设置 `dot` 、 `count` 或者 `content` 在目标元素四周生成一个标签：

```vue
<template>
  <Space direction="vertical">
    <Badge dot>
      <Button>top-right</Button>
    </Badge>
    <Badge :count="10">
      <Button>top-right</Button>
    </Badge>
    <Badge content="NEW">
      <Button>top-right</Button>
    </Badge>
  </Space>
</template>

<script setup lang="ts">
import { Space, Badge, Button } from "@xhs/delight";
</script>
```

## 位置

通过 `placement` 设置徽标的位置，默认 `top-right`：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Badge :placement="placement" :content="placement">
        <Button>{{ placement }}</Button>
      </Badge>
    </div>
  </Grid>
</template>

<script setup lang="ts">
import { Grid, Badge, Button } from "@xhs/delight";

const placements = [
  "top",
  "top-right",
  "top-left",
  "bottom",
  "bottom-right",
  "bottom-left",
];
</script>

<style scoped>
.popover-grid {
  grid-template:
    "top-left    top    top-right   " min-content
    "bottom-left bottom bottom-right" max-content / 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

## 类型

通过 `type` 设置徽标的类型，默认 `danger`：

```vue
<template>
  <Space direction="vertical" align="start">
    <Badge type="primary" dot>
      <Button>primary</Button>
    </Badge>
    <Badge type="primary" :count="10">
      <Button>primary</Button>
    </Badge>
    <Badge type="warning" dot>
      <Button>warning</Button>
    </Badge>
    <Badge type="warning" :count="10">
      <Button>warning</Button>
    </Badge>
    <Badge dot>
      <Button>danger</Button>
    </Badge>
    <Badge :count="10">
      <Button>danger</Button>
    </Badge>
    <Badge type="success" dot>
      <Button>success</Button>
    </Badge>
    <Badge type="success" :count="10">
      <Button>success</Button>
    </Badge>
  </Space>
</template>

<script setup lang="ts">
import { Space, Badge, Button } from "@xhs/delight";
</script>
```

## 最大数字限制

当通过 `count` 设置展示的数字时，通过 `overflowCount` 限制最大显示数字，超过时展示 `${overflowCount}+`，默认 99：

```vue
<template>
  <Space direction="vertical" align="start">
    <Badge :count="1000" :overflow-count="999">
      <Button>top-right</Button>
    </Badge>
    <Badge :count="1000">
      <Button>top-right</Button>
    </Badge>
  </Space>
</template>

<script setup lang="ts">
import { Space, Badge, Button } from "@xhs/delight";
</script>
```

## 独立使用

不传入 `slots.default` 时作为独立标签使用：

```vue
<template>
  <Space direction="vertical" align="start">
    <Badge dot />
    <Badge :count="10" />
    <Badge content="NEW" />
    <Badge
      class="--color-bg-danger-light"
      :style="{ padding: 'var(--size-space-step-small)' }"
    >
      <template #content>
        <Icon color="danger" :icon="Lock" />
      </template>
    </Badge>
  </Space>
</template>

<script setup lang="ts">
import { Space, Badge, Icon } from "@xhs/delight";
import { Lock } from "@xhs/delight/icons";
</script>
```

## 自定义内容

通过设置 `slots.content` 自定义标签内容：

```vue
<template>
  <Space>
    <Badge
      class="--color-bg-danger-light"
      :style="{ padding: 'var(--size-space-step-small)' }"
    >
      <Button>custom badge</Button>
      <template #content>
        <Icon color="danger" :icon="Lock" />
      </template>
    </Badge>
    <Badge>
      <Button>custom badge</Button>
      <template #content>
        <Tooltip content="tooltip content">
          <Text
            type="h6"
            color="current"
            :style="{
              padding: 'var(--size-space-step-default) var(--size-space-small)',
            }"
          >
            hover me
          </Text>
        </Tooltip>
      </template>
    </Badge>
  </Space>
</template>

<script setup lang="ts">
import { Space, Badge, Button, Icon, Text, Tooltip } from "@xhs/delight";
import { Lock } from "@xhs/delight/icons";
</script>
```

## API 参考

| 属性          | 说明                                                                                                   | 类型                                                                                                  | 默认值      |
| :------------ | :----------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------- | :---------- |
| placement     | 徽标的位置                                                                                             | 'top' &#124; 'top-right' &#124; 'top-left' &#124; 'bottom' &#124; 'bottom-right' &#124; 'bottom-left' | 'top-right' |
| type          | 徽标的类型                                                                                             | 'primary' &#124; 'success' &#124; 'warning' &#124; 'danger'                                           | 'danger'    |
| dot           | 徽标是否展示为一个点                                                                                   | boolean                                                                                               | false       |
| count         | 数字内容                                                                                               | number                                                                                                | -           |
| overflowCount | 当通过 `count` 设置展示的数字时，通过 `overflowCount` 限制最大显示数字，超过时展示 `${overflowCount}+` | number                                                                                                | 99          |
| content       | 文字内容                                                                                               | string                                                                                                | -           |

### 插槽 |插槽|说明|参数|

| :- | :- | :- |
|default|挂载徽标的目标元素，不传入则独立展示|-|
|content|徽标的自定义内容|-|
