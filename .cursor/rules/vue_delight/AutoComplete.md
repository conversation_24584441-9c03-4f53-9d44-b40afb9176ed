## 单行输入框自动完成

通过 `autoComplete` 提示自动完成，通过 `options` 配置选项，通过 `loading` 配置加载中状态，通过 `slots.loading` 自定义加载状态展示内容：

```vue
<template>
  <Space direction="vertical">
    <Input v-model="value" auto-complete :options="options"/>
    <Input v-model="value" auto-complete :options="options" :loading="loading"/>
    <Input v-model="value" auto-complete :options="options" :loading="loading">
      <template #loading>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing" title="加载中"/>
        </div>
      </template>
    </Input>
  <Space>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { Space, Input, Result, useDebounce } from '@xhs/delight'

  const value = ref('')
  const options = ref([])
  const loading = ref(false)

  const remoteSearch = useDebounce(
    v => {
      options.value = v?.trim()
      ? [
        v + '@xiaohongshu.com',
        v + '@xiaohongshu.net',
      ]
      : []

      loading.value = false
    },
    { delay: 1000 }
  )

  watch(
    value,
    v => {
      loading.value = true
      remoteSearch(v)
    }
  )
</script>
```

## 多行输入框自动完成

通过 `autoComplete` 提示自动完成，通过 `options` 配置选项，通过 `loading` 配置加载中状态，通过 `slots.loading` 自定义加载状态展示内容：

```vue
<template>
  <Space direction="vertical" style="width: 100%">
    <TextArea v-model="value" auto-complete :options="options" style="width: 100%"/>
    <TextArea v-model="value" auto-complete :options="options" :loading="loading" style="width: 100%"/>
    <TextArea v-model="value" auto-complete :options="options" :loading="loading" style="width: 100%">
      <template #loading>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing" title="加载中"/>
        </div>
      </template>
    </TextArea>
  <Space>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { Space, TextArea, Result, useDebounce } from '@xhs/delight'

  const value = ref('')
  const options = ref([])
  const loading = ref(false)

  const remoteSearch = useDebounce(
    v => {
      options.value = v?.trim()
      ? [
        v + '@xiaohongshu.com',
        v + '@xiaohongshu.net',
      ]
      : []

      loading.value = false
    },
    { delay: 1000 }
  )

  watch(
    value,
    v => {
      loading.value = true
      remoteSearch(v)
    }
  )
</script>
```

## Input API 参考

通过设置 Input 的属性来产生不同的单行输入框样式：

| 属性              | 说明                                                                                                      | 类型                                                                                                           | 默认值 |
| :---------------- | :-------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------- | :----- |
| modelValue        | 单行输入框的内容                                                                                          | string                                                                                                         | -      |
| type              | 单行输入框的类型                                                                                          | 'text' &#124; 'password'                                                                                       | 'text' |
| placeholder       | 单行输入框的提示文本                                                                                      | string                                                                                                         | -      |
| prefix            | 单行输入框的前缀内容                                                                                      | string                                                                                                         | -      |
| suffix            | 单行输入框的后缀内容                                                                                      | string                                                                                                         | -      |
| monospace         | 单行输入框使用等宽样式（仅对数字生效）                                                                    | boolean                                                                                                        | false  |
| clearable         | 开启清除输入内容按钮                                                                                      | boolean                                                                                                        | false  |
| required          | 必填项                                                                                                    | boolean                                                                                                        | false  |
| requiredError     | 必填报错（Form 中展示）                                                                                   | string                                                                                                         | -      |
| maxLength         | 限制输入内容的长度                                                                                        | number                                                                                                         | -      |
| requiredError     | 超出长度报错（Form 中展示                                                                                 | string                                                                                                         | -      |
| validate          | 自定义校验规则                                                                                            | (args: &#123; modelValue?: string &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt; | -      |
| validateDelay     | 校验延迟（ms）                                                                                            | number                                                                                                         | 100    |
| validateTiming    | 校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验                                                    | 'immediate' &#124; 'blur' &#124; 'manual'                                                                      | 'blur' |
| validating        | 切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态   | boolean                                                                                                        | false  |
| autofocus         | 自动获取焦点                                                                                              | boolean                                                                                                        | false  |
| autoComplete      | 单行输入框是否提示自动完成                                                                                | boolean                                                                                                        | false  |
| options           | 单行输入框自动完成的提示内容                                                                              | string[]                                                                                                       | -      |
| maxDropdownWidth  | 单行输入框中下拉菜单最大宽度，过长的内容会被省略                                                          | number                                                                                                         | -      |
| maxDropdownHeight | 单行输入框中下拉菜单最大高度，超过会滚动                                                                  | number                                                                                                         | -      |
| loading           | 单行输入框中下拉菜单展示 `loading` 状态                                                                   | boolean                                                                                                        | false  |
| dropdownClass     | 单行输入框中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped` | string &#124; array &#124; object                                                                              | -      |
| dropdownStyle     | 单行输入框中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped` | string &#124; array &#124; object                                                                              | -      |
| block             | 展示为块级元素                                                                                            | boolean                                                                                                        | false  |
| disabled          | 禁用                                                                                                      | boolean                                                                                                        | false  |
| fade              | 渐隐风格                                                                                                  | boolean &#124; &#123; showIndicators?: boolean; blankHighlight?: boolean &#125;                                | false  |

### INPUT 事件 |事件|说明|类型|默认值|

| :- | :- | :- | :- |
|update:modelValue|单行输入框中值变化的回调事件|(e: string) => void|-|
|change|单行输入框中值变化的回调事件|(e: string) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|
|enter|键盘回车的回调事件|(e: KeyboardEvent) => void|-|

### INPUT 插槽 |插槽|说明|

| :- | :- |
|prefix|单行输入框的前缀内容|
|suffix|单行输入框的后缀内容|
|loading|单行输入框中下拉菜单展示 `loading` 状态时的内容|

### INPUT TEMPLATE REF API |内容|说明|类型|

| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|

## TEXTAREA API 参考

通过设置 Textarea 的属性来产生不同的多行输入框样式：

| 属性              | 说明                                                                                                      | 类型                                                                                                           | 默认值 |
| :---------------- | :-------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------- | :----- |
| modelValue        | 多行输入框的内容                                                                                          | string                                                                                                         | -      |
| rows              | 多行输入框的行数                                                                                          | number                                                                                                         | 4      |
| placeholder       | 多行输入框的提示文本                                                                                      | string                                                                                                         | -      |
| prefix            | 多行输入框的前缀内容                                                                                      | string                                                                                                         | -      |
| suffix            | 多行输入框的后缀内容                                                                                      | string                                                                                                         | -      |
| monospace         | 多行输入框使用等宽样式（仅对数字生效）                                                                    | boolean                                                                                                        | false  |
| clearable         | 开启清除输入内容按钮                                                                                      | boolean                                                                                                        | false  |
| required          | 必填项                                                                                                    | boolean                                                                                                        | false  |
| requiredError     | 必填报错（Form 中展示）                                                                                   | string                                                                                                         | -      |
| maxLength         | 限制输入内容的长度                                                                                        | number                                                                                                         | -      |
| requiredError     | 超出长度报错（Form 中展示                                                                                 | string                                                                                                         | -      |
| validate          | 自定义校验规则                                                                                            | (args: &#123; modelValue?: string &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt; | -      |
| validateDelay     | 校验延迟（ms）                                                                                            | number                                                                                                         | 100    |
| validateTiming    | 校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验                                                    | 'immediate' &#124; 'blur' &#124; 'manual'                                                                      | 'blur' |
| validating        | 切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态   | boolean                                                                                                        | false  |
| autosize          | 自动扩展                                                                                                  | boolean                                                                                                        | false  |
| autofocus         | 自动获取焦点                                                                                              | boolean                                                                                                        | false  |
| autoComplete      | 多行输入框是否提示自动完成                                                                                | boolean                                                                                                        | false  |
| options           | 多行输入框自动完成的提示内容                                                                              | string[]                                                                                                       | -      |
| maxDropdownWidth  | 多行输入框中下拉菜单最大宽度，过长的内容会被省略                                                          | number                                                                                                         | -      |
| maxDropdownHeight | 多行输入框中下拉菜单最大高度，超过会滚动                                                                  | number                                                                                                         | -      |
| loading           | 多行输入框中下拉菜单展示 `loading` 状态                                                                   | boolean                                                                                                        | false  |
| dropdownClass     | 多行输入框中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped` | string &#124; array &#124; object                                                                              | -      |
| dropdownStyle     | 多行输入框中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped` | string &#124; array &#124; object                                                                              | -      |
| block             | 展示为块级元素                                                                                            | boolean                                                                                                        | false  |
| disabled          | 禁用                                                                                                      | boolean                                                                                                        | false  |
| fade              | 渐隐风格                                                                                                  | boolean &#124; &#123; showIndicators?: boolean; blankHighlight?: boolean &#125;                                | false  |

### TEXTAREA 事件 |事件|说明|类型|默认值|

| :- | :- | :- | :- |
|update:modelValue|多行输入框中值变化的回调事件|(e: string) => void|-|
|change|多行输入框中值变化的回调事件|(e: string) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|
|enter|键盘回车的回调事件|(e: KeyboardEvent) => void|-|

### TEXTAREA 插槽 |插槽|说明|

| :- | :- |
|prefix|多行输入框的前缀内容|
|suffix|多行输入框的后缀内容|
|loading|多行输入框中下拉菜单展示 `loading` 状态时的内容|

### TEXTAREA TEMPLATE REF API |内容|说明|类型|

| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|
