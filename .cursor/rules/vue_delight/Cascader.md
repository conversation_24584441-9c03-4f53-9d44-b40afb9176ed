

##    基本使用

通过 `options` 设置下拉选项：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" :options="options" multiple @change="handleChange" />
    <Cascader v-model="value" :options="options" autofocus/>
    <Cascader v-model="value" :options="options" hide-after-select/>
    <Cascader v-model="value" :options="options" hide-indicator/> 
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'

  const value = ref('')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: 'Parent C',
      value: 'Parent C',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
          disabled: true
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
              disabled: false,
              children: [
                {
                  label: 'E1',
                  value: 'E1'
                }, {
                  label: 'F1',
                  value: 'F1',
                  disabled: true
                }
              ]
            },
            {
              label: '选项 D',
              value: 'option D',
              children: [
                {
                  label: 'E',
                  value: 'E',
                }, {
                  label: 'F',
                  value: 'F',
                  disabled: true
                }
              ]
            },
          ]
        },
      ]
    },
  ]


  const handleChange = () => {
    console.log('value', value.value)
  }

</script>
```

##    移入展开

通过移入展开下级菜单，点击完成选择。

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" :options="options" expand-trigger="hover" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'

  const value = ref('option B')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    选择即改变

通过 `changeOnSelect` 设置在单选时允许选中父级选项：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" :options="options" change-on-select/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'

  const value = ref('option B')

  const options = [
    {
      label: '选项 A-1',
      value: 'option A-1',
    },
    {
      label: '选项 A-2',
      value: 'option A-2',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B-1',
          value: 'option B-1',
        },
        {
          label: '选项 B-2',
          value: 'option B-2',
        },
        {
          label: '选项 B-3',
          value: 'option B-3',
        },
        {
          label: '选项 B-4',
          value: 'option B-4',
        },
        {
          label: '选项 B-5',
          value: 'option B-5',
        },
        {
          label: '选项 B-6',
          value: 'option B-6',
        },
        {
          label: '选项 B-7',
          value: 'option B-7',
        },
        {
          label: '选项 B-8',
          value: 'option B-8',
        },
        {
          label: '选项 B-9',
          value: 'option B-9',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    分隔符

通过 `separator` 设置分隔符：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" :options="options" separator="->"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'

  const value = ref('option B')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    选择器渐隐

通过 `fade` 设置渐隐风格：

渐隐风格在非交互状态时会隐去边框、背景和一些内部指示器如向下箭头
 - 通过 showIndicators 强制展示内部指示器
 - 通过 blankHighlight 在没有输入内容时展示高亮

```vue
<template>
  <Space direction="vertical">
    <Cascader :options="options" fade/>
    <Cascader :options="options" :fade="{ showIndicators: true }"/>
    <Cascader :options="options" :fade="{ blankHighlight: true }"/>
    <Cascader :options="options" fade required validate-timing="immediate"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'

  const value1 = ref('option B')
  const value2 = ref('option B')
  const value3 = ref('option B')
  const value4 = ref('option B')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    placeholder

通过 `placeholder` 设置提示文本：

```vue
<template>
  <Cascader placeholder="提示文本" :options="options"/>
</template>

<script setup lang="ts">
  import { Cascader } from '@xhs/delight'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    前缀

通过 `prefix` 或 `slots.prefix` 设置前缀：

```vue
<template>
  <Space direction="vertical">
    <Cascader prefix="标题" :options="options"/>
    <Cascader :options="options">
      <template #prefix>
        <Icon :icon="Box"/>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Cascader, Icon } from '@xhs/delight'
  import { Box } from '@xhs/delight/icons'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    后缀

通过 `suffix` 或 `slots.suffix` 设置后缀：

```vue
<template>
  <Space direction="vertical">
    <Cascader suffix="@xiaohongshu.com" :options="options"/>
    <Cascader :options="options">
      <template #suffix>
        <Icon :icon="Box"/>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Cascader, Icon } from '@xhs/delight'
  import { Box } from '@xhs/delight/icons'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    清除输入

通过 `clearable` 开启清除输入功能：

```vue
<template>
  <Cascader v-model="value" clearable :options="options"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'

  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    多选

通过 `multiple` 设置多选：

```vue
<template>
  <Cascader v-model="value" :options="options" multiple/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref(['option B', 'level 3', 'Parent C'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: 'Parent C',
      value: 'Parent C',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    动态加载

通过 `loadData` 设置动态加载，`loadData`需设置为函数，该函数第一个参数为`option`，类型为`Option`，第二个参数是回调函数，当请求完数据后调用此回调函数，该回调函数接收一个类型为`Option[]`的数组，若数据未能请求成功，需传递一个空数组。
注意: 如需动态加载数据，需要将`option`的`children`设置为空数组，且多选情况下默认选择父级时会全选子级，如需解绑父子级需设置checkStrictly。
```vue
<template>
  <Cascader v-model="value1" :options="options" clearable :loadData="loadData"/>
  <br/>
  <br/>
  <Cascader v-model="value2" :options="options" clearable multiple :loadData="loadData"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value1 = ref('option A')
  const value2 = ref(['option A'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: 'Parent C',
      value: 'Parent C',
      children: [],
      disableSelect: true,
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [],
      disableSelect: true,
    },
  ]

function loadData(option,resolve){
    if(option.value === 'level 2'){
      setTimeout(() => {
        resolve([
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [],
          disableSelect: true,
        },
      ])
      },1000)
    }else if(option.value === 'level 3'){
      setTimeout(() => {
        resolve([
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
              children: [],
              disableSelect: true,
            },
          ])
      },1000)
    }else if(option.value === 'option D'){
      setTimeout(() => {
        resolve([])
      },1000)
    }else if(option.value === 'Parent C'){
      setTimeout(() => {
        resolve([
          {
            label: 'C-1',
            value: 'C-1',
          },
          {
            label: 'C-2',
            value: 'C-2',
          },
        ])
      },1000)
    }
  }
</script>
```

##    选择任意一级选项

在单选模式下，你只能选择叶子节点；而在多选模式下，勾选父节点真正选中的都是叶子节点。 启用该功能后，可让父子节点取消关联，选择任意一级选项。

可通过 props.checkStrictly = true 来设置父子节点取消选中关联，从而达到选择任意一级选项的目的。

```vue
<template>
  <Cascader v-model="value1" :options="options" check-strictly placeholder="选择任意一级选项" />

  <br />
  <br />

  <Cascader v-model="value2" :options="options" multiple check-strictly placeholder="选择任意一级选项" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value1 = ref('')
  const value2 = ref([])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    禁用某一项是否可选中

使用 disableSelect 禁止某一项是否可以被选中，但是可以触发展示下级菜单。

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" checkStrictly expand-trigger="hover" :options="options" placeholder="单选" />
    <Cascader v-model="value1" multiple checkStrictly :max-tag-count="2" expand-trigger="hover" :options="options" placeholder="多选" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'

  const value = ref('')
  const value1 = ref([])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
      disableSelect: true,
      children: [
        {
          label: '选项 A-2-1',
          value: 'option A-2-1',
        },
        {
          label: '选项 A-2-2',
          value: 'option A-2-2',
        },
        {
          label: '选项 A-2-3',
          value: 'option A-2-3',
          children: [
            {
              label: '选项 A-3-1',
              value: 'option A-3-1',
            },
            {
              label: '选项 A-3-2',
              value: 'option A-3-2',
            },
          ]
        },
      ]
    },
    {
      label: '选项 B',
      value: 'level 2',
      disableSelect: true,
      children: [
        {
          label: '选项 B-2-1',
          value: 'option B-2-1',
        },
        {
          label: '选项 B-2-2',
          value: 'option B-2-2',
        },
        {
          label: '选项 A-2-3',
          value: 'option A-2-3',
          children: [
            {
              label: '选项 B-3-1',
              value: 'option B-3-1',
            },
            {
              label: '选项 B-3-2',
              value: 'option B-3-2',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    多行展示

通过 `multiLine` 设置多行展示：

```vue
<template>
  <Cascader
    v-model="value"
    :options="options"
    prefix="标题"
    suffix="@xiaohongshu.com"
    placeholder="提示文本"
    clearable
    multiple
    multi-line
    filterable
    :style="{ width: '400px' }"
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref(['option A', 'option B', 'option C', 'option D', 'option E'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
    {
      label: '选项 E',
      value: 'option E',
    },
  ]
</script>
```

##    仅叶子节点

通过 `leafOnly` 设置多选时 `modelValue` 只包含叶子节点：

```vue
<template>
  <Cascader v-model="value" :options="options" multiple leaf-only/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref(['option A'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自动合并 value

通过 `autoMergeValue` 来设置多选时选中祖先节点后 `modelValue` 不包含对应的子孙节点：
- 默认为 `true`
- 当 `autoMergeValue` 和 `leafOnly` 同时开启时，后者优先级更高

```vue
<template>
  <Cascader v-model="value" :options="options" multiple :auto-merge-value="false"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref(['option A'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    筛选选项

通过 `filterable` 设置选项可筛选，同时可以通过 `filterable` 设置筛选用的输入框类型：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value1" :options="options" prefix="标题" clearable multiple filterable/>
    <Cascader v-model="value2" :options="options" prefix="标题" clearable multiple filterable filter-type="textarea" :filter-value="filterValue"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'


  const value1 = ref(['option A'])
  const value2 = ref(['option A'])

  const filterValue = ref('只要输入的内容足够长就会换行')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
              children: [
                {
                  label: '选项 E',
                  value: 'option E',
                },
                {
                  label: '选项 F',
                  value: 'option F',
                },
              ]
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义筛选方法

通过 `filter` 自定义筛选方法：

```vue
<template>
  <Cascader v-model="value" :options="options" multiple filterable :filter="filter"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref(['option A'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function filter(filter, opt) {
    console.log(filter, opt)

    return opt.label === '选项 D'
  }
</script>
```

##    远端筛选

通过 `remote` 设置远端筛选：
- 远端筛选时需通过 `filter` 来变更 `options`，此时 `filter` 只有一个入参 `filterValue`

```vue
<template>
  <Cascader
    v-model="value"
    :options="options"
    multiple
    filterable
    :filter="filter"
    :loading="loading"
    remote
  >
    <template #empty>
      <div style="padding: var(--size-space-large) 0;">
        <Result title="请输入筛选项进行搜索"/>
      </div>
    </template>
  </Cascader>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader, Result, useDebounce } from '@xhs/delight'


  const value = ref(['option A'])
  const options = ref([])

  const loading = ref(false)

  function getOptions(v) {
    return [
      {
        label: v + 'A',
        value: v + 'A',
      },
      {
        label: '二级选项',
        value: v + '2',
        children: [
          {
            label: v + 'B',
            value: v + 'B',
          },
          {
            label: '三级选项',
            value: v + '3',
            children: [
              {
                label: v + 'C',
                value: v + 'C',
              },
              {
                label: v + 'D',
                value: v + 'D',
              },
            ]
          },
        ]
      },
    ]
  }

  const filter = useDebounce(
    filterValue => {
      if (!!filterValue) {
        loading.value = true

        setTimeout(
          () => {
            options.value = getOptions(filterValue)
            loading.value = false
          },
          3000
        )
      }
    },
    { delay: 300 },
  )
</script>
```

##    自定义筛选区域

通过 `slots.top` 、 `slots.bottom` 自定义筛选区域，会吸附在 `Dropdown` 顶部 / 底部：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value1" v-model:filterValue="filterValue" :options="options" :max-dropdown-height="200">
      <template #top>
        <Input
          :style="{
            width: '100%',
            borderRadius: 'var(--size-radius-default) var(--size-radius-default) 0 0'
          }"
          v-model="filterValue"
        />
      </template>
    </Cascader>
    <Cascader v-model="value2" v-model:filterValue="filterValue" :options="options" :max-dropdown-height="200">
      <template #bottom>
        <Input
          :style="{
            width: '100%',
            borderRadius: '0 0 var(--size-radius-default) var(--size-radius-default)'
          }"
          v-model="filterValue"
        />
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader, Input } from '@xhs/delight'


  const value1 = ref('option A')
  const value2 = ref('option A')
  const value3 = ref('option A')
  const value4 = ref('option A')

  const filterValue = ref('A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    必填

通过 `required` 设置为必填项：

```vue
<template>
  <Cascader v-model="value" :options="options" required/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref()

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则（不能排除存在选项同值的情况，参数不区分单选、多选均为 Option[] 类型的数组）：

```vue
<template>
  <Cascader v-model="value" :options="options" :validate="validate"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function validate({ modelValue, fullValue }) {
    return fullValue.every(({ value }) => value !== 'option A')
  }
</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <Cascader v-model="value" :options="options" :validate="validate" validate-timing="immediate"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function validate({ modelValue, fullValue }) {
    return fullValue.every(({ value }) => value !== 'option A')
  }
</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space>
    <Cascader ref="cascader" v-model="value" :options="options" required validate-timing="manual"/>
    <Button type="primary" @click="manualValidate">result: {{result}}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader, Button } from '@xhs/delight'


  const cascader = ref()
  const value = ref()
  const result = ref()

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function manualValidate() {
    cascader.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }
</script>
```

##    标签最大展示数量

通过 `maxTagCount` 设置标签最大展示数量：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value1" :max-tag-count="1" :options="options" multiple filterable/>
    <Cascader v-model="value2" :options="options" multiple filterable/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'


  const value1 = ref(['option A', 'option B'])
  const value2 = ref(['option A', 'option B'])

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    标签最大展示宽度

通过 `maxTagWidth` 设置标签最大展示宽度，并在触发省略时展示 `Tooltip`：

```vue
<template>
  <Cascader v-model="value" :max-tag-width="150" :options="options" multiple filterable/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'


  const value = ref(['option A', 'option B'])

  const options = [
    {
      label: '选选选选选选选选选选选选选选选选选选选选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    无数据

当 `options` 为空时默认展示无数据状态，可以通过 `slots.empty` 自定义无数据内容：

```vue
<template>
  <Space direction="vertical">
    <Cascader :options="options" />
    <Cascader :options="options" empty-text="No Data" />
    <Cascader :options="options">
      <template #empty>
        <div style="padding: var(--size-space-large) 0;">
          <Result title="暂无内容"/>
        </div>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Cascader, Result } from '@xhs/delight'

  const options = []
</script>
```

##    加载中

通过 `loading` 设置加载中，可以通过 `slots.loading` 自定义加载中内容：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value1" :options="options" loading/>
    <Cascader v-model="value2" :options="options" loading>
      <template #loading>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing" title="加载中"/>
        </div>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader, Result } from '@xhs/delight'


  const value1 = ref('option A')
  const value2 = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    顶部 / 底部附加项

通过 `slots.top` 、 `slots.bottom` 设置顶部 / 底部附加项，会吸附在 `Dropdown` 顶部 / 底部：

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value1" :options="options" :max-dropdown-height="200">
      <template #top>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Cascader>
    <Cascader v-model="value2" :options="options" :max-dropdown-height="200">
      <template #bottom>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader, Result } from '@xhs/delight'


  const value1 = ref('option A')
  const value2 = ref('option A')
  const value3 = ref('option A')
  const value4 = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    自定义 tag

通过 `slots.tag` 自定义 tag，tag 会接收到完整的 `option` 以及 `maxWidth` 、 `disabled` 、 `onClose` 共 4 个参数，同时还可以通过 `option.extra` 传递额外需要的数据：

```vue
<template>
  <Cascader
    v-model="value"
    :options="options"
    multiple
    :max-tag-width="200"
    filterable
    clearable
  >
    <template v-slot:tag="{ option, maxWidth, disabled, onClose }">
      <Tag
        closeable
        :max-width="maxWidth"
        color="blue"
        size="small"
        @close="onClose"
      >
        <Icon
          v-if="option?.extra?.customTagIcon"
          size="large"
          class="--space-m-right-small"
          theme="filled"
          color="primary"
          :icon="option.extra.customTagIcon"
        />
        label: {{ option.label }} - value: {{ option.value }} - maxWidth: {{ maxWidth }} - disabled: {{ disabled }}
      </Tag>
    </template>
  </Cascader>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader, Tag, Icon } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
      extra: { customTagIcon: Fire },
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <Cascader
    v-model="value"
    :options="options"
    prefix="标题"
    placeholder="提示文本"
    multiple
    filterable
    clearable
    suffix="@xiaohongshu.com"
    block
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader, Space } from '@xhs/delight'


  const value = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <Space direction="vertical">
    <Cascader
      v-model="value1"
      :options="options"
      prefix="标题"
      placeholder="提示文本"
      filterable
      clearable
      suffix="@xiaohongshu.com"
      disabled
      :style="{ width: '360px' }"
    />
    <Cascader
      v-model="value2"
      :options="options"
      prefix="标题"
      placeholder="提示文本"
      filterable
      clearable
      suffix="@xiaohongshu.com"
      :style="{ width: '360px' }"
    />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader, Space } from '@xhs/delight'


  const value1 = ref('option A')
  const value2 = ref('option A')

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      disabled: true,
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]
</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素：

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 Cascader 的静态描述文本"
      on-error="这是一段 Cascader 的静态错误提示"
    >
      <Cascader :options="options" required multiple :validate="validate"/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :on-error="onError"
    >
      <Cascader :options="options" required multiple :validate="validate"/>
    </FormItem>
    <FormItem name="3">
      <Cascader :options="options" required multiple :validate="validate"/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue, fullValue }">当前输入内容为：{{ fullValue?.map(v => v.label).join() }}</template>
      <template v-slot:onError="{ modelValue, fullValue }">{{
        modelValue !== undefined
          ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，选项要求必须为 2 的倍数'
          :'必填项'
      }}</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { Cascader, Form, FormItem } from '@xhs/delight'

  const options = [
    {
      label: '选项 A',
      value: 'option A',
    },
    {
      label: '二级选项',
      value: 'level 2',
      children: [
        {
          label: '选项 B',
          value: 'option B',
          tooltip: '选项的提示信息',
        },
        {
          label: '三级选项',
          value: 'level 3',
          children: [
            {
              label: '选项 C',
              value: 'option C',
            },
            {
              label: '选项 D',
              value: 'option D',
            },
          ]
        },
      ]
    },
  ]

  function description({ modelValue, fullValue }) {
    return '当前输入内容为：' + fullValue?.map(v => v.label).join()
  }

  function onError({ modelValue, fullValue }) {
    return  modelValue !== undefined
        ? '当前输入内容为：' + fullValue?.map(v => v.label).join() + '，选项要求长度必须为 2 的倍数'
        :'必填项'
  }

  function validate({ fullValue }) {
    return fullValue.length % 2 === 0
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    自定义 label、value、children 字段

```vue
<template>
  <Cascader v-model="value" :options="options" :field-names="fieldNames" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader } from '@xhs/delight'

  const value = ref('')

  const fieldNames = { label: 'name', value: 'code', children: 'items' }

  const options = [
    {
      code: 'zhejiang',
      name: 'Zhejiang',
      items: [
        {
          code: 'hangzhou',
          name: 'Hangzhou',
          items: [
            {
              code: 'xihu',
              name: 'West Lake',
            },
          ],
        },
      ],
    },
    {
      code: 'jiangsu',
      name: 'Jiangsu',
      items: [
        {
          code: 'nanjing',
          name: 'Nanjing',
          items: [
            {
              code: 'zhonghuamen',
              name: 'Zhong Hua Men',
            },
          ],
        },
      ],
    }
  ]
</script>
```

##    API 参考

```
interface Option {
  disabled?: boolean
  name?: string
  label?: string
  value?: string | number | boolean
  tooltip?: string
  extra?: Record<string, any>
  trigger?: 'click' | 'hover'
  children?: Option[]
  id?: string
  disableSelect?: boolean
  isloading?: boolean
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
}
```

通过设置 Cascader 的属性来产生不同的选择器样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|选择器的选中值|string &#124; number &#124; (string &#124; number)[]|-|
|revertable|选择器是否可以反选（单选时生效）|boolean|false|
|placeholder|选择器的提示文本|string|-|
|prefix|选择器的前缀内容|string|-|
|suffix|选择器的后缀内容|string|-|
|clearable|开启清除选择内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|validate|自定义校验规则，不能排除存在 `value` 相同的选项，`fullValue` 参数永远是一个数组|(args: &#123; modelValue?: string &#124; number &#124; (string &#124; number)[]; fullValue: AllCascaderProps[] &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|loadData|动态加载|(option: Option, resolve: (data: Option[]) => void) =>  void|-|
|autofocus|自动获取焦点|boolean|false|
|hideAfterSelect|选中值后关闭下拉菜单|boolean|false|
|hideIndicator|不展示右侧指示器（向下箭头）|boolean|false|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean|false|
|fade|渐隐风格|boolean &#124; &#123; showIndicators?: boolean; blankHighlight?: boolean &#125;|false|
|options|选择器中的选项|Option[\]|[]|
|multiple|选择器是否可以多选|boolean|false|
|multiLine|选择器是否多行展示选中值|boolean|false|
|filterable|选择器是否可以筛选|boolean|false|
|filterType|选择器筛选使用的输入框类型|'input' &#124; 'textarea'|'input'|
|filter|选择器自定义筛选方法，`filter` 为当前用户输入的文字，`option` 为选项信息|(filter: string, option: Option) => boolean|-|
|remote|选择器远端筛选|boolean|false|
|changeOnSelect|单选时下允许选中父级选项|boolean|false|
|leafOnly|多选时 `modelValue` 只包含叶子节点|boolean|false|
|autoMergeValue|多选时选中祖先节点后 `modelValue` 不包含对应的子孙节点，当 `autoMergeValue` 和 `leafOnly` 同时开启时，后者优先级更高|boolean|true|
|loading|选择器中下拉菜单展示 `loading` 状态|boolean|false|
|separator|分隔符|string|'/'|
|maxTagCount|选择器展示选中选项的最大数量|number|-|
|maxTagWidth|选择器展示选中选项的最大宽度，过长的内容会被省略并在 `Tooltip` 中展示完整内容|number|-|
|maxCountPopupClass|自定义最大数量浮层弹窗类名|string &#124; object|-|
|maxCountPopupStyle|自定义最大数量浮层弹窗样式|string &#124; object|-|
|showMaxCountPopup|是否显示最大数量的选项弹窗|boolean|true|
|virtualize|虚拟滚动|boolean|false|
|virtualizeOptions|虚拟滚动参数，设置选项的高、选项间的间距、真实渲染的最大选项数|&#123; optionHeight?: number; optionGap?: number; renderLimit?: number; &#125;|&#123; optionHeight: 32, optionGap: 8, renderLimit: 100 &#125;|
|checkStrictly|选择任意一级选项|boolean|false|
|expandTrigger|次级菜单的展开方式，可选 'click' 和 'hover'|string|click|
|showAllLevels|输入框中是否显示选中值的完整路径（单选时有效）|boolean|true|
|fieldNames|自定义 options 中 label name children 的字段|`{ label: 'label', value: 'value', children: 'children' }`|-|
|emptyText|无选项时显示的文字，也可以使用 empty 插槽设置自定义内容|string|'暂无数据'|
|includeDuplicateTags|多选的场景下，会出现重复的 tag（options中出现多个 value 值相同的场景下），判断用户是都需要|boolean|true|

### Cascader 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:filterValue|选择器筛选值变化的回调事件|(e: string) => void|-|
|change|选择器选中值变化的回调事件，不能排除存在 `value` 相同的选项，`onChange` 返回的永远是一个数组|(value: string &#124; number &#124; (string &#124; number)[], option: Option[]) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|

### Cascader 插槽 |插槽|说明|作用域|
| :- | :- |:- |
|prefix|选择器的前缀内容|-|
|suffix|选择器的后缀内容|-|
|default|自定义选择器选中内容（单选）|-|
|tag|自定义选择器标签（多选）|-|
|optionContent|自定义选择器选项的内容区域|-|
|loading|自定义选择器 `loading` 时下拉菜单展示的内容|-|
|empty|自定义选择器选项为空时下拉菜单展示的内容|-|
|top|选择器下拉菜单的顶部附加项，不随内容滚动|-|
|bottom|选择器下拉菜单的底部附加项，不随内容滚动|-|
|columnTop|选择器下拉菜单每列顶部附加项|`#columnTop="{ column, options, parentId }"`|

### Cascader TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|

### Option API 参考 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|id|选项的唯一键（下拉菜单中使用了 `label` + `value` 作为选项的默认唯一键，但当可能存在重复时可以额外声明 `id` 来保证内容发生变化时 `dom` 被正确更新）|string|-|
|name|选项的标题，优先使用 `label`|string|-|
|label|选项的标题|string|-|
|value|选项的值|string &#124; number|-|
|tooltip|选项的提示信息|string|-|
|extra|自定义需要通过 `option` 携带的数据，用法参考 `Cascader` 的 `自定义 Tag`|object|-|
|diabled|禁用状态|boolean|false|
|children|下级 下拉菜单中的选项|Option[\]|[]|

### Option 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|