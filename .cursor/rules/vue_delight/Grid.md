
import { Grid, GridI<PERSON>, Divider, Badge } from '@xhs/delight'

##    设置列

通过 `columns` 设置栅格布局列数：
 - 当 `columns` 为 `number` 时设置样式为 `repeat(${rows}, minmax(0, 1fr))` [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-rows)

```vue
<template>
    <Space direction="vertical" block align="unset">
      <Text bold>24 x 10 栅格：</Text>
      <Grid :columns="24">
        <template v-for="row of rows">
            <template v-for="col of cols">
              <div class="item --color-bg-primary-light"/>
              <div class="item --color-bg-fill"/>
            </template>
        </template>
      </Grid>
    <Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Grid, GridItem, Text } from '@xhs/delight'

  const rows = Array.from({ length: 10 }, (_, i) => i + 1)
  const cols = Array.from({ length: 12 }, (_, i) => i + 1)
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--size-space-step-default) * 10);
}
</style>
```

##    自定义列宽

通过 `columns` 自定义列宽：
 - 当 `columns` 为 `string` 则直接设置为对应样式 [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-rows)

```vue
<template>
    <Grid columns="repeat(10, 1fr) repeat(10, 2fr) repeat(2, minmax(var(--size-space-large), 0.5fr)) repeat(2, var(--size-space-small))">
        <template v-for="row of rows">
            <template v-for="col of cols">
              <div class="item --color-bg-primary-light"/>
              <div class="item --color-bg-fill"/>
            </template>
        </template>
    </Grid>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Grid, GridItem, Text } from '@xhs/delight'

  const rows = Array.from({ length: 10 }, (_, i) => i + 1)
  const cols = Array.from({ length: 12 }, (_, i) => i + 1)
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--size-space-step-default) * 10);
}
</style>
```

##    行间距

通过 `rowGap` 设置栅格布局行间距：

```vue
<template>
    <Grid :columns="24" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
          <template v-for="col of cols">
            <div class="item --color-bg-primary-light"/>
          </template>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Grid, GridItem } from '@xhs/delight'

  const rows = Array.from({ length: 10 }, (_, i) => i + 1)
  const cols = Array.from({ length: 24 }, (_, i) => i + 1)
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--size-space-step-default) * 10);
}
</style>
```

##    列间距

通过 `columnGap` 设置栅格布局列间距：

```vue
<template>
    <Grid :columns="24" column-gap="var(--size-space-default)">
      <template v-for="row of rows">
          <template v-for="col of cols">
            <div class="item --color-bg-primary-light"/>
          </template>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Grid, GridItem } from '@xhs/delight'

  const rows = Array.from({ length: 10 }, (_, i) => i + 1)
  const cols = Array.from({ length: 24 }, (_, i) => i + 1)
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--size-space-step-default) * 10);
}
</style>
```


##    跨行跨列

通过 `GridItem` 设置跨行跨列：
 - row：
  - `row` 为 `string` 则直接设置为对应样式 [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-row)
  - `row` 为 `number` 则设置为 `span ${row}`，使 `GridItem` 跨对应行数
 - column：
  - `column` 为 `string` 则直接设置为对应样式 [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-column)
  - `column` 为 `number` 则设置为 `span ${row}`，使 `GridItem` 跨对应列数

```vue
<template>
    <Grid :columns="4" column-gap="var(--size-space-default)" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
        <GridItem>
          <div class="item --color-bg-primary-light"/>
        </GridItem>
        <GridItem
          v-if="row !== 3"
          :row="row === 2 ? 2 : undefined"
          :column="row === 2 ? 2 : undefined"
        >
          <div class="item --color-bg-primary-light">
            <Text v-if="row === 2">
              跨 2 行 2 列
            </Text>
          </div>
        </GridItem>
        <GridItem v-if="row !== 2 && row !== 3">
          <div class="item --color-bg-primary-light"/>
        </GridItem>
        <GridItem>
          <div class="item --color-bg-primary-light"/>
        </GridItem>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Grid, GridItem, Text } from '@xhs/delight'

  const rows = Array.from({ length: 8 }, (_, i) => i + 1)
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(var(--size-space-step-default) * 10);
  height: 100%;
}
</style>
```

##    重叠列

通过 `GridItem` 设置跨行跨列：
 - 设置重叠列时需要显式的声明相关元素的行列位置

```vue
<template>
    <Grid :columns="4" column-gap="var(--size-space-default)" row-gap="var(--size-space-default)">
      <template v-for="row of rows">
        <GridItem
          :row="row === 2 ? '2 / 3' : undefined"
          :column="row === 2 ? '1 / 3' : undefined"
          :style="row === 2 && { zIndex: 1 }"
        >
          <div :class="['item', row === 2 ? '--color-bg-primary' : '--color-bg-primary-light']">
            <Text v-if="row === 2" color="white">
              跨 1 行 2 列
            </Text>
          </div>
        </GridItem>
        <GridItem
          v-if="row !== 3"
          :row="row === 2 ? '2 / 4' : undefined"
          :column="row === 2 ? '2 / 4' : undefined"
        >
          <div class="item lower --color-bg-primary-light">
            <Text v-if="row === 2">
              跨 2 行 2 列
            </Text>
          </div>
        </GridItem>
        <GridItem v-if="row !== 2 && row !== 3">
          <div class="item --color-bg-primary-light"/>
        </GridItem>
        <GridItem>
          <div class="item --color-bg-primary-light"/>
        </GridItem>
      </template>
    </Grid>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Grid, GridItem, Text } from '@xhs/delight'

  const rows = Array.from({ length: 8 }, (_, i) => i + 1)
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(var(--size-space-step-default) * 10);
  height: 100%;
}
.item.lower {
  padding-top: var(--size-space-large);
}
</style>
```

##    Grid API 参考

通过设置 Grid 的属性来产生不同的栅格布局样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|rows|设置栅格布局的行样式（grid-template-rows）如果为 `string` 则直接设置为对应样式 [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-rows)，如果为 `number` 则设置为 `repeat(${rows}, minmax(0, 1fr))`|string &#124; number|-|
|columns|设置栅格布局的列样式（grid-template-columns）如果为 `string` 则直接设置为对应样式 [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-columns)，如果为 `number` 则设置为 `repeat(${columns}, minmax(0, 1fr))`|string &#124; number|-|
|rowGap|设置行间距|string &#124; number|-|
|columnGap|设置列间距|string &#124; number|-|
|inline|设置为行内元素|boolean|-|

##    GridItem API 参考

通过设置 GridItem 的属性来产生不同的栅格布局元素样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|row|设置栅格布局元素的行样式（grid-row）如果为 `string` 则直接设置为对应样式 [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-row)，如果为 `number` 则设置为 `span ${row}`，使 `GridItem` 跨对应行数|string &#124; number|-|
|column|设置栅格布局元素的列样式（grid-column）如果为 `string` 则直接设置为对应样式 [参考](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-column)，如果为 `number` 则设置为 `span ${row}`，使 `GridItem` 跨对应列数|string &#124; number|-|