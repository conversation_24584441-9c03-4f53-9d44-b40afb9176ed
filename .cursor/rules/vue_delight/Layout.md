##    基本使用

通过 `slots.top`、`slots.right`、`slots.bottom`、`slots.left`、`slots.default` 来控制页面布局：

````vue
<template>
  <Layout :bottom-divider="false">
    <template #top>
      <Topbar>
        <template #title>
          <RedLogo />
        </template>
        <Menu mode="horizontal" :menu="horizontalMenu" />
      </Topbar>
    </template>
    <template #bottom>
      <div class="footer">
        <Text size="small" type="description">
          沪ICP备 13030189号 Copyright © 2014-2022 行吟信息科技（上海）有限公司
        </Text>
      </div>
    </template>
    <template #left>
      <Menu :style="{ height: '100%' }" :menu="menu" />
    </template>
    <Result :style="{ height: '100%' }" />
  </Layout>
</template>

<script setup lang="ts">
import { Layout, Topbar, Menu, Text, Result } from "@xhs/delight";
import {
  Home,
  Fire,
  Schedule,
  ApplicationTwo,
  Presets,
} from "@xhs/delight/icons";

const { RedLogo } = Presets;

const horizontalMenu = [
  {
    title: "基础组件",
    children: [
      {
        title: "Text 文字排版",
      },
      {
        title: "Icon 图标",
      },
      {
        title: "Button 按钮",
      },
      {
        title: "Layout 布局",
        to: { name: "delight-layout" },
      },
    ],
  },
  {
    title: "物料平台",
    disabled: true,
  },
  {
    title: "最佳实践",
    disabled: true,
  },
  {
    title: "更多资源",
    disabled: true,
  },
  {
    title: "关于我们",
    disabled: true,
  },
];
const menu = [
  {
    description: "开始",
  },
  {
    icon: Home,
    title: "Introduction 介绍",
  },
  {
    icon: Fire,
    title: "Get Started 快速开始",
  },
  {
    icon: Schedule,
    title: "Change Log 更新日志",
  },
  {
    description: "组件",
  },
  {
    icon: ApplicationTwo,
    title: "通用",
    children: [
      {
        title: "Text 文字排版",
      },
      {
        title: "Icon 图标",
      },
      {
        title: "Button 按钮",
      },
      {
        title: "Layout 布局",
        to: { name: "delight-layout" },
      },
    ],
  },
];
</script>

<style scoped>
.footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(
      (var(--size-space-step-default) + var(--size-space-step-small)) * 7
    ) 0;
}
</style>
```

##    顶部栏

通过 `slots.title`、`slots.default` 来控制顶部栏的内容：
```vue
<template>
  <Topbar>
    <template #title>
      <div class="title">
        <RedLogo />
        <Text class="name" type="h4" bold>平台名称</Text>
      </div>
    </template>
    <Menu mode="horizontal" :menu="horizontalMenu" />
  </Topbar>
</template>

<script setup lang="ts">
import { Topbar, Menu, Text, Result } from "@xhs/delight";
import { Presets } from "@xhs/delight/icons";

const { RedLogo } = Presets;

const horizontalMenu = [
  {
    title: "基础组件",
    children: [
      {
        title: "Text 文字排版",
      },
      {
        title: "Icon 图标",
      },
      {
        title: "Button 按钮",
      },
      {
        title: "Layout 布局",
        to: { name: "delight-layout" },
      },
    ],
  },
  {
    title: "物料平台",
    disabled: true,
  },
  {
    title: "最佳实践",
    disabled: true,
  },
  {
    title: "更多资源",
    disabled: true,
  },
  {
    title: "关于我们",
    disabled: true,
  },
];
</script>

<style scoped>
.title {
  display: flex;
  align-items: center;
}
.name {
  margin-left: var(--size-space-small);
}
</style>
```

##    API 参考

通过设置 Input 的属性来产生不同的单行输入框样式： |属性|说明|类型|默认值| | :- |
:- | :- | :- | |topDivider|是否展示顶部插槽分割线|boolean|true|
|rightDivider|是否展示右侧插槽分割线|boolean|true|
|bottomDivider|是否展示底部插槽分割线|boolean|true|
|leftDivider|是否展示左侧插槽分割线|boolean|true| ### Layout 插槽 |插槽|说明| | :- | :- | |top|顶部内容| |right|右侧内容| |bottom|底部内容| |left|左侧内容|
|default|主区域内容| ### Topbar 插槽 |插槽|说明| | :- | :- | |title|标题内容| |default|主区域内容|
````
