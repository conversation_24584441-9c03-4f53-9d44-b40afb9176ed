### 何时使用 当内容区域比较长，需要滚动页面时，这部分内容对应的操作或者导航需要在滚动范围内始终展现。常用于侧边菜单和按钮组合。

页面可视范围过小时，慎用此功能以免遮挡页面内容。

## 基本使用

```vue
<template>
  <Affix :target="target" :offset-top="60">
    <Button type="primary">Affix top</Button>
  </Affix>
</template>

<script setup>
import { Affix, Button } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const target = () => document.querySelector(".d-layout-main");
</script>
```

## 固定状态改变的回调

可以获得是否固定的状态

```vue
<template>
  <Affix :target="target" :offset-top="120" @change="change">
    <Button type="primary">120px to affix top</Button>
  </Affix>
</template>

<script setup>
import { Affix, Button } from "@xhs/delight";

// 此案例仅仅针对 Delight 文档生效，用户可根据自己的业务场景判断滚动的元素
const target = () => document.querySelector(".d-layout-main");

const change = (affixed) => {
  console.log(affixed);
};
</script>
```

## API 参考

| 属性         | 说明                                                                 | 类型              | 默认值       |
| :----------- | :------------------------------------------------------------------- | :---------------- | :----------- |
| offsetBottom | 距离窗口底部达到指定偏移量后触发                                     | number            | -            |
| offsetTop    | 距离窗口顶部达到指定偏移量后触发                                     | number            | 0            |
| target       | 设置 Affix 需要监听其滚动事件的元素，值为一个返回对应 DOM 元素的函数 | () => HTMLElement | () => window |

## 事件 | 事件 | 说明 | 类型 |

| :----- | :--------------------------- | :---------------------------- |
| change | 固定状态改变时触发的回调函数 | `(affixed?: boolean) => void` |
