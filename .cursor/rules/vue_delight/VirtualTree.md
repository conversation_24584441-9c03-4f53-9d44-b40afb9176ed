## 基本使用

虚拟列表需要一个高度

```js
import { ref } from "vue";
import { VirtualTree } from "@xhs/delight";

const expandedKeys = ref([]);
const selectedKeys = ref([]);

const getKey = (prefix, id) => prefix + "-" + id;

const createData = (
  maxDeep,
  maxChildren,
  minNodesNumber,
  deep = 1,
  key = "node"
) => {
  let id = 0;
  return Array.from({ length: minNodesNumber })
    .fill(deep)
    .map(() => {
      const childrenNumber = deep === maxDeep ? 0 : maxChildren;
      const nodeKey = getKey(key, ++id);
      return {
        key: nodeKey,
        title: nodeKey,
        children: childrenNumber
          ? createData(maxDeep, maxChildren, childrenNumber, deep + 1, nodeKey)
          : undefined,
      };
    });
};

const treeData = createData(3, 30, 40);
```

## 可选择

```js
{
  {
    treeNode.title;
  }
}

import { ref } from "vue";
import { VirtualTree } from "@xhs/delight";

const checkedKeys = ref([]);

const getKey = (prefix, id) => prefix + "-" + id;

const createData = (
  maxDeep,
  maxChildren,
  minNodesNumber,
  deep = 1,
  key = "node"
) => {
  let id = 0;
  return Array.from({ length: minNodesNumber })
    .fill(deep)
    .map((_, i) => {
      const childrenNumber = deep === maxDeep ? 0 : maxChildren;
      const nodeKey = getKey(key, ++id);
      return {
        key: nodeKey,
        title: i === 0 ? nodeKey + "1111111111111111111" : nodeKey,
        children: childrenNumber
          ? createData(maxDeep, maxChildren, childrenNumber, deep + 1, nodeKey)
          : undefined,
      };
    });
};

const treeData = createData(3, 30, 40);
```

## 可拖拽

```vue
<template>
  <VirtualTree
    v-model:expanded-keys="expandedKeys"
    v-model:selected-keys="selectedKeys"
    draggable
    expandOnClickNode
    style="height: 500px"
    :tree-data="treeData"
    :virtualize="false"
    @dragstart="dragstart"
    @dragenter="dragenter"
    @drop="drop"
  />
</template>

<script setup>
import { ref } from "vue";
import { VirtualTree } from "@xhs/delight";

const expandedKeys = ref([]);
const selectedKeys = ref([]);

const getKey = (prefix, id) => prefix + "-" + id;

const createData = (
  maxDeep,
  maxChildren,
  minNodesNumber,
  deep = 1,
  key = "node"
) => {
  let id = 0;
  return Array.from({ length: minNodesNumber })
    .fill(deep)
    .map(() => {
      const childrenNumber = deep === maxDeep ? 0 : maxChildren;
      const nodeKey = getKey(key, ++id);
      return {
        key: nodeKey,
        title: nodeKey,
        children: childrenNumber
          ? createData(maxDeep, maxChildren, childrenNumber, deep + 1, nodeKey)
          : undefined,
      };
    });
};

const treeData = ref(createData(3, 2, 5));

const dragstart = ({ node }) => {
  // 收缩当前拖拽的节点
  expandedKeys.value = expandedKeys.value.filter((key) => key !== node.key);
};

const dragenter = ({ event, node }) => {
  // 你可以在拖拽元素进入容器的时候，做一些你想做的事情，比如展开此节点
  const key = node.key;
  expandedKeys.value = [...new Set([...expandedKeys.value, key])];
};

// event 原生的 DragEvent  node 容器元素
// dragNode 拖拽元素   dropPosition 元素在容器的位置 -1 上 0 内部  1 下
const drop = ({ event, node: dropNode, dragNode, dropPosition }) => {
  const data = [...treeData.value];

  const loop = (data, key, callback) => {
    data.some((item, index, arr) => {
      if (item.key === key) {
        callback(item, index, arr);
        return true;
      }
      if (item.children) {
        return loop(item.children, key, callback);
      }
      return false;
    });
  };

  // 从源数据中删除 拖拽的节点
  loop(data, dragNode.key, (_, index, arr) => {
    arr.splice(index, 1);
  });

  // dropPosition = 0 ，将拖拽节点 push 到容器中
  if (dropPosition === 0) {
    loop(data, dropNode.key, (item) => {
      item.children = item.children || [];
      item.children.push(dragNode);
    });
  } else {
    // dropPosition -1 表示插入容器上面   1 表示插入容器下面
    loop(data, dropNode.key, (_, index, arr) => {
      arr.splice(dropPosition < 0 ? index : index + 1, 0, dragNode);
    });
  }

  treeData.value = data;
};
</script>
```

## 自定义插槽

```js
{
  {
    treeNode.title;
  }
}

import { ref } from "vue";
import { VirtualTree, Link, Icon } from "@xhs/delight";
import { BabyTaste } from "@xhs/delight/icons";

const getKey = (prefix, id) => prefix + "-" + id;

const createData = (
  maxDeep,
  maxChildren,
  minNodesNumber,
  deep = 1,
  key = "node"
) => {
  let id = 0;
  return Array.from({ length: minNodesNumber })
    .fill(deep)
    .map(() => {
      const childrenNumber = deep === maxDeep ? 0 : maxChildren;
      const nodeKey = getKey(key, ++id);
      return {
        key: nodeKey,
        title: nodeKey,
        children: childrenNumber
          ? createData(maxDeep, maxChildren, childrenNumber, deep + 1, nodeKey)
          : undefined,
      };
    });
};

const treeData = createData(4, 30, 40);
```

## API 参考

### Tree 属性 | 属性 | 说明 | 类型 | 默认值 |

| :-------------------- | :----------------------------------------------------------- | :------------------- | :----- |
| treeData | 树形组件可嵌套结构数据 | TreeNode[] | [] |
|expandOnClickNode|是否在点击节点的时候展开或者收缩节点，默认 false 的时候，只能点击箭头图标才能展开和收缩|boolean|false|
| checkable | 节点前添加 Checkbox 复选框 | boolean | false |
| selectable | 树节点是否可被选中 | boolean | true |
|revertable|单选的情况下，是否支持反选取消|boolean|true|
| checkStrictly | checkable 状态下节点选择完全受控（父子节点选中状态不再关联） | boolean | false |
| checkedKeys(v-model) | 选中复选框的树节点 | (string \| number)[] | [] |
| selectedKeys(v-model) | 设置选中的树节点 | (string \| number)[] | [] |
| expandedKeys(v-model) | 展开指定的树节点 | (string \| number)[] | [] |
| virtualize | 开启虚拟滚动 | boolean | true |
| virtualizeOptions | 虚拟滚动的配置 | `{ buffer: number }` | - |
|draggable|可拖拽|boolean|false|

### Tree 插槽 | 插槽 | 说明 |

| :---- | :--------------- |
| title | 自定义 Tree 标题 |
| icon | 自定义 icon |

### Events 事件 | Event 名称 | 描述 | 参数 |

| :--------- | :------------------ | :----------------------------------- |
| check | 勾选变化事件 | function(keys: string[] \| number[]) |
| select | 选中事件 | function(keys: string[] \| number[]) |
| expand | 展开/收起节点时触发 | function(keys: string[] \| number[]) |
|dragstart|拖拽开始|`function({ event, node })`|
|dragend|拖拽结束|`function({ event, node })`|
|dragenter|拖拽元素进入容器|`function({ event, node })`|
|dragover|拖拽元素经过容器|`function({ event, node })`|
|dragleave|拖拽元素离开容器|`function({ event, node })`|
|drop|拖拽元素掉落容器|`function({ event, node, dragNode, dropPosition })`|

### TreeNode 属性 | 属性 | 说明 | 类型 | 默认值 |

| :-------------- | :------------------- | :--------------- | :----- |
| title | 标题 | string | - |
| key | 必填，唯一标识树节点 | string \| number | - |
| children | 孩子节点 | TreeNode[] | - |
| selectable | 节点是否可被选中 | boolean | - |
| disabled | 禁掉响应 | boolean | - |
| disableCheckbox | 禁掉 checkbox | boolean | - |
