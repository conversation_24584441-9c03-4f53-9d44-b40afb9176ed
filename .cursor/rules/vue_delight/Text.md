

import { Icon, Text } from '@xhs/delight'

### 关于字体和字重   Delight 全局设置了文字所会使用的字体

  英文、数字使用 [Inter](https://fonts.google.com/specimen/Inter)

  中文使用系统默认字体
  - MacOS 为 PingFang SC
  - Windows 为 Microsoft YaHei

  目前文字排版支持两种字重
  - default 400
  - bold 500

##    类型

通过 `type` 设置文字排版的类型：

```vue
<template>
  <Text type="h1">h1</Text>
  <Text type="h2">h2</Text>
  <Text type="h3">h3</Text>
  <Text type="h4">h4</Text>
  <Text type="h5">h5</Text>
  <Text type="h6">h6</Text>
  <Text>paragraph</Text>
  <br/>
  <Text type="description">description</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    尺寸

除了 title 类型（h1、h2、h3、h4、h5、h6）自带了对应的大小以外，文字排版还支持通过 `size` 小号尺寸：

```vue
<template>
  <Text type="h1">h1</Text>
  <Text type="h2">h2</Text>
  <Text type="h3">h3</Text>
  <Text type="h4">h4</Text>
  <Text type="h5">h5</Text>
  <Text type="h6">h6</Text>
  <Text>paragraph</Text>
  <br/>
  <Text size="small">small</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    加粗

通过 `bold` 设置文字排版加粗：

```vue
<template>
  <Text type="h1" bold>h1</Text>
  <Text type="h2" bold>h2</Text>
  <Text type="h3" bold>h3</Text>
  <Text type="h4" bold>h4</Text>
  <Text type="h5" bold>h5</Text>
  <Text type="h6" bold>h6</Text>
  <Text bold>paragraph</Text>
  <br/>
  <Text size="small" bold>small</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    颜色

通过 `color` 设置文字排版的颜色：

```vue
<template>
  <Text color="primary">primary</Text>
  <br/>
  <Text color="warning">warning</Text>
  <br/>
  <Text color="danger">danger</Text>
  <br/>
  <Text color="success">success</Text>
  <br/>
  <Text color="info">info</Text>
  <br/>
  <Text color="text-title">text-title</Text>
  <br/>
  <Text color="text-paragraph">text-paragraph</Text>
  <br/>
  <Text color="text-description">text-description</Text>
  <br/>
  <Text color="text-placeholder">text-placeholder</Text>
  <br/>
  <Text color="text-disabled">text-disabled</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    下划线

通过 `underline` 给文字添加下划线，下滑线颜色默认与文字颜色一致：

```vue
<template>
  <Text type="h1" underline>h1</Text>
  <Text type="h2" underline='solid'>h2</Text>
  <Text type="h3" underline='dashed'>h3</Text>
  <Text type="h4" underline='dotted'>h4</Text>
  <Text type="h5" underline='double'>h5</Text>
  <Text type="h6" underline='wavy'>h6</Text>
  <Text underline>paragraph</Text>
  <br/>
  <Text underline color="primary">paragraph</Text>
  <br/>
  <Text size="small" underline>small</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    超出省略

通过 `ellipsis` 设置文字排版超出省略，并通过 `maxLines` 调整行数

此时如果你想设置 Tooltip 弹窗显示完整信息，可以增加 tooltip 属性即可

```vue
<template>
  <Text type="h6" bold>单行省略：</Text>
  <Text ellipsis tooltip :style="{ width: '200px' }">太武老师说他要来给大家举个例子</Text>
  <br/>
  <br/>
  <br/>
  <Text type="h6" bold>超过 2 行省略：</Text>
  <Text ellipsis tooltip :max-lines="2" :style="{ width: '200px' }">太武老师说可以通过多说一些废话的方式来让省略效果生效我试试看吧</Text>
  <br/>
  <br/>
  <Text type="h6" bold ellipsis :max-lines="4" :style="{ width: '200px' }">超过 4 行省略超过 4 行省略超过 4 行省略超过 4 行省略超过 4 行省略超过 4 行省略超过 4 行省略超过 4 行省略超过 4 行省略：</Text>
  <Text ellipsis tooltip :max-lines="4" :style="{ width: '200px' }">通过多说一些废话的方式让 4 行每行 200px 的文案省略生效对平时沉默寡言的我来说实在有些太勉强了不会真的有人要这么残忍的对待一个罹患社交恐惧症的孩子吧他可能这一生都没有一口气说过这么多话而且要是憋出什么毛病了不说能不能走医保去医院都不知道该挂什么科太愁人了要不算了吧太武老师</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    图标

通过设置 `icon` 设置带有图标的文字排版：

```vue
<template>
  <Space direction="vertical" align="start">
    <Space direction="vertical" align="start" :size="0">
      <Text type="h6" bold>尺寸：</Text>
      <Text :icon="BubbleChart" type="h5">h5</Text>
      <Text :icon="BubbleChart" type="h6">h6</Text>
      <Text :icon="BubbleChart">paragraph</Text>
      <Text :icon="BubbleChart" size="small">small</Text>
    </Space>
    <Space direction="vertical" align="start" :size="0">
      <Text type="h6" bold>位置：</Text>
      <Text :icon="{ icon: BubbleChart, theme: 'filled', color: 'red-6' }">left</Text>
      <Text :icon="BubbleChart" icon-position="right">right</Text>
    </Space>
    <Space direction="vertical" align="start" :size="0">
      <Text type="h6" bold>加载中：</Text>
      <Text loading icon-position="left">loading</Text>
      <Text loading>loading</Text>
    </Space>
    <Space direction="vertical" align="start" :size="0">
      <Text type="h6" bold>链接：</Text>
      <Text :icon="BubbleChart" link>带有图标的文字 link</Text>
      <Text :icon="BubbleChart" link muted>带有图标的文字 link</Text>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Text } from '@xhs/delight'
  import { BubbleChart } from '@xhs/delight/icons'

  function handleClick() {
    console.log('你点击了文字 link')
  }
</script>
```

##    等宽样式（仅对数字生效）

通过 `monospace` 设置文字排版为等宽样式：
```vue
<template>
  <Text type="h6" monospace>1,234,567,890</Text>
  <Text type="h6" bold monospace>1,234,567,890</Text>
  <br />
  <Text monospace>1,234,567,890</Text>
  <br />
  <Text bold monospace>1,234,567,890</Text>
  <br />
  <br />
  <Text size="small" monospace>1,234,567,890</Text>
  <br />
  <Text size="small" bold monospace>1,234,567,890</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    禁用

通过 `disabled` 设置文字排版为禁用样式：
```vue
<template>
  <Text type="h1" disabled>h1</Text>
  <Text type="h2" disabled>h2</Text>
  <Text type="h3" disabled>h3</Text>
  <Text type="h4" disabled>h4</Text>
  <Text type="h5" disabled>h5</Text>
  <Text type="h6" disabled>h6</Text>
  <Text disabled>paragraph</Text>
  <br />
  <Text size="small" disabled>small</Text>
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|type|文字排版的类型|'h1' &#124; 'h2' &#124; 'h3' &#124; 'h4' &#124; 'h5' &#124; 'h6' &#124; 'paragraph' &#124; 'description'|'paragraph'|
|size|文字排版的大小|'small'|跟随 type|
|bold|文字排版加粗|boolean|false|
|underline|文字下划线|boolean \| 'solid' \| 'dashed' \| 'double' \| 'dotted' \| 'wavy'|false
|color|文字排版的颜色|'primary' &#124; 'warning' &#124; 'danger' &#124; 'info' &#124; 'success' &#124; 'text-title' &#124; 'text-paragraph' &#124; 'text-description' &#124; 'text-placeholder' &#124; 'text-disabled'|跟随 type|
|icon|[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|((p: IconProps) => string)|-|
|iconPosition|图标位置|'left' &#124; 'right'|'left'|
|ellipsis|文字排版超出省略|boolean|false|
|maxLines|文字排版最大行数|number|-|
|link|文字排版转为链接|boolean &#124; [AnchorAttrs](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#attributes)|-|
|monospace|文字排版使用等宽样式（仅对数字生效）|boolean|false|
|disabled|文字排版禁用鼠标事件|boolean|false|
|block|独占一行|boolean|false|
|tooltip|支持弹窗显示（前提是文字出现省略号）|boolean \| string|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|文字排版的内容|

### TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|isEllipsisActive|文字排版是否触发省略|() => boolean|