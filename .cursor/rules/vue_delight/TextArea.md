

##    基本使用

```vue
<template>
  <TextArea v-model="value" />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { TextArea } from '@xhs/delight'

  const value = ref('')
</script>
```

##    多行输入框渐隐

通过 `fade` 设置渐隐风格：

渐隐风格在非交互状态时会隐去边框、背景和一些内部指示器如最大长度
 - 通过 showIndicators 强制展示内部指示器
 - 通过 blankHighlight 在没有输入内容时展示高亮

```vue
<template>
  <Space direction="vertical">
    <TextArea fade :max-length="10" />
    <TextArea :fade="{ showIndicators: true }" :max-length="10" />
    <TextArea :fade="{ blankHighlight: true }" :max-length="10" />
    <TextArea model-value="一段超过最大长度的文本" fade :max-length="10" validate-timing="immediate"/>
  </Space>
</template>

<script setup lang="ts">
  import { Space, TextArea } from '@xhs/delight'
</script>
```

##    行数

通过 `rows` 设置多行输入框行数：

```vue
<template>
  <TextArea :rows="7"/>
</template>

<script setup lang="ts">
  import { TextArea } from '@xhs/delight'
</script>
```

##    placeholder

通过 `placeholder` 设置提示文本：

```vue
<template>
  <TextArea placeholder="提示文本"/>
</template>

<script setup lang="ts">
  import { TextArea } from '@xhs/delight'
</script>
```

##    前缀

通过 `prefix` 或 `slots.prefix` 设置前缀：

```vue
<template>
  <TextArea prefix="标题"/>
  <div style="height: var(--size-space-default)"/>
  <TextArea>
    <template #prefix>
      <Icon :icon="Search"/>
    </template>
  </TextArea>
</template>

<script setup lang="ts">
  import { TextArea, Icon } from '@xhs/delight'
  import { Search } from '@xhs/delight/icons'
</script>
```

##    清除输入

通过 `clearable` 开启清除输入功能：

```vue
<template>
  <TextArea v-model="value" clearable />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { TextArea } from '@xhs/delight'

  const value = ref('点击清除按钮清除内容')
</script>
```

##    必填

通过 `required` 设置为必填项：

```vue
<template>
  <TextArea v-model="value" required />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { TextArea } from '@xhs/delight'

  const value = ref()
</script>
```

##    限制输入长度

通过 `maxLength` 限制输入长度：

```vue
<template>
  <TextArea v-model="value" :max-length="5"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { TextArea } from '@xhs/delight'

  const value = ref('超出长度了哦')
</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则：

```vue
<template>
  <TextArea v-model="value" :validate="validate"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { TextArea } from '@xhs/delight'

  const value = ref('需要输入整数字符串')

  function validate({ modelValue }) {
    return modelValue !== '' && Number.isInteger(Number(modelValue))
  }
</script>
```

##    立即校验（包括必填、限制输入长度、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验：

```vue
<template>
  <TextArea v-model="value" :validate="validate" validate-timing="immediate"/>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { TextArea } from '@xhs/delight'

  const value = ref('需要输入整数字符串')

  function validate({ modelValue }) {
    return modelValue !== '' && Number.isInteger(Number(modelValue))
  }
</script>
```

##    手动校验（包括必填、限制输入长度、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验：

```vue
<template>
  <Space direction="vertical" align="start">
    <TextArea ref="textarea" v-model="value" required validate-timing="manual"/>
    <Button type="primary" @click="manualValidate">result: {{result}}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, TextArea, Button } from '@xhs/delight'

  const textarea = ref()
  const value = ref()
  const result = ref()

  function manualValidate() {
    textarea.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }
</script>
```

##    自动扩展

通过 `autosize` 设置自动扩展：

```vue
<template>
  <TextArea v-model="value" :rows="1" autosize />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { TextArea } from '@xhs/delight'

  const value = ref('当前设置了 textarea 的 rows 为 1，正常情况下超出 1 行就会在 1 行空间内滚动。但是如果设置了 autosize，textarea 会被扩展到足够的高度来展示所有内容。')
</script>
```

##    自动完成

通过 `autoComplete` 提示自动完成，通过 `options` 配置选项，通过 `loading` 配置加载中状态，通过 `slots.loading` 自定义加载状态展示内容：

```vue
<template>
  <Space direction="vertical" style="width: 100%">
    <TextArea v-model="value" auto-complete :options="options" style="width: 100%"/>
    <TextArea v-model="value" auto-complete :options="options" :loading="loading" style="width: 100%"/>
    <TextArea v-model="value" auto-complete :options="options" :loading="loading" style="width: 100%">
      <template #loading>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing" title="加载中"/>
        </div>
      </template>
    </TextArea>
  <Space>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { Space, TextArea, Result, useDebounce } from '@xhs/delight'

  const value = ref('')
  const options = ref([])
  const loading = ref(false)

  const remoteSearch = useDebounce(
    v => {
      options.value = v?.trim()
      ? [
        v + '@xiaohongshu.com',
        v + '@xiaohongshu.net',
      ]
      : []

      loading.value = false
    },
    { delay: 1000 }
  )

  watch(
    value,
    v => {
      loading.value = true
      remoteSearch(v)
    }
  )
</script>
```

##    块级元素

通过 `block` 设置块级元素：

```vue
<template>
  <TextArea placeholder="提示文本 block/>
</template>

<script setup lang="ts">
  import { TextArea } from '@xhs/delight'
</script>
```

##    禁用

通过 `disabled` 设置禁用：

```vue
<template>
  <TextArea placeholder="提示文本"  disabled/>
</template>

<script setup lang="ts">
  import { TextArea } from '@xhs/delight'
</script>
```

##    只读

通过 `readonly` 设置只读：

```vue
<template>
  <TextArea modelValue="我是只读的" placeholder="提示文本" readonly clearable/>
</template>

<script setup lang="ts">
  import { TextArea } from '@xhs/delight'
</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素：

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 TextArea 的静态描述文本"
      on-error="这是一段 TextArea 的静态错误提示"
    >
      <TextArea modelValue="一段文字" :max-length="10" placeholder="提示文本" clearable/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :on-error="onError"
    >
      <TextArea model-value="一段文字" required :max-length="10" placeholder="提示文本" clearable/>
    </FormItem>
    <FormItem name="3">
      <TextArea model-value="一段文字" required :max-length="10" placeholder="提示文本" clearable/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue }">当前输入长度为：{{ modelValue?.length || 0 }}</template>
      <template v-slot:on-error="{ modelValue }">{{
        modelValue?.length
          ? '当前输入长度为：' + modelValue.length + '，最大输入长度为 10'
          :'必填项'
      }}</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { TextArea, Form, FormItem } from '@xhs/delight'

  function description({ modelValue }) {
    return '当前输入长度为：' + (modelValue?.length || 0)
  }

  function onError({ modelValue }) {
    return modelValue?.length
      ? '当前输入长度为：' + modelValue.length + '，最大输入长度为 10'
      :'必填项'
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|多行输入框的内容|string|-|
|rows|多行输入框的行数|number|4|
|placeholder|多行输入框的提示文本|string|-|
|prefix|多行输入框的前缀内容|string|-|
|suffix|多行输入框的后缀内容|string|-|
|monospace|多行输入框使用等宽样式（仅对数字生效）|boolean|false|
|clearable|开启清除输入内容按钮|boolean|false|
|required|必填项|boolean|false|
|requiredError|必填报错（Form 中展示）|string|-|
|maxLength|限制输入内容的长度|number|-|
|requiredError|超出长度报错（Form 中展示|string|-|
|validate|自定义校验规则|(args: &#123; modelValue?: string &#125;) => string &#124; boolean &#124; Promise&lt;string &#124; boolean&gt;|-|
|validateDelay|校验延迟（ms）|number|100|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|'immediate' &#124; 'blur' &#124; 'manual'|'blur'|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|boolean|false|
|autosize|自动扩展|boolean|false|
|autofocus|自动获取焦点|boolean|false|
|autoComplete|多行输入框是否提示自动完成|boolean|false|
|options|多行输入框自动完成的提示内容|string[]|-|
|maxDropdownWidth|多行输入框中下拉菜单最大宽度，过长的内容会被省略|number|-|
|maxDropdownHeight|多行输入框中下拉菜单最大高度，超过会滚动|number|-|
|loading|多行输入框中下拉菜单展示 `loading` 状态|boolean|false|
|dropdownClass|多行输入框中下拉菜单 class，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|dropdownStyle|多行输入框中下拉菜单 style，由于下拉菜单是通过 `Teleport` 挂载到 `body` 上，所以定义样式时不能为 `scoped`|string &#124; array &#124; object|-|
|block|展示为块级元素|boolean|false|
|disabled|禁用|boolean|false|
|readonly|原生`readonly`属性，是否只读|boolean|false|
|fade|渐隐风格|boolean &#124; &#123; showIndicators?: boolean; blankHighlight?: boolean &#125;|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|多行输入框中值变化的回调事件|(e: string) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
|input|输入的回调事件|(e: Event) => void|-|
|focus|获得焦点的回调事件|(e: FocusEvent) => void|-|
|blur|失去焦点的回调事件|(e: FocusEvent) => void|-|
|clear|清除输入内容的回调事件|(e: MouseEvent) => void|-|
|enter|键盘回车的回调事件|(e: KeyboardEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|prefix|多行输入框的前缀内容|
|suffix|多行输入框的后缀内容|
|loading|多行输入框中下拉菜单展示 `loading` 状态时的内容|

### Ref API |内容|说明|类型|
| :- | :- | :- |
|blur|手动矢焦|() => void|
|focus|手动聚焦|() => void|
|validate|手动校验|() => Promise&lt;string &#124; boolean&gt;|
|reset|清空内容和状态|() => void|
|status|校验状态|'default' &#124; 'waiting' &#124; 'error'|
|validateError|校验报错|string|