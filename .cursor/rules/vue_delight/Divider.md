

##    样式

通过 `direction` 设置分割线样式：

```vue
<template>
  <div class="layout">
    <div style="display: flex; flex: 1;">
        <div class="content"/>
        <Divider direction="vertical" />
        <div class="content"/>
    </div>
    <Divider />
    <div style="display: flex; flex: 1;">
        <div class="content"/>
        <Divider direction="vertical" />
        <div class="content"/>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Divider, Text } from '@xhs/delight'
</script>

<style scoped>
.layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.content {
  flex: 1;
}
</style>
```

##    虚线

通过 `dashed` 设置分割线为虚线：

```vue
<template>
  <div class="layout-dashed">
    <div style="display: flex; flex: 1;">
        <div class="content-dashed"/>
        <Divider direction="vertical" dashed/>
        <div class="content-dashed"/>
    </div>
    <Divider dashed/>
    <div style="display: flex; flex: 1;">
        <div class="content-dashed"/>
        <Divider direction="vertical" dashed/>
        <div class="content-dashed"/>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Divider, Text } from '@xhs/delight'
</script>

<style scoped>
.layout-dashed {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.content-dashed {
  flex: 1;
}
</style>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|direction|分割线样式|'vertical' &#124; 'horizontal'|'horizontal'|
|dashed|虚线分割线|boolean|false|