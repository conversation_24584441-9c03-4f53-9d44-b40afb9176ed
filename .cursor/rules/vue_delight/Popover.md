

❗️❗️❗️ 由于通过 `Teleport` 挂载，浮层样式 不能为 `scoped

##    基本使用

通过传入 `target` 或包裹 `slots.default`，将 `slots.content` 中的内容通过浮层的方式在 `slots.default` 周围展示：

```vue
<template>
  <Space>
    <Popover>
      <Button>hover</Button>
      <template #content>
        <div style="padding: var(--size-space-default)">
          <Text>bottom</Text>
        </div>
      </template>
    </Popover>

    <Button ref="button">hover</Button>
    <Popover :target="button?.$el">
      <template #content>
        <div style="padding: var(--size-space-default)">
          <Text>bottom</Text>
        </div>
      </template>
    </Popover>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Popover, Button, Text } from '@xhs/delight'

  const button = ref()
</script>
```

##    位置

通过 `placement` 设置浮层展示的相对位置：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Popover :placement="placement">
        <Button block>{{ placement }}</Button>
        <template #content>
          <div style="padding: var(--size-space-large)">
            <Text>{{ placement }}</Text>
          </div>
        </template>
      </Popover>
    </div>
  </Grid>
</template>

<script setup lang="ts">
  import { Grid, Popover, Button, Text } from '@xhs/delight'

  const placements = ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end']
</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    间距

通过 `offset` 设置浮层与 `slots.default` 的间距：

当 offset 属性接受一个 number 类型时，表示浮层在主轴方向的偏移量，等同于 `offset={ mainAxis: number }`

当你想改变交叉轴方向上的偏移量的时候，可以设置 `offset={ crossAxis: number }`

```vue
<template>
  <Popover :offset="12">
    <Button>mainAxis</Button>
    <template #content>
      <div style="padding: var(--size-space-large)">
        <Text>mainAxis</Text>
      </div>
    </template>
  </Popover>

  <span style="margin: 0 20px;"></span>

  <Popover :offset="{ crossAxis: 20 }">
    <Button>crossAxis</Button>
    <template #content>
      <div style="padding: var(--size-space-large)">
        <Text>crossAxis</Text>
      </div>
    </template>
  </Popover>
</template>

<script setup>
  import { Popover, Button, Text } from '@xhs/delight'

</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    箭头

通过 `arrow` 设置浮层指向 `slots.default` 的箭头：

```vue
<template>
  <Grid class="popover-grid">
    <div v-for="placement of placements" :style="{ gridArea: placement }">
      <Popover :arrow="true" :placement="placement">
        <Button block>{{ placement }}</Button>
        <template #content>
          <div style="padding: var(--size-space-large)">
            <Text>{{ placement }}</Text>
          </div>
        </template>
      </Popover>
    </div>
  </Grid>
</template>

<script setup lang="ts">
  import { Grid, Popover, Button, Text } from '@xhs/delight'

  const placements = ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end']
</script>

<style scoped>
.popover-grid {
  grid-template: 
            ".          top-start    top    top-end    .          " min-content
            "left-start .            .      .          right-start" min-content
            "left       .            .      .          right      " min-content
            "left-end   .            .      .          right-end  " min-content
            ".          bottom-start bottom bottom-end .          " max-content / 1fr 1fr 1fr 1fr 1fr;
  row-gap: var(--size-space-small);
  column-gap: var(--size-space-small);
}
</style>
```

##    触发方式

通过 `trigger` 设置展示浮层的触发方式：

```vue
<template>
  <Popover>
    <Button>hover</Button>
    <template #content>
      <div style="padding: var(--size-space-default)">
        <Text>bottom</Text>
      </div>
    </template>
  </Popover>

  <div style="height: var(--size-space-large)"/>

  <Popover trigger="click">
    <Button>click</Button>
    <template #content>
      <div style="padding: var(--size-space-default)">
        <Text>bottom</Text>
      </div>
    </template>
  </Popover>

  <div style="height: var(--size-space-large)"/>
  
  <Popover trigger="manual" :visible="manualVisible">
    <Button>manual</Button>
    <template #content>
      <div style="padding: var(--size-space-default)">
        <Button type="danger" @click="manualHide">click here to hide</Button>
      </div>
    </template>
  </Popover>

  <div style="height: var(--size-space-default)"/>

  <Button type="primary" @click="manualShow">click here to show</Button>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Popover, Button, Text } from '@xhs/delight'

  const manualVisible = ref(false)
  
  function manualShow() {
    manualVisible.value = true
  }
  
  function manualHide() {
    manualVisible.value = false
  }
</script>
```

##    预插入

通过 `prepend` 设置浮层预先插入，以第一时间触发对外部的影响，
比如下拉菜单中，父节点的选中状态依赖子节点逐级向上通知，那么就需要预先插入所有的子节点：

```vue
<template>
  <Popover>
    <Button>prepend</Button>
    <template #content>
      <div>
        <Popover placement="right">
          <div style="padding: var(--size-space-default)">
            <Text>root</Text>
          </div>
          <template #content>
            <div>
              <Popover prepend placement="right">
                <div style="padding: var(--size-space-default)">
                  <Text
                    :color="parentTitle === parentTitles.exist
                      ? 'success'
                      : 'danger'"
                  >
                    {{ parentTitle }}
                  </Text>
                </div>
                <template #content>
                  <div style="padding: var(--size-space-default)">
                    <component :is="PrependChild"/>
                  </div>
                </template>
              </Popover>
            </div>
          </template>
        </Popover>
      </div>
    </template>
  </Popover>

  <div style="height: var(--size-space-default)"/>
  
  <Popover>
    <Button>not prepend</Button>
    <template #content>
      <div>
        <Popover placement="right">
          <div style="padding: var(--size-space-default)">
            <Text>root</Text>
          </div>
          <template #content>
            <div>
              <Popover placement="right">
                <div style="padding: var(--size-space-default)">
                  <Text
                    :color="parentTitle === parentTitles.exist
                      ? 'success'
                      : 'danger'"
                  >
                    {{ parentTitle }}
                  </Text>
                </div>
                <template #content>
                  <div style="padding: var(--size-space-default)">
                    <component :is="PrependChild"/>
                  </div>
                </template>
              </Popover>
            </div>
          </template>
        </Popover>
      </div>
    </template>
  </Popover>
</template>

<script setup lang="ts">
  import { ref, defineComponent, onUnmounted, h } from 'vue'
  import { Popover, Button, Text } from '@xhs/delight'

  const parentTitles = {
    exist: 'leaf child exists!',
    notExist: 'leaf child does not exist.'
  }

  const parentTitle = ref(parentTitles.notExist)

  const PrependChild = defineComponent({
    setup() {

      parentTitle.value = parentTitles.exist

      onUnmounted(
        () => {
          parentTitle.value = parentTitles.notExist
        }
      )
    },
    render() {
      return h(
        Text,
        null,
        () => 'prepend child'
      )
    }
  })
</script>
```

##    自定义浮层

`slots.content` 带有默认的边框和阴影，通过 `slots.popover` 可以完全自定义浮层的样式：

```vue
<template>
  <Popover>
    <Button>hover</Button>
    <template #popover>
      <div class="custom-popover">
        <Icon :icon="Fire" size="extra-large"/>
      </div>
    </template>
  </Popover>
</template>

<script setup lang="ts">
  import { Popover, Button, Icon } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'
</script>

<style scoped>
.custom-popover {
  padding: var(--size-space-default);
  border: var(--border-default);
  border-radius: 100vmax;
  line-height: 1;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1), 0px 4px 15px rgba(0, 0, 0, 0.1);
}
</style>
```

##    API 参考

```
type OffsetValue = number | {
  mainAxis?: number;
  crossAxis?: number;
}
```

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|target|浮层的目标元素|HTMLElement|-|
|placement|浮层展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'bottom'|
|offset|浮层与目标元素的间距|OffsetValue|4|
|arrow|浮层展示指向目标元素的箭头|boolean|false|
|trigger|浮层展示的触发方式|'hover' &#124; 'click' &#124; 'manual'|'hover'|
|visible|手动控制浮层是否展示，仅当 `trigger` 为 `manual` 时生效|boolean|false|
|validate|判断浮层是否需要展示，当需要判断条件再展示浮层时使用。比如 `Tag` 只有出现文字被省略时再展示 `Tooltip`|() => boolean|-|
|prepend|浮层是否预插入|boolean|false|
|to|指定浮层的 DOM 元素插入的位置节点，默认插入 body 元素尾部|string &#124; HTMLElement|'body'|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|浮层展示状态变化的回调事件|(e: boolean) => void|-|
|click|鼠标单击的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素、浮层及所有子孙浮层）|(e: MouseEvent) => void|-|
|click:target|鼠标单击的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素）|(e: MouseEvent) => void|-|
|click:popover|鼠标单击的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于浮层及所有子孙浮层）|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素、浮层及所有子孙浮层）|(e: MouseEvent) => void|-|
|mousedown:target|鼠标按下的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素）|(e: MouseEvent) => void|-|
|mousedown:popover|鼠标按下的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于浮层及所有子孙浮层）|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素、浮层及所有子孙浮层）|(e: MouseEvent) => void|-|
|mouseup:target|鼠标抬起的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于目标元素）|(e: MouseEvent) => void|-|
|mouseup:popover|鼠标抬起的回调事件（仅当 `trigger` 为 `click` 、 `manual` 时生效于浮层及所有子孙浮层）|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|浮层的目标元素|
|content|浮层的内容，带有默认边框和阴影|
|popover|完全自定义的浮层的内容|