

##    基本使用

通过 `visible` 设置对话框展示，通过 `title` 设置对话框标题，通过 `slots.default` 设置对话框内容：
- `Modal` 默认有 `取消` 、 `确认` 两个操作，通过 `onCancel` 、 `onConfirm` 接收点击事件

```vue
<template>
  <Button type="primary" @click="handleOpen">Modal</Button>
  <Modal
    v-model:visible="visible"
    title="逍遥游"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { But<PERSON>, <PERSON><PERSON>, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    自定义标题

通过 `slots.title` 设置自定义标题：

```vue
<template>
  <Button type="primary" @click="handleOpen">Modal</Button>
  <Modal
    v-model:visible="visible"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <template #title>
      <Text
        :icon="{ icon: Fire, theme: 'filled', color: 'red-6' }"
        type="h5"
        bold
      >
        逍遥游
      </Text>
    </template>
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Modal, Text } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    提示类型

通过 `type` 设置提示类型：
- `type` 支持 `info` 、 `success` 、 `warning` 、 `danger` 共 `4` 个类型，默认 `undefined`

```vue
<template>
  <Space>
    <Button type="primary" @click="() => handleOpen()">default</Button>
    <Button type="primary" @click="() => handleOpen('info')">info</Button>
    <Button type="primary" @click="() => handleOpen('success')">success</Button>
    <Button type="primary" @click="() => handleOpen('warning')">warning</Button>
    <Button type="primary" @click="() => handleOpen('danger')">danger</Button>
  </Space>
  <Modal
    v-model:visible="visible"
    :type="type"
    title="逍遥游"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Modal, Text } from '@xhs/delight'

  const type = ref()
  const visible = ref(false)

  function handleOpen(t) {
    type.value = t
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    尺寸

通过 `size` 设置尺寸，支持 `default` 、 `large` 两种尺寸，也可以自定义：

```vue
<template>
  <Space>
    <Button type="primary" @click="() => handleOpen()">default</Button>
    <Button type="primary" @click="() => handleOpen('large')">large</Button>
    <Button type="primary" @click="() => handleOpen(300)">300px</Button>
    <Button type="primary" @click="() => handleOpen('calc(100vw - 180px)')">calc(100vw - 180px)</Button>
  </Space>
  <Modal
    v-model:visible="visible"
    :size="size"
    title="逍遥游"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Modal, Text } from '@xhs/delight'

  const size = ref()
  const visible = ref(false)

  function handleOpen(s) {
    size.value = s
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    默认可关闭

通过 `closeable` 设置默认可关闭，默认为 `true`：

```vue
<template>
  <Button type="primary" @click="handleOpen">Modal</Button>
  <Modal
    v-model:visible="visible"
    title="逍遥游"
    :closeable="false"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Modal, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    居中展示

通过 `centered` 设置居中展示：

```vue
<template>
  <Space>
    <Button type="primary" @click="() => handleOpen()">centered</Button>
  </Space>
  <Modal
    v-model:visible="visible"
    centered
    title="逍遥游"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Modal, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    全屏展示

通过 `fullScreen` 设置全屏展示：

```vue
<template>
  <Space>
    <Button type="primary" @click="() => handleOpen()">fullScreen</Button>
  </Space>
  <Modal
    v-model:visible="visible"
    full-screen
    title="逍遥游"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Modal, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    点击外部关闭

通过 `outsideCloseable` 设置点击外部关闭

```vue
<template>
  <Button type="primary" @click="handleOpen">outsideCloseable</Button>
  <Modal
    v-model:visible="visible"
    outside-closeable
    title="逍遥游"
    :mask="false"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Modal, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    遮罩层

通过 `mask` 设置遮罩层 默认为 true
- 通过 `maskCloseable` 设置点击遮罩层关闭

```vue
<template>
  <Space>
    <Button type="primary" @click="() => handleOpen(false)">mask</Button>
    <Button type="primary" @click="() => handleOpen(true)">maskCloseable</Button>
  </Space>
  <Modal
    v-model:visible="visible"
    :mask-closeable="maskCloseable"
    title="逍遥游"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Modal, Text } from '@xhs/delight'

  const visible = ref(false)
  const maskCloseable = ref(false)

  function handleOpen(v) {
    maskCloseable.value = v
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    自定义对话框操作栏

非真实场景，仅做能力演示 

- 通过 `with-confirm` 设置是否有确认按钮，默认为 `true`
- 通过 `confirm-text` 设置确认按钮的文案
- 通过 `confirm-type` 设置确认按钮的类型
- 通过 `confirm-button-props` 设置确认按钮的属性
- 通过 `with-cancel` 设置是否有取消按钮，默认为 `true`
- 通过 `cancel-text` 设置取消按钮的文案
- 通过 `cancel-type` 设置取消按钮的类型
- 通过 `cancel-button-props` 设置取消按钮的属性
- 通过 `with-footer` 设置是否有操作栏，默认为 `true`
- 通过 `slots.footer` 设置自定义操作栏

```vue
<template>
  <Space>
    <Button type="primary" @click="handleOpenDefault">default</Button>
    <Button type="primary" @click="handleOpenNoFooter">no footer</Button>
    <Button type="primary" @click="handleOpenCustom">custom</Button>
  </Space>
  <Modal
    v-model:visible="visible1"
    title="逍遥游"
    confirm-text="销毁"
    confirm-type="danger"
    :confirm-button-props="{ icon: Clear, loading }"
    :with-cancel="false"
    @confirm="handleCloseDefault"
  >
    <Spinner :spinning="loading" tip="正在销毁数据" size="large">
      <Text>
        北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
      </Text>
    </Spinner>
  </Modal>
  <Modal
    v-model:visible="visible2"
    title="逍遥游"
    :with-footer="false"
    @confirm="handleCloseNoFooter"
    @cancel="handleCloseNoFooter"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
  <Modal
    v-model:visible="visible3"
    :closeable="false"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
    <template #footer>
      <Button type="danger" size="small" @click="handleCloseCustom">
        关闭
      </Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Modal, Text, Spinner } from '@xhs/delight'
  import { Clear } from '@xhs/delight/icons'

  const visible1 = ref(false)
  const visible2 = ref(false)
  const visible3 = ref(false)

  const loading = ref(false)

  function handleOpenDefault() {
    visible1.value = true
  }

  function handleOpenNoFooter() {
    visible2.value = true
  }

  function handleOpenCustom() {
    visible3.value = true
  }

  function handleCloseDefault() {
    loading.value = true

    setTimeout(
      () => {
        loading.value = false
        visible1.value = false
      },
      3000
    )
  }

  function handleCloseNoFooter() {
    visible2.value = false
  }

  function handleCloseCustom() {
    visible3.value = false
  }
</script>
```

##    自定义弹窗的类名和样式

- class 用于自定义弹窗主体内容的类名
- style 用于自定义弹窗主体内容的样式
- zIndex 用于自定义 Modal 的层级
- maskStyle 用于自定义遮罩层样式

```vue
<template>
  <Button type="primary" @click="handleOpen">Modal</Button>
  <Modal
    v-model:visible="visible"
    title="逍遥游"
    class="my-custom-content-class"
    :mask-style="{
      background: 'yellow'
    }"
    style="background: pink"
    :z-index="999"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Text>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Modal, Text } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }

  function handleClose() {
    visible.value = false
  }
</script>
```

##    函数式调用

目前提供了五种快捷调用

 - Modal.info
 - Modal.success
 - Modal.warning
 - Modal.danger
 - Modal.confirm

```vue
<template>
  <Button type="primary" @click="handleOpen">Modal</Button>
</template>

<script setup lang="ts">
  import { ref, h } from 'vue'
  import { Button, Modal, Text } from '@xhs/delight'

  function handleOpen() {
    Modal.success({
      title: '逍遥游',
      content: h(Text, {}, {
        default: () => '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”'
      }),
      // onCancel onConfirm 函数默认接受的一个参数是 close ，用于关闭弹窗，不写此方法，默认关闭
      onConfirm(close) {
        console.log('confirm')
        close?.()
      },
      onCancel(close) {
        console.log('cancel')
        close?.()
      }
    })
  }

</script>
```

##    函数式调用 - 手动销毁

使用 `Modal.destroyAll()` 可以销毁弹出的确认窗。通常用于路由监听当中，处理路由前进、后退不能销毁确认对话框的问题。

```vue
<template>
  <Button type="primary" @click="handleOpen">Modal</Button>
</template>

<script setup lang="ts">
  import { ref, h } from 'vue'
  import { Button, Modal, Text } from '@xhs/delight'

  function handleOpen() {
    const modal = Modal.success({
      title: '逍遥游',
      content: h(Text, {}, {
        default: () => '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”'
      }),
      onCancel: () => {
        // Modal.destroyAll()

        // 销毁当前的 Modal
        modal.destroy()
      }
    })
  }

</script>
```

##    函数式调用 - 更新数据

```vue
<template>
  <Space>
    <Button type="primary" @click="handleOpen">Modal</Button>
    <Button type="primary" @click="handleChange">change</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref, h } from 'vue'
  import { Button, Modal, Text, Space } from '@xhs/delight'

  let modal;

  function handleOpen() {
    modal = Modal.success({
      title: '逍遥游',
      mask: false,
      content: h(Text, {}, {
        default: () => '北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”'
      }),
    })
  }

  function handleChange() {
    modal.update({
      mask: true
    })
  }

</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|type|对话框类型|'info' &#124; 'success' &#124; 'warning' &#124; 'danger'|-|
|title|对话框标题|string|-|
|size|对话框尺寸|'default' &#124; 'large' &#124; string &#124; number|'default'|
|closeable|对话框默认可关闭|boolean|true|
|centered|对话框是否居中展示|boolean|false|
|fullScreen|对话框是否全屏展示|boolean|false|
|withFooter|对话框是否带操作栏|boolean|true|
|visible|对话框是否展示|boolean|false|
|outsideCloseable|对话框点击外部可关闭|boolean|false|
|mask|对话框带遮罩层|boolean|true|
|maskCloseable|对话框点击遮罩层可关闭|boolean|false|
|withConfirm|对话框操作栏带默认确认按钮|boolean|true|
|confirmText|对话框操作栏默认确认按钮文案|string|-|
|confirmType|对话框操作栏默认确认按钮类型|'default' &#124; 'primary' &#124; 'secondary' &#124; 'danger' &#124; 'light'|'primary'|
|confirmButtonProps|对话框操作栏默认确认按钮属性|ButtonProps|-|
|withCancel|对话框操作栏带默认取消按钮|boolean|true|
|cancelText|对话框操作栏默认取消按钮文案|string|-|
|cancelType|对话框操作栏默认取消按钮类型|'default' &#124; 'primary' &#124; 'secondary' &#124; 'danger' &#124; 'light'|'primary'|
|cancelButtonProps|对话框操作栏默认取消按钮属性|ButtonProps|-|
|maskStyle|遮罩样式|CSSProperties|-|
|style|弹窗主体内容|CSSProperties|-|
|class|弹窗主体内容的 class 类名|string|-|
|zIndex|设置 Modal 的 `z-index`|number|99|
|loading|加载中（仅仅出现在确认按钮上面）|boolean|false|
|destroyOnClose|`Modal` 关闭的时候，是否要销毁组件|boolean|true|
|noPadding|是否设置左右padding|boolean|false|
|ssr|当你发现默认插槽可能出现挂载多次的问题，请设置 ssr=false|boolean|true|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:visible|对话框展示状态变化的回调事件|(v: boolean) => void|-|
|confirm|默认确认按钮点击的回调事件|(e: MouseEvent) => void|-|
|cancel|默认取消按钮点击的回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|对话框的内容|
|title|对话框的标题|
|footer|对话框的页脚操作栏|

### Modal.method() 包括：

  - Modal.info
  - Modal.success
  - Modal.danger
  - Modal.warning
  - Modal.confirm

以上均为一个函数，参数为 object，具体属性全面兼容 Modal 组件属性，事件兼容 onCancel 和 onConfirm 。

以上函数调用后，会返回一个引用，可以通过该引用更新和关闭弹窗。

```
  const modal = Modal.info();

  modal.update({
    title: '修改的标题',
    content: '修改的内容',
  });

  modal.destroy();
```

