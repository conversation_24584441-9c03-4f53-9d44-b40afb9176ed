

##    基本使用

通过 `visible` 设置通知横幅展示，通过 `title` 设置通知横幅标题， `description` 设置通知横幅内容：

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Button type="primary" block @click="handleOpen">Banner</Button>
    <Banner
      v-model:visible="visible"
      title="标题"
    />
    <Banner
      v-model:visible="visible"
      description="这是一段描述信息"
    />
    <Banner
      v-model:visible="visible"
      title="标题"
      description="这是一段描述信息"
    />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Banner, Button } from '@xhs/delight'

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
  }
</script>
```

##    通知横幅类型

通过 `type` 设置通知横幅类型：
- `type` 支持 `info` 、 `success` 、 `warning` 、 `danger` 共 `4` 个类型，默认 `info`

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Banner description="这是一段描述信息"/>
    <Banner
      type="success"
      description="这是一段描述信息"
    />
    <Banner
      type="warning"
      description="这是一段描述信息"
    />
    <Banner
      type="danger"
      description="这是一段描述信息"
    />
  </Space>
</template>

<script setup lang="ts">
  import { Space, Banner } from '@xhs/delight'
</script>
```

##    对齐方式

通过 `align` 设置对齐方式：
- `align` 支持 `start` 、 `center` 、 `end` 共 `3` 个对齐方式，默认 `start`
- `title` 和 `description` 同时存在时 `align` 不生效

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Banner description="这是一段描述信息"/>
    <Banner
      align="center"
      description="这是一段描述信息"
    />
    <Banner
      align="end"
      description="这是一段描述信息"
    />
  </Space>
</template>

<script setup lang="ts">
  import { Space, Banner } from '@xhs/delight'
</script>
```

##    默认可关闭

通过 `closeable` 设置默认可关闭，默认为 `true`：

```vue
<template>
  <Banner description="这是一段描述信息" :closeable="false"/>
</template>

<script setup lang="ts">
  import { Banner } from '@xhs/delight'
</script>
```

##    自定义 icon

```vue
<template>
  <Space direction="vertical" align="unset" block>
    <Banner description="这是一段描述信息">
      <template #icon>
        <Icon :icon="AddWeb" />
      </template>
    </Banner>
    <Banner
      type="success"
      description="这是一段描述信息"
    >
      <template #icon>
        <Icon :icon="Adjustment" />
      </template>
    </Banner>
    <Banner
      type="warning"
      description="这是一段描述信息"
    >
      <template #icon>
        <Icon :icon="AdobeIndesign" />
      </template>
    </Banner>
    <Banner
      type="danger"
      description="这是一段描述信息"
    >
      <template #icon>
        <Icon :icon="AdobePhotoshop" />
      </template>
    </Banner>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Banner, Icon } from '@xhs/delight'
  import { AddWeb, Adjustment, AdobeIndesign, AdobePhotoshop } from '@xhs/delight/icons'
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|type|通知横幅类型|'info' &#124; 'success' &#124; 'warning' &#124; 'danger'|-|
|title|通知横幅标题|string|-|
|description|通知横幅描述信息|string|-|
|align|通知横幅的对齐方式|'start' &#124; 'center' &#124; 'end'|'start'|
|closeable|通知横幅默认可关闭|boolean|true|
|visible|通知横幅是否展示|boolean|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:visible|通知横幅展示状态变化的回调事件|(v: boolean) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|icon|自定义 icon|
|title|通知横幅的标题|
|description|通知横幅的描述信息|