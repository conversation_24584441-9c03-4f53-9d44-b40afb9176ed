

##    基本使用

通过 `Tabs` 加 `TabPane` ，展示一个带标签切换的内容区域：
 - `Tabs` 内只接收 `TabPane` 组件
 - 通过 `TabPane` 的 `icon` 设置图标
 - 通过 `TabPane` 的 `label` 设置标签名
 - 通过 `TabPane` 的 `count` 设置标签头的徽标内容
 - 通过 `TabPane` 的 `slots.rightIcon` 设置标签右侧icon
 - 通过 `TabPane` 的 `slots.default` 设置展示内容

```vue
<template>
  <Tabs>
    <TabPane :icon="International" label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane :icon="International" label="北美区" :count="12">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane :icon="International" label="欧洲区" >
      <template #rightIcon>
        <Icon :icon='Info' color="info" theme="filled"/>
      </template>
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane :icon="International" label="东南亚区" >
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
    <TabPane :icon="International" label="非洲区" >
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
    <TabPane :icon="International" label="南极区" >
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
    <TabPane :icon="International" label="北极区" >
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
    <TabPane :icon="International" label="大洋洲区" >
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
    <TabPane :icon="International" label="太平洋区" >
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Tabs>
    <template v-for="label of ['大中华区', '北美区', '欧洲区', '东南亚区']" :key="label">
      <TabPane :icon="International" :label="label">
        <Text class="--space-m-top-large" type="h6">{{ label }}</Text>
      </TabPane>
    </template>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result, Text, Icon } from '@xhs/delight'
  import { International, Danger, Home, Info, Warning} from '@xhs/delight/icons'
</script>
```

##    标签页的展示状态

通过 `modelValue` 控制标签页的展示状态，值为 `TabPane` 的 `id`，`id` 默认为 `TabPane` 的 `label`：

```vue
<template>
  <Tabs v-model="value">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区" id="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区" id="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Text>当前选中标签的 id 为：{{ value }}</Text>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tabs, TabPane, Result, Text } from '@xhs/delight'

  const value = ref('北美区')
</script>
```

##    尺寸

通过 `size` 设置 `Tabs` 的尺寸，分为 `default` 和 `small`，默认 `default`：
 - 仅对 `linear` 线性 `Tabs` 生效

```vue
<template>
  <Tabs>
    <TabPane :icon="International" label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Tabs size="small">
    <TabPane :icon="International" label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result } from '@xhs/delight'
  import { International } from '@xhs/delight/icons'
</script>
```

##    风格

通过 `theme` 设置 `Tabs` 的风格，分为 `linear` 和 `card`，默认 `linear`：
 - `linear` 线性 `Tabs`，常用于页面级别切换
 - `card` 卡片 `Tabs`，常用于模块内

```vue
<template>
  <Tabs theme="card">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result } from '@xhs/delight'
</script>
```

##    位置

通过 `position` 设置 `Tabs` 的位置，目前仅支持 `top` 和 `left`

```vue
<template>
  <Tabs position="left" :style="{height:'200px'}">
    <TabPane label="大中华区">
      <Result class="--space-m-left-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-left-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-left-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-left-large" status="forbidden" title="东南亚区"/>
    </TabPane>
    <TabPane label="大中华区1">
      <Result class="--space-m-left-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区1">
      <Result class="--space-m-left-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区1">
      <Result class="--space-m-left-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区1">
      <Result class="--space-m-left-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <br/>
  <br/>
  <Tabs position="left" >
    <TabPane label="大中华区">
      <Result class="--space-m-left-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-left-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-left-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-left-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result } from '@xhs/delight'
</script>
```

##    可关闭

通过 `closeable` 设置标签可关闭，并通过 `onClose` 接收当前关闭的 `TabPane` 的 `id`，`id` 默认为 `TabPane` 的 `index`：
 - `closeable` 也可以在 `TabPane` 上设置，具有更高的优先级

```vue
<template>
  <Tabs closeable @close="handleClose">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Tabs theme="card" closeable @close="handleClose">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Tabs theme="card" @close="handleClose">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区" closeable>
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区" closeable>
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区" closeable>
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result } from '@xhs/delight'

  function handleClose(id) {
    console.log(id)
  }
</script>
```

##    强制渲染

通过 `forceRender` 设置强制渲染，让未激活的 `TabPane` 预渲染后通过 `v-show` 控制展示状态，以获得更好的用户体验：
 - `forceRender` 也可以在 `TabPane` 上设置，具有更高的优先级

```vue
<template>
  <Tabs theme="card" forceRender>
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Tabs theme="card" forceRender>
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" force-render="false" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result } from '@xhs/delight'
</script>
```

##    TabPane 激活

通过 `TabPane` 上的 `active` 设置当前 `TabPane` 被激活，若同时设置了 `modelValue` 则会覆盖 `modelValue` ：

```vue
<template>
  <Tabs theme="card" v-model="value">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区" active>
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Text>当前选中标签的 id 为：{{ value }}</Text>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tabs, TabPane, Result, Text } from '@xhs/delight'

  const value = ref('欧洲区')
</script>
```

##    TabPane 禁用

通过 `TabPane` 上的 `disabled` 设置当前 `TabPane` 禁用：

```vue
<template>
  <Tabs>
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区" disabled>
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
  <Tabs theme="card">
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区" disabled>
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result, Text } from '@xhs/delight'
</script>
```

##    完全自定义 icon 和 label

通过 `TabPane` 的 `tab` 插槽对当前 `TabPane` 的标签头进行自定义，注意：若使用了插槽，则在 `TabPane` 上传入的 icon、label、count 将不显示，且需要在插槽中自定义 icon 的样式：

```vue
<template>
  <Tabs>
    <TabPane label="大中华区">
      <template #tab="{ icon, label, extra, active, disabled, count, id }">
        <Icon
         :color="active ? 'primary' : '' "
         :class="[disabled && 'disabled']"
        >
          <svg width="100%" height="100%" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.99959 10.8333C10.4598 10.8333 10.8329 10.4602 10.8329 9.99996C10.8329 9.53972 10.4598 9.16663 9.99959 9.16663C9.53936 9.16663 9.16626 9.53972 9.16626 9.99996C9.16626 10.4602 9.53936 10.8333 9.99959 10.8333Z" fill="currentColor" fill-opacity="0.9"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M14.419 5.58053C14.6489 5.81037 14.7241 6.15288 14.6117 6.45788L12.5493 12.0558C12.465 12.2848 12.2845 12.4653 12.0555 12.5497L6.45754 14.6121C6.15254 14.7244 5.81004 14.6492 5.5802 14.4194C5.35036 14.1895 5.27513 13.847 5.3875 13.542L7.4499 7.9441C7.53426 7.7151 7.71477 7.5346 7.94376 7.45023L13.5417 5.38784C13.8467 5.27547 14.1892 5.35069 14.419 5.58053ZM8.88084 8.88117L7.5756 12.424L11.1184 11.1187L12.4236 7.57593L8.88084 8.88117Z" fill="currentColor" fill-opacity="0.9"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0.833008 10C0.833008 4.93743 4.93706 0.833374 9.99967 0.833374C15.0623 0.833374 19.1663 4.93743 19.1663 10C19.1663 15.0626 15.0623 19.1667 9.99967 19.1667C4.93706 19.1667 0.833008 15.0626 0.833008 10ZM9.99967 2.50004C5.85754 2.50004 2.49967 5.8579 2.49967 10C2.49967 14.1422 5.85754 17.5 9.99967 17.5C14.1418 17.5 17.4997 14.1422 17.4997 10C17.4997 5.8579 14.1418 2.50004 9.99967 2.50004Z" fill="currentColor" fill-opacity="0.9"/>
          </svg>
        </Icon>
        <span :class="[disabled && 'disabled']">{{ formLabel(label, extra) }}</span>
        <span v-if="count" style="color: red">{{count + 1}}</span>
      </template>
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区">
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区" :icon="International">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { Tabs, TabPane, Result, Icon } from '@xhs/delight'
  import { International } from '@xhs/delight/icons'

  function formLabel(label, extra) {
    return extra?.description
      ? (label + '（' + extra.description + '）')
      : label
  }
</script>
```

##    自定义标签头

通过 `Tab` 的 `slots.header` 自定义标签头：

```vue
<template>
  <Tabs v-model="value">
    <template #header="{ icon, label, extra, closeable, active, disabled, count, id }">
      <Text
        :class="['d-clickable', disabled && 'disabled']"
        @click="() => !disabled && handleClick(id)"
      >
        <del v-if="disabled">{{ formLabel(label, extra) }}</del>
        <template v-else="disabled">{{ formLabel(label, extra) }}</template>
        <template v-if="count !== undefined">{{ count }}</template>
      </Text>
    </template>
    <TabPane label="大中华区">
      <Result class="--space-m-top-large" status="success" title="大中华区"/>
    </TabPane>
    <TabPane label="北美区" :extra="{ description: '暂无数据' }" disabled>
      <Result class="--space-m-top-large" status="fail" title="北美区"/>
    </TabPane>
    <TabPane label="欧洲区">
      <Result class="--space-m-top-large" status="404" title="欧洲区"/>
    </TabPane>
    <TabPane label="东南亚区">
      <Result class="--space-m-top-large" status="forbidden" title="东南亚区"/>
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tabs, TabPane, Result, Text } from '@xhs/delight'

  const value = ref()

  function formLabel(label, extra) {
    return extra?.description
      ? (label + '（' + extra.description + '）')
      : label
  }

  function handleClick(id) {
    value.value = id
  }
</script>
```

##    Tabs API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue|标签页当前激活标签的 id|string|-|
|theme|标签页的风格|`'linear'` \| `'card'`|`'linear'`|
|size|标签页的大小|`'default'` \| `'small'`|`'default'`|
|position|标签页的方位|`'top'` \| `'left'`|`'top'`|
|closeable|标签页是否可关闭|`boolean`|`false`|
|forceRender|标签页是否强制渲染|`boolean`|`false`|

### Tabs 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:modelValue|标签页当前激活标签变化的回调事件|(id: string) => void|-|
|close|标签点击关闭的回调事件|(e: MouseEvent) => void|-|
|tab-click|每个 tab 点击的回调时间|`(e: MouseEvent, v: { id?: string; label?: string }) => void`|-|

### Tabs 插槽 |插槽|说明| 作用域 |
| :- | :- | :- |
|header|自定义标签头|`#header="{icon, label, extra, closeable, active, disabled, count, id}"`|
|actionBefore|自定义Tabs左边区域|-|
|actionAfter|自定义Tabs右边区域|-|

##    TabPane API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|icon|标签头的[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|(p: IconProps) => string|-|
|label|标签头的内容|string|-|
|count|标签头的徽标内容|number|-|
|extra|标签头的额外参数，常用于自定义标签头|object|-|
|closeable|标签页是否可关闭，会覆盖 `Tabs` 上的 closeable|boolean|false|
|active|标签页是否激活，会覆盖 `Tabs` 上的 modelValue|boolean|false|
|disabled|标签页是否禁用|boolean|false|
|forceRender|标签页是否强制渲染|boolean|false|
|id|标签页的唯一标识，默认为标签页的 `label`|string|`label`|

### TabPane 插槽 |插槽|说明|作用域|
| :- | :- | :- |
|tab|自定义当前 TABPANE 的标签头|`#tab="{icon, label, extra, closeable, active, disabled, count, id}"`|
|rightIcon|自定义当前 TABPANE 的标签右侧icon|`#rightIcon`|