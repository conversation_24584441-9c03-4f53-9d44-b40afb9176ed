

##    基本使用

```vue
<template>
   <div class="demo-robin-feedback" ref="triggerRef" @mousedown="dragMouseDown">
    <div class="demo-robin-feedback-bottom-end">
      <Feedback
        welcomeText="Hi 喜茶"
        thanksTitle="感谢您的反馈"
        thanksDescription=“反馈已收到，感谢您对我们工作的支持～”
        :questions="questions"
        :isDrag="isDrag"
        @toggleRatingReason="toggleRatingReason"
        @submit="handleSubmit"
      />
    </div>
   </div>
</template>

<script setup lang="ts">
  import { reactive, onMounted,ref } from 'vue'
  import { RobinFeedback as Feedback } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  const triggerRef = ref()
  const isDrag = ref(false)
  let pos1 = 0; let pos2 = 0; let pos3 = 0; let pos4 = 0
  function closeDragElement() {
    console.log('wrapper mouseup',isDrag.value);
    document.onmouseup = null
    document.onmousemove = null
  }
  function elementDrag(e) {
    isDrag.value = true
    console.log('wrapper mousedmove',isDrag.value);
    
    e = e || window.event
    e.preventDefault()
    pos1 = pos3 - e.clientX
    pos2 = pos4 - e.clientY
    pos3 = e.clientX
    pos4 = e.clientY
    triggerRef.value.style.top =  triggerRef.value.offsetTop - pos2 + 'px'
    triggerRef.value.style.left = triggerRef.value.offsetLeft - pos1 + 'px'
  }
  function dragMouseDown(e) {
    console.log('wrapper mousedown',isDrag.value);
    isDrag.value = false
    e = e || window.event
    e.preventDefault()
    pos3 = e.clientX
    pos4 = e.clientY
    document.onmouseup = closeDragElement
    document.onmousemove = elementDrag
  }

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function handleUpload({ onSuccess, onProgress, file, onError }) {
    console.log('----',file)
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比，腾讯云返回的 percent 是 0.x
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }

  const questions = reactive([
    {
      type: 'RATING',
      RATING: {
        title: '您向同事推荐此产品的可能性有多大？',
         reasonArr: [
          '操作麻烦',
          '效率降低',
          '不习惯',
          '容易点错',
          '无法完成任务',
          '不好上手',
          '布局不合理',
          '找不到功能',
        ], 
        showReason: false,
        rateTexts: [
          '请填写',
          '效率降低',
          '不习惯',
          '容易点错',
          '无法完成任务',
          '不好上手',
        ]
      }
    },
    {
      type: 'QA',
      QA: {
        title: '提个小建议吧',
        qaTip: '请输入...',
        limitLength: 200,
        tipOptional: true,
      }
    },
    {
      type: 'SCREENSHOT',
      SCREENSHOT: {
        title: '告诉我们您具体在哪里遇到了问题 ⬇️',
        limitNum: 3,
        uploadTip: '可提交 3 张图片，每张大小不超过 2 M',
        accept: "image/jpg, image/png",
        handleUpload,
      },
    }
  ])

  const toggleRatingReason = ({ questionIdx, val }) => {
    if (val <= 6) {
      questions[questionIdx].RATING.showReason = true
    } else {
      questions[questionIdx].RATING.showReason = false
    }
  }

  const handleSubmit = (formModel) => {
     console.log('formModel', formModel)
  }
  
</script>

<style scoped>
  .demo-robin-feedback {
    position: fixed;
    top: 400px;
    left: 400px
  }
</style>
```

##    预览模式

- 使用属性 `isPreview` 设置是否是预览模式。

```vue
<template>
   <Feedback
      welcomeText="Hi 喜茶"
      thanksTitle="感谢您的反馈"
      thanksDescription=“反馈已收到，感谢您对我们工作的支持～”
      :questions="questions"
      @toggleRatingReason="toggleRatingReason"
      @submit="handleSubmit"
      isPreview
    />
</template>

<script setup lang="ts">
  import { reactive, onMounted } from 'vue'
  import { RobinFeedback as Feedback } from '@xhs/delight'
  import Uploader from '@xhs/uploader'
  import { http } from '@xhs/launcher'
  import { TokenRequest, TokenResponse } from '@xhs/uploader'

  function getToken(params) {
    return http
      .get('TOKEN', { params: { ...params, subsystem: 'web_resource' }, withCredentials: true })
  }

  const uploader = new Uploader(
    {
      bizName: 'capa',
      scene: 'notes',
      getToken,
    },
  )

  function handleUpload({ onSuccess, onProgress, file, onError }) {
    uploader.post({
      Body: file,
      onProgress({ percent }) {
        // 目前接受的 percent 是百分比，腾讯云返回的 percent 是 0.x
        onProgress({ percent: percent * 100 })
      },
    })
      .then(res => {
        console.log(res)
        if (res.success) {
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      })
  }

  const questions = reactive([
    {
      type: 'RATING',
      RATING: {
        type: 'satisfaction',
        title: '您向同事推荐此产品的可能性有多大？',
        reasonArr: [
          '操作麻烦',
          '效率降低',
          '不习惯',
          '容易点错',
          '无法完成任务',
          '不好上手',
          '布局不合理',
          '找不到功能',
        ], 
        showReason: false,
        rateTexts: [
          '请填写',
          '效率降低',
          '不习惯',
          '容易点错',
          '无法完成任务',
          '不好上手',
        ]
      }
    },
    {
      type: 'QA',
      QA: {
        title: '提个小建议吧',
        qaTip: '提个小建议吧',
        limitLength: 200,
        tipOptional: true,
      }
    },
    {
      type: 'SCREENSHOT',
      SCREENSHOT: {
        title: '告诉我们您具体在哪里遇到了问题 ⬇️',
        limitNum: 3,
        uploadTip: '可提交 3 张图片，每张大小不超过 2 M',
        accept: "image/jpg, image/png, image/webp",
        handleUpload,
      },
    }
  ])

  const toggleRatingReason = ({ questionIdx, val }) => {
    if (val <= 3) {
      questions[questionIdx].RATING.showReason = true
    } else {
      questions[questionIdx].RATING.showReason = false
    }
  }

  const handleSubmit = (formModel) => {
     console.log('formModel', formModel)
  }
  
</script>
```

##    API 参考

```
interface Question {
  type: QuestionType
  [QuestionType.RATING]?: RatingQuestion
  [QuestionType.QA]?: QaQuestion
  [QuestionType.SCREENSHOT]?: ScreenshotQuestion
}

enum QuestionType {
  RATING = 'RATING',
  QA = 'QA',
  SCREENSHOT = 'SCREENSHOT',
}

interface RatingQuestion {
  showReason: boolean
  reasonArr: string[]
  randomOrder: boolean
  title: string
  // 设置题型，'nps'为评分，'satisfaction'为满意度
  type: 'nps' | 'satisfaction'
  // 设置满意度文案
  rateTexts?: string[]
}

interface QaQuestion {
  title: string
  qaTip: string
  tipOptional: boolean
  shouldLimit: boolean
  limitLength: number
}

interface ScreenshotQuestion {
  title: string
  limitNum: number
  uploadTip: string
  allowUpload: boolean
  shouldLimitFormat: boolean
  format: UploadFormat[]
  limitSize: number
  accpet: string
  handleUpload?: (e:any) => void
}
```

通过设置 RobinFeedback 的属性来描述反馈组件：

|属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|welcomeText|欢迎词|string|-|
|thanksTitle|感谢语的标题|string|-|
|thanksDescription|感谢语标题的描述文案|string|-|
|questions|反馈问题数组|`Question[]`|`[]`|
|isPreview|是否是预览模式|boolean|false|

### 事件 |属性 | 说明 | 类型 | 默认值|
| :- | :- | :- | :- |
|submit|点击发送的回调事件|`(formModel): void`|-|
|toggleRatingReason|点击评分的回调事件|`({ questionIdx: number, val: number }): void`|-|
|cancel| 点击关闭按钮的回调事件 | `(formModel): void`|-|