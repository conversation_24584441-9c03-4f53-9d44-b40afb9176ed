## 图片头像

通过 `src` 设置图片头像：

```vue
<template>
  <Avatar
    src="https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png"
  />
</template>
<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 图片填充方式

通过 `objectFit` 设置图片填充方式，默认 `cover`：

```vue
<template>
  <Avatar
    object-fit="contain"
    src="https://interactive-examples.mdn.mozilla.net/media/examples/plumeria.jpg"
  />
  <Avatar
    object-fit="cover"
    src="https://interactive-examples.mdn.mozilla.net/media/examples/plumeria.jpg"
  />
  <Avatar
    object-fit="fill"
    src="https://interactive-examples.mdn.mozilla.net/media/examples/plumeria.jpg"
  />
  <Avatar
    object-fit="none"
    src="https://interactive-examples.mdn.mozilla.net/media/examples/plumeria.jpg"
  />
  <Avatar
    object-fit="scale-down"
    src="https://interactive-examples.mdn.mozilla.net/media/examples/plumeria.jpg"
  />
</template>
<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 图片加载失败

通过 `onError` 设置图片加载失败的回调，可通过返回 `false` 或 `Promise` 取消默认 `fallback` 行为：

```vue
<template>
  <Avatar src="error link" />
  <Avatar src="error link">
    <img
      src="https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png"
    />
  </Avatar>
  <Avatar src="error link" text="miss" />
  <Avatar src="error link" text="miss" :on-error="() => false" />
</template>
<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 文字头像

通过 `text` 设置文字头像：

- 当字符串较长时，字体大小会根据头像宽度自动调整，最小 12px。

```vue
<template>
  <Avatar text="D" />
  <Avatar text="De" />
  <Avatar text="Del" />
  <Avatar text="Deli" />
  <Avatar text="Delig" />
  <Avatar text="Deligh" />
  <Avatar text="Delight" />
</template>

<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 文字边距

通过 `gap` 设置文字边距：

- 文字边距最小为 `4px`。

```vue
<template>
  <Avatar text="Delight" :gap="0" />
  <Avatar text="Delight" :gap="1" />
  <Avatar text="Delight" :gap="2" />
  <Avatar text="Delight" :gap="3" />
  <Avatar text="Delight" :gap="4" />
  <Avatar text="Delight" :gap="5" />
  <Avatar text="Delight" :gap="6" />
  <Avatar text="Delight" :gap="7" />
  <Avatar text="Delight" :gap="8" />
  <Avatar text="Delight" :gap="9" />
</template>

<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 文字颜色

通过 `color` 设置文字颜色：

```vue
<template>
  <Avatar text="Delight" color="red-1" />
  <Avatar text="Delight" color="red-2" />
  <Avatar text="Delight" color="red-3" />
  <Avatar text="Delight" color="red-4" />
  <Avatar text="Delight" color="red-5" />
  <Avatar text="Delight" color="red-6" />
  <Avatar text="Delight" color="red-7" />
  <Avatar text="Delight" color="red-8" />
  <Avatar text="Delight" color="red-9" />
</template>

<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 背景颜色

通过 `backgroundColor` 设置背景颜色：

```vue
<template>
  <Avatar text="Delight" background-color="red-1" />
  <Avatar text="Delight" background-color="red-2" />
  <Avatar text="Delight" background-color="red-3" />
  <Avatar text="Delight" background-color="red-4" />
  <Avatar text="Delight" background-color="red-5" />
  <Avatar text="Delight" background-color="red-6" />
  <Avatar text="Delight" background-color="red-7" />
  <Avatar text="Delight" background-color="red-8" />
  <Avatar text="Delight" background-color="red-9" />
</template>

<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 尺寸

通过 `size` 设置尺寸，可选 `extra-small`、`small`、`default`、`large` 、`extra-large`、`number`，默认 `default`：

```vue
<template>
  <Avatar text="D" size="extra-small" />
  <Avatar text="D" size="small" />
  <Avatar text="D" />
  <Avatar text="D" size="large" />
  <Avatar text="D" size="extra-large" />
  <Avatar text="D" :size="100" />
</template>

<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 变体

通过 `shape` 设置变体，可选 `circle`、`square`，默认 `circle`：

```vue
<template>
  <Avatar text="D" shape="square" />
  <Avatar text="D" />
</template>
<script setup lang="ts">
import { Avatar } from "@xhs/delight";
</script>
```

## 头像组

通过在 `AvatarGroup` 中设置 `avatars` 生成头像组：

```vue
<template>
  <AvatarGroup :avatars="avatars" />
</template>
<script setup lang="ts">
import { AvatarGroup } from "@xhs/delight";

const avatars = [
  {
    text: "D",
    name: "D",
  },
  {
    text: "Deli",
    name: "Deli",
  },
  {
    text: "Delight",
    name: "Delight",
  },
  {
    src: "https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png",
    name: "avatar",
  },
];
</script>
```

## 头像组尺寸

通过在 `AvatarGroup` 中设置 `size` 配置头像组尺寸：

```vue
<template>
  <Space direction="vertical" align="start">
    <AvatarGroup :avatars="avatars" size="extra-small" />
    <AvatarGroup :avatars="avatars" size="small" />
    <AvatarGroup :avatars="avatars" />
    <AvatarGroup :avatars="avatars" size="large" />
    <AvatarGroup :avatars="avatars" size="extra-large" />
    <AvatarGroup :avatars="avatars" :size="100" />
  </Space>
</template>
<script setup lang="ts">
import { Space, AvatarGroup } from "@xhs/delight";

const avatars = [
  {
    text: "D",
    name: "D",
  },
  {
    text: "Deli",
    name: "Deli",
  },
  {
    text: "Delight",
    name: "Delight",
  },
  {
    src: "https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png",
    name: "avatar",
  },
];
</script>
```

## 头像组变体

通过在 `AvatarGroup` 中设置 `shape` 配置头像组变体：

```vue
<template>
  <AvatarGroup :avatars="avatars" shape="square" />
</template>
<script setup lang="ts">
import { AvatarGroup } from "@xhs/delight";

const avatars = [
  {
    text: "D",
    name: "D",
  },
  {
    text: "Deli",
    name: "Deli",
  },
  {
    text: "Delight",
    name: "Delight",
  },
  {
    src: "https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png",
    name: "avatar",
  },
];
</script>
```

## 头像组重叠方式

通过在 `AvatarGroup` 中设置 `overlapFrom` 配置头像组重叠方式，可选 `start`、`end`，默认 `end`：

```vue
<template>
  <AvatarGroup :avatars="avatars" overlap-from="end" />
</template>
<script setup lang="ts">
import { AvatarGroup } from "@xhs/delight";

const avatars = [
  {
    text: "D",
    name: "D",
  },
  {
    text: "Deli",
    name: "Deli",
  },
  {
    text: "Delight",
    name: "Delight",
  },
  {
    src: "https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png",
    name: "avatar",
  },
];
</script>
```

## 头像组最大数量

通过在 `AvatarGroup` 中设置 `maxCount` 配置头像组最大数量：

- `size="extra-small"` 时 `Popover` 的样式略有不同

```vue
<template>
  <Space direction="vertical" align="start">
    <AvatarGroup :avatars="avatars" :max-count="2" size="extra-small" />
    <AvatarGroup
      :avatars="avatars"
      :max-count="2"
      size="extra-small"
      shape="square"
    />
    <AvatarGroup :avatars="avatars" :max-count="2" />
    <AvatarGroup :avatars="avatars" :max-count="2" shape="square" />
  </Space>
</template>
<script setup lang="ts">
import { Space, AvatarGroup } from "@xhs/delight";

const avatars = [
  {
    text: "D",
    name: "D",
  },
  {
    text: "Deli",
    name: "Deli",
  },
  {
    text: "Delight",
    name: "Delight",
  },
  {
    src: "https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png",
    name: "avatar",
  },
];
</script>
```

## Avatar API 参考

通过设置 Avatar 的属性来产生不同的样式：

| 属性            | 说明                                                                                 | 类型                                                                                              | 默认值    |
| :-------------- | :----------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------ | :-------- |
| size            | 头像的大小                                                                           | 'extra-small' &#124; 'small' &#124; 'default' &#124; 'large' &#124; 'extra-large' &#124; 'number' | 'default' |
| shape           | 头像的变体                                                                           | 'circle' &#124; 'square'                                                                          | 'circle'  |
| backgroundColor | 头像的背景色                                                                         | [PaletteColor](https://delight.devops.xiaohongshu.com/delight/token#common-color)                 | 'grey-4'  |
| src             | 图片类型头像的 url                                                                   | string                                                                                            | -         |
| objectFit       | 图片的适配方式                                                                       | 'contain' &#124; 'cover' &#124; 'fill' &#124; 'none' &#124; 'scale-down'                          | 'cover'   |
| onError         | 图片的加载失败的回调方法，，可通过返回 `false` 或 `Promise` 取消默认 `fallback` 行为 | (e: Event) => boolean &#124; Promise&lt;boolean&gt;                                               |
| text            | 文本类型头像的文字                                                                   | string                                                                                            | -         |
| gap             | 文本类型头像的文字与左右边界的距离                                                   | number                                                                                            | 4         |
| color           | 文本类型头像的颜色                                                                   | [PaletteColor](https://delight.devops.xiaohongshu.com/delight/token#common-color)                 | 'white'   |
| name            | 头像对应的名称，仅在 `AvatarGroup` 中生效                                            | string                                                                                            | -         |

### Avatar 事件 |事件|说明|类型|默认值|

| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### Avatar 插槽 | 插槽 | 说明 |

| :------ | :--------- |
| default | 头像的内容 |

## AvatarGroup API 参考

```
interface AllAvatarProps {
  size?: 'extra-small' | 'small' | 'default' | 'large' | 'extra-large'
  shape?: 'circle' | 'square'
  backgroundColor?: PaletteColor
  src?: string
  objectFit?: ObjectFit
  onError?: (e: Event) => boolean | Promise<boolean>
  text?: string
  gap?: number
  color?: PaletteColor
  name?: string
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
}

```

通过设置 AvatarGroup 的属性来产生不同的样式：

| 属性         | 说明                                 | 类型                                                                              | 默认值    |
| :----------- | :----------------------------------- | :-------------------------------------------------------------------------------- | :-------- |
| avatars      | 头像组中的头像                       | AllAvatarProps[]                                                                  | -         |
| showPopup    | hover 头像组的时候展示下拉框         | boolean                                                                           | false     |
| size         | 头像组中头像的大小                   | 'extra-small' &#124; 'small' &#124; 'default' &#124; 'large' &#124; 'extra-large' | 'default' |
| shape        | 头像组中头像的变体                   | 'circle' &#124; 'square'                                                          | 'circle'  |
| overlapFrom  | 头像组中头像的重叠方式               | 'start' &#124; 'end'                                                              | 'start'   |
| maxCount     | 头像组中的头像最大数量，超出会被折叠 | number                                                                            | -         |
| popoverProps | 透传给 Popover 组件的属性            | object                                                                            | -         |

### AvatarGroup 事件 |事件|说明|类型|默认值|

| :- | :- | :- | :- |
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|popupAvatarClick|点击 popover 中选项时触发的事件|(e: MouseEvent, v: AllAvatarProps) => void| - |
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|
