
import {Text} from '@xhs/delight'

##    基本使用

```vue
<template>
  <Popconfirm
    title="逍遥游"
    description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    @confirm="handleConfirm"
    @cancel="handleClose"
  >
    <Button type="primary">标准</Button>
  </Popconfirm>
  <br/>
  <br/>
  <br/>
  <Popconfirm
    :closeable="false"
    @confirm="handleConfirm"
    @cancel="handleClose"
  >
    <template #description>
      <Space>
        <Icon color="info"><component :is="Presets.Info"/></Icon>
        <Text>需要填写日期</Text>
      </Space>
    </template>
    <Button type="primary">无头部标题</Button>
  </Popconfirm>
</template>

<script setup>
  import { Button, Popconfirm, toast, Icon, Text, Space } from '@xhs/delight'
  import { Presets } from '@xhs/delight/icons'

  function handleConfirm() {
    toast.success('点击了确定')
  }

  function handleClose() {
    toast.danger('点击了取消')
  }

</script>
```

##    提示类型

通过 `type` 设置提示类型：
- `type` 支持 `info` 、 `success` 、 `warning` 、 `danger` 共 `4` 个类型，默认 `undefined`

```vue
<template>
  <Popconfirm
    :type="type"
    title="逍遥游"
    description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
  >
    <Space>
      <Button type="primary" @click="() => handleOpen()">default</Button>
      <Button type="primary" @click="() => handleOpen('info')">info</Button>
      <Button type="primary" @click="() => handleOpen('success')">success</Button>
      <Button type="primary" @click="() => handleOpen('warning')">warning</Button>
      <Button type="primary" @click="() => handleOpen('danger')">danger</Button>
    </Space>
  </Popconfirm>
</template>

<script setup>
  import { ref } from 'vue'
  import { Space, Button, Popconfirm, Text } from '@xhs/delight'

  const type = ref('')

  function handleOpen(t) {
    type.value = t
  }
</script>
```

##    尺寸

通过 `size` 设置尺寸：

```vue
<template>
  <Popconfirm
    :size="size"
    title="逍遥游"
    description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
  >
    <Space>
      <Button type="primary" @click="() => changeSize()">default</Button>
      <Button type="primary" @click="() => changeSize(300)">300px</Button>
      <Button type="primary" @click="() => changeSize('calc(100vw - 500px)')">calc(100vw - 500px)</Button>
    </Space>
  </Popconfirm>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Popconfirm, Text } from '@xhs/delight'

  const size = ref()

  function changeSize(s) {
    size.value = s
  }

</script>
```

##    点击外部关闭

通过 `outsideCloseable` 设置点击外部关闭

```vue
<template>
  <Popconfirm
    outside-closeable
    title="逍遥游"
    description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
  >
    <Button type="primary">outsideCloseable</Button>
  </Popconfirm>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, Popconfirm, Text } from '@xhs/delight'
</script>
```

##    条件触发

通过 `visible` 属性，灵活判断气泡框是否需要弹出，当绑定 `visible` 时，气泡框的开启和关闭都要在相应函数中设置：

```vue
<template>
  <Popconfirm
    :visible="visible"
    title="逍遥游"
    description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    @confirm="handleClose"
    @cancel="handleClose"
    @update:visible="handleChange"
  >
    <Button type="primary" @click="handleOpen">Popconfirm</Button>
  </Popconfirm>
  
  <p>Whether directly execute：</p>
  <Checkbox v-model:checked="step">下一步</Checkbox>
</template>

<script setup>
  import { ref } from 'vue'
  import { Button, Popconfirm, Text, toast, Checkbox } from '@xhs/delight'

  const visible = ref(false)
  const step = ref(false)
  const success = () => {
    toast.success('下一步')
  }

  function handleOpen() {
    if (step.value) {
      success()
    } else {
      visible.value = true
    }
  }

  function handleClose() {
    visible.value = false
    success()
  }

  function handleChange(v) {
      if (!v) {
        visible.value = false
        return
      }
      if (step.value) {
        success()
      } else {
        visible.value = true
      }
    }
</script>
```

##    自定义按钮文案

```vue
<template>
  <Popconfirm
    title="逍遥游"
    description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    confirmText="Yes"
    cancelText="No"
  >
    <Button type="primary">Popconfirm</Button>
  </Popconfirm>
</template>

<script setup>
  import { Button, Popconfirm } from '@xhs/delight'

</script>
```

##    自定义标题和内容

通过 `slots.title` 设置自定义标题，通过 `slots.description` 设置自定义内容：

```vue
<template>
  <Popconfirm>
    <template #title>
      <Text
        :icon="{ icon: Fire, theme: 'filled', color: 'red-6' }"
        type="h5"
        bold
      >
        逍遥游
      </Text>
    </template>
    <template #description>
      <Text size="small">
        北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
      </Text>
    </template>
    <Button type="primary">Popconfirm</Button>
  </Popconfirm>
</template>

<script setup>
  import { Button, Popconfirm, Text } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'
</script>
```

##    自定义气泡确认框操作栏

- 通过 `with-confirm` 设置是否有确认按钮，默认为 `true`
- 通过 `confirmText` 设置确认按钮的文案
- 通过 `confirmType` 设置确认按钮的类型
- 通过 `confirmButtonProps` 设置确认按钮的属性
- 通过 `with-cancel` 设置是否有取消按钮，默认为 `true`
- 通过 `cancelText` 设置取消按钮的文案
- 通过 `cancelType` 设置取消按钮的类型
- 通过 `cancelButtonProps` 设置取消按钮的属性
- 通过 `with-footer` 设置是否有操作栏，默认为 `true`
- 通过 `slots.footer` 设置自定义操作栏

```vue
<template>
  <Space>
    <Popconfirm
      v-model:visible="visible1"
      title="逍遥游"
      confirm-text="销毁"
      confirm-type="danger"
      :confirm-button-props="{ icon: Clear, loading }"
      :with-cancel="false"
      @confirm="handleCloseDefault"
    >
      <template #description>
        <Spinner :spinning="loading" tip="正在销毁数据" size="large">
          <Text>
            北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
          </Text>
        </Spinner>
      </template>
      <Button type="primary" @click="handleOpenDefault">default</Button>
    </Popconfirm>

    <Popconfirm
      title="逍遥游"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
      :with-footer="false"
    >
      <Button type="primary">no footer</Button>
    </Popconfirm>

    <Popconfirm
      v-model:visible="visible2"
      title="自定义 footer"
      description="北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”"
    >
      <template #footer>
        <Button type="danger" size="small" @click="handleCloseCustom">
          关闭
        </Button>
      </template>
      <Button type="primary" @click="handleOpenCustom">custom</Button>
    </Popconfirm>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Button, Popconfirm, Text, Spinner } from '@xhs/delight'
  import { Clear } from '@xhs/delight/icons'

  const visible1 = ref(false)
  const visible2 = ref(false)

  const loading = ref(false)

  function handleOpenDefault() {
    visible1.value = true
  }

  function handleCloseDefault() {
    loading.value = true

    setTimeout(
      () => {
        loading.value = false
        visible1.value = false
      },
      3000
    )
  }

  function handleOpenCustom() {
    visible2.value = true
  }

  function handleCloseCustom() {
    visible2.value = false
  }

</script>
```

##    API 参考

```
type ButtonType = 'default' | 'primary' | 'secondary' | 'danger' | 'light'

type ButtonSize = 'small' | 'default' | 'large'

type ButtonIconPosition = 'left' | 'right'

interface ButtonProps {
  type?: ButtonType
  size?: ButtonSize
  icon?:((p: IconProps) => string)
  iconPosition?: ButtonIconPosition
  iconSpace?: string | number
  disabled?: boolean
  loading?: boolean
  round?: boolean
  block?: boolean
  onClick?: (e: MouseEvent) => void
  onMousedown?: (e: MouseEvent) => void
  onMouseenter?: (e: MouseEvent) => void
  onMouseleave?: (e: MouseEvent) => void
  onMouseup?: (e: MouseEvent) => void
}

```

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|placement|气泡确认框展示的相对位置|'top' &#124; 'top-start' &#124; 'top-end' &#124; 'right' &#124; 'right-start' &#124; 'right-end' &#124; 'bottom' &#124; 'bottom-start' &#124; 'bottom-end' &#124; 'left' &#124; 'left-start' &#124; 'left-end'|'bottom'|
|offset|气泡确认框与目标元素的间距|number|4|
|arrow|气泡确认框展示指向目标元素的箭头|boolean|false|
|type|气泡确认框类型|'info' &#124; 'success' &#124; 'warning' &#124; 'danger'|-|
|title|气泡确认框标题|string|-|
|size|气泡确认框尺寸|'default' &#124; 'large' &#124; string &#124; number|'default'|
|withFooter|气泡确认框是否带操作栏|boolean|true|
|visible|气泡确认框是否展示|boolean|false|
|outsideCloseable|气泡确认框点击外部可关闭|boolean|false|
|withConfirm|气泡确认框操作栏带默认确认按钮|boolean|true|
|confirmText|气泡确认框操作栏默认确认按钮文案|string|-|
|confirmType|气泡确认框操作栏默认确认按钮类型|'default' &#124; 'primary' &#124; 'secondary' &#124; 'danger' &#124; 'light'|'primary'|
|confirmButtonProps|气泡确认框操作栏默认确认按钮属性|ButtonProps|-|
|withCancel|气泡确认框操作栏带默认取消按钮|boolean|true|
|cancelText|气泡确认框操作栏默认取消按钮文案|string|-|
|cancelType|气泡确认框操作栏默认取消按钮类型|'default' &#124; 'primary' &#124; 'secondary' &#124; 'danger' &#124; 'light'|'primary'|
|cancelButtonProps|气泡确认框操作栏默认取消按钮属性|ButtonProps|-|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:visible|气泡确认框展示状态变化的回调事件|(v: boolean) => void|-|
|confirm|默认确认按钮点击的回调事件|(e: MouseEvent) => void|-|
|cancel|默认取消按钮点击的回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|气泡确认框的目标|
|title|气泡确认框的标题|
|description|气泡确认框的内容|
|footer|气泡确认框的页脚操作栏|

##    Q&A

- 为什么Popconfirm 标题栏无法隐藏？
需要满足两个条件：1、不设置title，2、将closeable设置为false
