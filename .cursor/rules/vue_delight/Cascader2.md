
import { Link, Text } from '@xhs/delight'
import { Editor } from '@xhs/repl'

由于Cascader性能不满足现有业务需求，Delight 目前已经重构了 Cascader，Cascader2兼容了绝大部分CascaderAPI

后续Cascader将不再维护，请尽快升级到Cascader2
升级手册：升级到 Cascader2 注意项

##    基本使用

注意modelValue必须传递路径值

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" :options="options" @change="handleChange" />
    <Cascader v-model="value1" :options="options" @change="handleChange" />
    <Cascader v-model="value2" multiple :options="options" @change="handleChange" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref([])
  const value1 = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value2 = ref(['nanjing','qinhuai','The Confucius Temple'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]


  const handleChange = (changeVal, options) => {
    console.log('change', changeVal, options)
  }

</script>
```

##    移入展开

通过移入展开下级菜单，点击完成选择

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" expand-trigger="hover" :options="options" @change="handleChange" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref([])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

  const handleChange = (changeVal, options) => {
    console.log('change', changeVal, options)
  }

</script>
```

##    分隔符

通过 separator 设置分隔符

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" separator='🤔️' :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    选择器渐隐

通过 `fade` 设置渐隐风格：

渐隐风格在非交互状态时会隐去边框、背景和一些内部指示器如向下箭头
 - 通过 showIndicators 强制展示内部指示器
 - 通过 blankHighlight 在没有输入内容时展示高亮

```vue
<template>
  <Space direction="vertical">
    <Cascader v-model="value" fade :options="options" />
    <Cascader v-model="value" :fade="{ showIndicators: true }" :options="options" />
    <Cascader v-model="value" :fade="{ blankHighlight: true }" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref([])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    placeholder

通过 `placeholder` 设置提示文本

```vue
<template>
  <Space direction="vertical">
    <Cascader placeholder="你要去哪玩呀😎" v-model="value" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref([])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    前缀

通过 `prefix`属性 或 `prefix`插槽 设置前缀

```vue
<template>
  <Space direction="vertical">
    <Cascader prefix="目的地" v-model="value" :options="options" />
    <Cascader v-model="value" :options="options">
      <template #prefix>
        <Icon :icon="Local"/>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Icon, Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    后缀

通过 `suffix`属性 或 `suffix`插槽 设置后缀

```vue
<template>
  <Space direction="vertical">
    <Cascader suffix="目的地" v-model="value" :options="options" />
    <Cascader v-model="value" :options="options">
      <template #suffix>
        <Icon :icon="Local"/>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Icon, Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    清除输入

通过 `clearable` 开启清除输入功能

```vue
<template>
  <Space direction="vertical">
    <Cascader clearable v-model="value" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    多选

通过 `multiple` 设置多选，注意初始值需要设置成路径数组，多个初始值设置成二元组

```vue
<template>
  <Space direction="vertical">
    <Cascader multiple clearable v-model="value" :options="options" />
    <Cascader multiple clearable v-model="value1" :options="options" />
    <Cascader multiple clearable v-model="value2" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref([['nanjing','xuanwu','Xuanwu Lake']])
  const value2 = ref([['nanjing','xuanwu','Xuanwu Lake'],['nanjing','xuanwu','Nanjing Museum']])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    仅叶子节点

通过 `leafOnly` 设置多选时 `modelValue` 只包含叶子节点，这时展示的tag也只包含叶子节点

```vue
<template>
  <Space direction="vertical">
    <Cascader leafOnly multiple clearable v-model="value" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    自动合并 value

通过 `autoMergeValue` 来设置多选时选中祖先节点后 `modelValue` 不包含对应的子孙节点：
- 默认为 `true`
- 当 `autoMergeValue` 和 `leafOnly` 同时开启时，后者优先级更高

```vue
<template>
  <Space direction="vertical">
    <Cascader :autoMergeValue="false" multiple clearable v-model="value" :options="options" @change="handleChange"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

  const handleChange = (changeVal, options) => {
    console.log('change', changeVal, options)
  }

</script>
```

##    筛选选项

通过 `filterable` 设置选项可筛选

```vue
<template>
  <Space direction="vertical">
    <Cascader multiple filterable clearable v-model="value" :options="options" @change="handleChange"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

  const handleChange = (changeVal, options) => {
    console.log('change', changeVal, options)
  }

</script>
```

##    自定义筛选方法

通过 `filterMethod` 自定义筛选方法

```vue
<template>
  <Space direction="vertical">
    <Cascader :filter-method="filterMethod" multiple filterable clearable v-model="value" :options="options" @change="handleChange"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu'])
  const filterMethod = (filterValue, path) => {
    return path.some(option => option.label.toLowerCase().indexOf(filterValue.toLowerCase()) > -1)
  }

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

  const handleChange = (changeVal, options) => {
    console.log('change', changeVal, options)
  }

</script>
```

##    远端筛选

通过 `remote` 设置远端筛选：
- 远端筛选时需通过 `filterMethod` 来变更 `options`，此时 `filterMethod` 只有一个入参 `filterValue`

```vue
<template>
  <Space direction="vertical">
    <Cascader :filter-method="filterMethod" multiple filterable clearable v-model="value" :options="options"  :loading="loading" remote >
      <template #empty>
        <div style="padding: var(--size-space-large) 0;">
          <Result title="请输入筛选项进行搜索"/>
        </div>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader, Result, useDebounce } from '@xhs/delight'

  const loading = ref(false)
  const options = ref([])
  const value = ref([])

  function getOptions(v) {
    return [
      {
        label: v + 'A',
        value: v + 'A',
      },
      {
        label: '二级选项',
        value: v + '2',
        children: [
          {
            label: v + 'B',
            value: v + 'B',
          },
          {
            label: '三级选项',
            value: v + '3',
            children: [
              {
                label: v + 'C',
                value: v + 'C',
              },
              {
                label: v + 'D',
                value: v + 'D',
              },
            ]
          },
        ]
      },
    ]
  }

  const filterMethod = useDebounce(
    filterValue => {
      if (!!filterValue) {
        loading.value = true

        setTimeout(
          () => {
            options.value = getOptions(filterValue)
            loading.value = false
          },
          3000
        )
      }
    },
    { delay: 300 },
  )

</script>
```

##    禁用某一项是否可选中

通过 `disableSelect` 禁止某一项是否可以被选中，但是可以触发展示下级菜单。

```vue
<template>
  <Space direction="vertical">
    <Cascader 
      v-model="value" 
      :options="options" 
      checkStrictly
      clearable 
      @change="handleChange"
    />
    <Cascader 
      v-model="value1" 
      :options="options" 
      checkStrictly
      multiple 
      clearable 
      @change="handleChange"
    />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu'])
  const value1 = ref(['nanjing','xuanwu'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      disableSelect: true,
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      disableSelect: true,
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

  const handleChange = (changeVal, options) => {
    console.log('change', changeVal, options)
  }

</script>
```

##    选择任意一级选项

在单选模式下，你只能选择叶子节点；而在多选模式下，勾选父节点真正选中的都是叶子节点。 启用该功能后，可让父子节点取消关联，选择任意一级选项。

可通过 props.checkStrictly = true 来设置父子节点取消选中关联，从而达到选择任意一级选项的目的。

```vue
<template>
  <Space direction="vertical">
   <Cascader checkStrictly placeholder="可以选择任意一级" clearable v-model="value" :options="options" />
    <Cascader checkStrictly multiple clearable v-model="value1" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref([])
  const value1 = ref([['nanjing','xuanwu'],['nanjing','xuanwu','Purple Mountain']])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    标签最大展示数量

通过 `maxTagCount` 设置标签最大展示数量

```vue
<template>
  <Space direction="vertical">
    <Cascader :maxTagCount="3" leafOnly multiple clearable v-model="value" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    标签最大展示宽度

通过 `maxTagWidth` 设置标签最大展示宽度，并在触发省略时展示 `Tooltip`

```vue
<template>
  <Space direction="vertical">
    <Cascader :max-tag-width="100" leafOnly multiple clearable v-model="value" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院真好看', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    动态加载数据

loadData会传入当前选中的路径值的option组成数组，取数组最后一项就是当前选中的option，注意要将options重新赋值，需要加载数据的option请设置isLeaf，这样会展示expendIcon

```vue
<template>
  <Space direction="vertical">
    <Cascader clearable v-model="value" :options="options" :load-data="loadData"/>
    <Cascader multiple clearable v-model="value1" :options="options" :load-data="loadData"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref([])
  const value1 = ref([])

  const loadData = (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1]
    if(targetOption.value === 'nanjing'){
      setTimeout(() => {
        targetOption.children = [
          { label: '玄武区', value: 'xuanwu', isLeaf: false },
          { label: '秦淮区', value: 'qinhuai', isLeaf: false },
          { label: '江宁区', value: 'jiangning', isLeaf: false },
        ]
        options.value = [...options.value]
      }, 1000)
    }else if(targetOption.value === 'xuanwu' ){
      setTimeout(() => {
        targetOption.children = [
          { label: '鸡鸣寺', value: 'Jiming Temple',},
          { label: '玄武湖', value: 'Xuanwu Lake',},
          { label: '南京博物院', value: 'Nanjing Museum',},
          { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
          { label: '紫金山', value: 'Purple Mountain',},
        ]
        options.value = [...options.value]
      }, 1000)
    }else if(targetOption.value === 'qinhuai' ){
      setTimeout(() => {
        targetOption.children = [
          { label: '秦淮河', value: 'Qinhuai River',},
          { label: '夫子庙', value: 'The Confucius Temple',},
        ]
        options.value = [...options.value]
      }, 1000)
    }else if(targetOption.value === 'jiangning' ){
      setTimeout(() => {
        targetOption.children = [
          { label: '牛首山', value: 'niushoushan',},
        ]
        options.value = [...options.value]
      }, 1000)
    }
  }

  const options = ref([
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      isLeaf: false,
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ])

</script>
```

##    无数据

当 `options` 为空时默认展示无数据状态，可以通过 `slots.empty` 自定义无数据内容：

```vue
<template>
  <Space direction="vertical">
    <Cascader multiple clearable v-model="value" :options="options" />
    <Cascader :options="options" empty-text="No Data" />
    <Cascader :options="options">
      <template #empty>
        <div style="padding: var(--size-space-large) 0;">
          <Result title="暂无内容"/>
        </div>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader, Result } from '@xhs/delight'

  const value = ref([])

  const options = []

</script>
```

##    加载中

通过 `loading` 设置加载中，可以通过 `slots.loading` 自定义加载中内容：

```vue
<template>
  <Space direction="vertical">
    <Cascader loading clearable v-model="value" :options="options" />
    <Cascader loading multiple clearable v-model="value1" :options="options" >
      <template #loading>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing" title="加载中"/>
        </div>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader, Result } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    顶部 / 底部附加项

通过 `slots.top` 、 `slots.bottom` 设置顶部 / 底部附加项，会吸附在 `Dropdown` 顶部 / 底部：

```vue
<template>
  <Space direction="vertical">
    <Cascader multiple clearable v-model="value1" :options="options" >
      <template #top>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Cascader>
    <Cascader multiple clearable v-model="value1" :options="options" >
      <template #bottom>
        <div style="padding: var(--size-space-large) 0;">
          <Result status="constructing"/>
        </div>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader, Result } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    必填

通过 `required` 设置为必填项

```vue
<template>
  <Space direction="vertical">
    <Cascader required clearable v-model="value" :options="options" />
    <Cascader required multiple clearable v-model="value1" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref([])
  const value1 = ref([])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    自定义校验规则

通过 `validate` 自定义校验规则（不能排除存在选项同值的情况，参数区分单选、多选，单选为值的数组，多选为路径数组）

```vue
<template>
  <Space direction="vertical">
    <Cascader clearable v-model="value" :options="options" :validate="validate1"/>
    <Cascader multiple clearable v-model="value1" :options="options" :validate="validate2"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref(['nanjing','xuanwu','Xuanwu Lake'])

  function validate1({ modelValue}) {
    console.log('modelValue',modelValue,modelValue.includes('Xuanwu Lake'))
    return !modelValue.includes('Xuanwu Lake')
  }
  function validate2({ modelValue}) {
    if(modelValue.length > 0){
      return !modelValue[0].includes('Xuanwu Lake')
    }
    return false
  }

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    立即校验（包括必填、自定义校验规则）

通过设置 `validateTiming` 为 `immediate` 立即校验，默认仅在第一次失焦 / 点击 / 手动校验之后开始校验

```vue
<template>
  <Space direction="vertical">
    <Cascader clearable v-model="value" :options="options" :validate="validate1" validate-timing="immediate"/>
    <Cascader multiple clearable v-model="value1" :options="options" :validate="validate2" validate-timing="immediate"/>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref(['nanjing','xuanwu','Xuanwu Lake'])

  function validate1({ modelValue}) {
    console.log('modelValue',modelValue,modelValue.includes('Xuanwu Lake'))
    return !modelValue.includes('Xuanwu Lake')
  }
  function validate2({ modelValue}) {
    console.log('modelValue',modelValue)
    if(modelValue.length > 0){
      return !modelValue[0].includes('Xuanwu Lake')
    }
    return false
  }

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    手动校验（包括必填、自定义校验规则）

通过 `template ref` 获取 `validate` 方法手动校验

```vue
<template>
  <Space >
    <Cascader ref="cascader" clearable v-model="value" :options="options" :validate="validate" validate-timing="manual"/>
    <Button type="primary" @click="manualValidate">result: {{result}}</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader, Button } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const cascader = ref()
  const result = ref()

  function validate({ modelValue}) {
    console.log('modelValue',modelValue)
    if(modelValue.length > 0){
      return !modelValue.includes('Xuanwu Lake')
    }
    return false
  }
  function manualValidate() {
    cascader.value
      ?.validate()
      .then(
        res => {
          result.value = res
        }
      )
  }

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    自定义 tag / label

单选通过 `label`插槽 自定义 label，label 会接收到 `displayValue` 和  `disabled` 2 个参数
多选通过 `tag`插槽 自定义 tag，tag 会接收到 `displayValue` 以及 `maxWidth` 、 `disabled` 、 `onClose` 共 4 个参数
其中`displayValue`是一个对象，包含`label` `value` `key` `valueCells` `option` 五个字段，可以通过 `option` 字段内容传递额外需要的数据

```vue
<template>
  <Space direction="vertical">
    <Cascader clearable v-model="value" :options="options" validate-timing="immediate">
      <template v-slot:label="{ displayValue, disabled }">
        <span :style="{lineHeight: '22px'}"> {{displayValue.valueCells.join(' 🤡 ')}} </span>
      </template>
    </Cascader>
    <Cascader multiple clearable v-model="value1" :options="options" validate-timing="immediate">
      <template v-slot:tag="{ displayValue, maxWidth, disabled, onClose }">
        <Tag :avatar='avatar':max-width="maxWidth" round closeable size="paragraph" color="blue">
          {{displayValue.label}}
        </Tag>
      </template>
    </Cascader>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tag, Space, Cascader2 as Cascader } from '@xhs/delight'

  const avatar = {
    src: "https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png",
    size: "extra-small"
  }

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    块级元素

通过 `block` 设置为块级元素

```vue
<template>
  <Cascader block clearable v-model="value" :options="options" >
    <template #prefix>
      <Icon :icon="Local"/>
    </template>
  </Cascader>
  <br />
  <Cascader block leafOnly multiple clearable v-model="value1" :options="options" >
    <template #prefix>
      <Icon :icon="Local"/>
    </template>
  </Cascader>
</template>



<script setup lang="ts">
  import { ref } from 'vue'
  import { Icon, Cascader2 as Cascader } from '@xhs/delight'
  import { Local } from '@xhs/delight/icons'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref(['nanjing','xuanwu'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    禁用

通过 `disabled` 设置禁用

```vue
<template>
  <Space direction="vertical">
    <Cascader disabled clearable v-model="value" :options="options" />
    <Cascader disabled multiple clearable v-model="value1" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const value = ref(['nanjing','xuanwu','Xuanwu Lake'])
  const value1 = ref(['nanjing','xuanwu','Xuanwu Lake'])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    表单元素

通过 `Form` 、 `FormItem` 包裹设置为表单元素

```vue
<template>
  <Form @submit="handleSubmit">
    <FormItem
      name="1"
      label="标题"
      help="这是一段提示"
      description="这是一段 Cascader 的静态描述文本"
      on-error="这是一段 Cascader 的静态错误提示"
    >
      <Cascader v-model="value1" :options="options" required multiple :validate="validate"/>
    </FormItem>
    <FormItem
      name="2"
      label="标题"
      help="这是一段提示"
      :description="description"
      :on-error="onError"
    >
      <Cascader v-model="value2" :options="options" required multiple :validate="validate"/>
    </FormItem>
    <FormItem name="3">
      <Cascader v-model="value3" :options="options" required multiple :validate="validate"/>
      <template #label>标题</template>
      <template #help>这是一段提示</template>
      <template v-slot:description="{ modelValue, fullValue }">当前输入内容为：{{ modelValue?.join() }}</template>
      <template v-slot:onError="{ modelValue, fullValue }">{{
        modelValue !== undefined
          ? '当前输入内容为：' + modelValue?.join() + '，选项要求必须为 2 的倍数'
          :'必填项'
      }}</template>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Cascader2 as Cascader, Form, FormItem } from '@xhs/delight'

  const value1 = ref([])
  const value2 = ref([])
  const value3 = ref([])

  const options = [
    { label: '武汉',value: 'wuhan',tooltip: '暂未开放'},
    {
      label: '南京',
      value: 'nanjing',
      children: [
        {
          label: '玄武区',
          value: 'xuanwu',
          children: [
            { label: '鸡鸣寺', value: 'Jiming Temple',},
            { label: '玄武湖', value: 'Xuanwu Lake',},
            { label: '南京博物院', value: 'Nanjing Museum',},
            { label: '中山陵', value: 'Sun Yat-sen Mausoleum',},
            { label: '紫金山', value: 'Purple Mountain',},
          ],
        },
        {
          label: '秦淮区',
          value: 'qinhuai',
          children: [
            { label: '秦淮河', value: 'Qinhuai River',},
            { label: '夫子庙', value: 'The Confucius Temple',},
          ],
        },
        {
          label: '江宁区',
          value: 'jiangning',
          children: [
            { label: '牛首山', value: 'niushoushan',},
          ],
        }
      ]
    },
    {
      label: '上海',
      value: 'shanghai',
      children: [
        { 
          label: '黄浦区',
          value: 'huangpu',
          children: [
            { label: '外滩', value: 'the Bund',},
            { label: '豫园', value: 'Yu Garden',},
            { label: '人民广场', value: "People's Square",},
          ]
        },
        {
          label: '徐汇区',
          value: 'xuhui',
          children: [{ label: '龙华寺',value: 'Longhua Temple'}]
        },
        {
          label: '浦东新区',
          value: 'pudong',
          children: [
            { label: '迪士尼', value: 'Disney'},
            { label: '东方明珠', value: 'the Oriental Pearl Tower'},
          ]
        },
        {
          label: '静安区',
          value: 'jingan',
          children: [{ label: '静安寺',value: "Jing'an Temple"}]
        },
        {
          label: '普陀区',
          value: 'putuo',
        },
        {
          label: '松江区',
          value: 'songjiang',
        }
      ]
    },
  ]

  function description({ modelValue, fullValue }) {
    return '当前输入内容为：' + modelValue?.join()
  }

  function onError({ modelValue, fullValue }) {
    return  modelValue !== undefined
        ? '当前输入内容为：' + modelValue?.join() + '，选项要求长度必须为 2 的倍数'
        :'必填项'
  }

  function validate({ modelValue }) {
    return modelValue?.length % 2 === 0
  }

  function handleSubmit(v) {
    console.log(v)
  }
</script>
```

##    自定义字段名

通过 `fieldNames` 设置自定义字段名，包括`label`、`value`、`children`

```vue
<template>
  <Space direction="vertical">
    <Cascader :field-names="fieldNames" clearable v-model="value" :options="options" />
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Cascader2 as Cascader } from '@xhs/delight'

  const fieldNames = { label: 'name', value: 'id', children: 'items' }
  const value = ref([])
  const value1 = ref([])

  const options = [
    { name: '武汉',id: 'wuhan', tooltip: '暂未开放'},
    {
      name: '南京',
      id: 'nanjing',
      items: [
        {
          name: '玄武区',
          id: 'xuanwu',
          items: [
            { name: '鸡鸣寺', id: 'Jiming Temple',},
            { name: '玄武湖', id: 'Xuanwu Lake',},
            { name: '南京博物院', id: 'Nanjing Museum',},
            { name: '中山陵', id: 'Sun Yat-sen Mausoleum',},
            { name: '紫金山', id: 'Purple Mountain',},
          ],
        },
        {
          name: '秦淮区',
          id: 'qinhuai',
          items: [
            { name: '秦淮河', id: 'Qinhuai River',},
            { name: '夫子庙', id: 'The Confucius Temple',},
          ],
        },
        {
          name: '江宁区',
          id: 'jiangning',
          items: [
            { name: '牛首山', id: 'niushoushan',},
          ],
        }
      ]
    },
    {
      name: '上海',
      id: 'shanghai',
      items: [
        { 
          name: '黄浦区',
          id: 'huangpu',
          items: [
            { name: '外滩', id: 'the Bund',},
            { name: '豫园', id: 'Yu Garden',},
            { name: '人民广场', id: "People's Square",},
          ]
        },
        {
          name: '徐汇区',
          id: 'xuhui',
          items: [{ name: '龙华寺',id: 'Longhua Temple'}]
        },
        {
          name: '浦东新区',
          id: 'pudong',
          items: [
            { name: '迪士尼', id: 'Disney'},
            { name: '东方明珠', id: 'the Oriental Pearl Tower'},
          ]
        },
        {
          name: '静安区',
          id: 'jingan',
          items: [{ name: '静安寺',id: "Jing'an Temple"}]
        },
        {
          name: '普陀区',
          id: 'putuo',
        },
        {
          name: '松江区',
          id: 'songjiang',
        }
      ]
    },
  ]

</script>
```

##    API 参考

#### Option     children?: OptionContent[]
    isLeaf: boolean
    disableSelect?: boolean
    [key: string]: any
  }
  type ValueType = (string | number | boolean)[] | (string | number | boolean)[][]
```

## Props |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|选择器的选中值, 无论multiple是否为true，modelValue都是数组|`(string \| numebr \| boolean)[] \| (string \| numebr \| boolean)[][]`|-|
|placeholder|选择器的提示文本|`string`|-|
|prefix|选择器的前缀内容|`string`|-|
|suffix|选择器的后缀内容|`string`|-|
|clearable|开启清除选择内容按钮|`boolean`|`false`|
|required|必填项|`boolean`|`false`|
|requiredError|必填报错（Form 中展示）|`string`|-|
|validate|自定义校验规则|`(args: {modelValue: ValueType}) => string \| boolean \| Promise`|-|
|validateDelay|校验延迟（ms）|`number`|`100`|
|validateTiming|校验时机，默认仅在第一次失焦 / 点击 / 手动校验开始校验|`'immediate'` \| `'blur'` \| `'manual'`|`'blur'`|
|validating|切换校验状态，动态设置时为 `true` 时会立即校验一次并切换到对应的校验状态，为 `false` 会回复到未校验状态|`boolean`|`false`|
|autofocus|自动获取焦点|`boolean`|`false`|
|hideAfterSelect|选中值后关闭下拉菜单|`boolean`|`false`|
|hideIndicator|不展示右侧指示器（向下箭头）|`boolean`|`false`|
|block|展示为块级元素|`boolean`|`false`|
|disabled|禁用|`boolean`|`false`|
|filterValue (v-model)|选择器筛选输入框的值|`string`|-|
|filterable|选择器是否可以筛选|`boolean`|`false`|
|filterMethod|选择器自定义筛选方法，`filterValue` 为当前用户输入的文字，`path` 为选项路径|`(filterValue: string, path: OptionContent[]) => boolean`|-|
|remote|选择器远端筛选|`boolean`|`false`|
|fade|渐隐风格|boolean \| `{showIndicators?: boolean; blankHighlight?: boolean}` |`false`|
|options|选择器中的选项|`OptionContent[]`|`[]`|
|multiple|选择器是否可以多选|`boolean`|`false`|
|multiLine|选择器是否多行展示选中值|`boolean`|`true`|
|leafOnly|多选时 `modelValue` 只包含叶子节点|`boolean`|`false`|
|loadData|动态加载数据|`(selectOptions: OptionContent[]) => void`|-|
|separator|分隔符|`string`|`'/'`|
|maxTagCount|选择器展示选中选项的最大数量|`number`|-|
|maxTagWidth|选择器展示选中选项的最大宽度，过长的内容会被省略并在 `Tooltip` 中展示完整内容|`number`|-|
|maxCountPopupClass|自定义最大数量浮层弹窗类名|`string` \| `object`|-|
|maxCountPopupStyle|自定义最大数量浮层弹窗样式|`string` \| `object`|-|
|showMaxCountPopup|是否显示最大数量的选项弹窗|`boolean`|`true`|
|checkStrictly|选择任意一级选项|`boolean`|`false`|
|expandTrigger|次级菜单的展开方式，可选 `'click'` 和 `'hover'`|`string`|`click`|
|fieldNames|自定义 `options` 中 `label` `name` `children` 的字段|`{ label: 'label', value: 'value', children: 'children' }`|-|
|autoMergeValue|多选时选中祖先节点后 `modelValue` 不包含对应的子孙节点，当 `autoMergeValue` 和 `leafOnly` 同时开启时，后者优先级更高|`boolean`|`true`|
|loading|选择器中下拉菜单展示 `loading` 状态|`boolean`|`false`|
|showFilter|是否展示默认筛选器|`boolean`|`true`|
|clearFilterValueAfterSelect|筛选模式下选择后是否清空搜索词|`boolean`|`true`|

### Cascader 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|选择器选中值变化的回调事件，不能排除存在 `value` 相同的选项，`onChange` 返回的永远是一个数组|`(values: ValueType, options: OptionContent[] \| OptionContent[][]) => void`|-|
|update:filterValue|选择器筛选值变化的回调事件|`(v: string) => void`|-|
|click|鼠标单击的回调事件|`(e: MouseEvent) => void`|-|
|focus|获得焦点的回调事件|`(e: FocusEvent) => void`|-|
|blur|失去焦点的回调事件|`(e: FocusEvent) => void`|-|
|clear|清除输入内容的回调事件|`(e: MouseEvent) => void`|-|

### Cascader 插槽 |插槽|说明|作用域|
| :- | :- |:- |
|prefix|选择器的前缀内容|-|
|suffix|选择器的后缀内容|-|
|label|自定义选择器选中内容（单选）|`#label="{ displayValue, disabled }"`|
|tag|自定义选择器标签（多选）|`#tag="{ displayValue, maxWidth, disabled, onClose }"`|
|optionContent|自定义选择器选项的内容区域|`#optionContent="{ option, active, disabled, markString, column }"`|
|loading|自定义选择器 `loading` 时下拉菜单展示的内容|-|
|empty|自定义选择器选项为空时下拉菜单展示的内容|-|
|top|选择器下拉菜单的顶部附加项，不随内容滚动|-|
|bottom|选择器下拉菜单的底部附加项，不随内容滚动|-|
|columnTop|选择器下拉菜单每列顶部附加项|`#columnTop="{ options, column, previousPath }"`|

### Cascader TEMPLATE REF API |内容|说明|类型|
| :- | :- | :- |
|$el|cascader dom 节点|`HTMLElement`|
|modelValue|选择器的选中值|`(string \| numebr \| boolean)[] \| (string \| numebr \| boolean)[][]`|
|required|必填项|`boolean`|
|blur|手动矢焦|`() => void`|
|focus|手动聚焦|`() => void`|
|validate|手动校验|`() => Promise`|
|reset|清空内容和状态|`() => void`|
|status|校验状态|`'default'` \| `'waiting'` \| `'error'`|
|validateError|校验报错|`string`|
|update|更新modelValue|`(v: (string \| numebr \| boolean)[] \| (string \| numebr \| boolean)[][]) => void`|