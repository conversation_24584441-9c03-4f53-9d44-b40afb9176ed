

##    尺寸

通过 `size` 设置标签的尺寸：

```vue
<template>
  <Space>
    <Tag closeable :help="{content:'提示信息', theme:'light'}">
      标签
      <template #prefix>
          <Icon :icon="Home" />
      </template>
    </Tag>
    <Tag closeable :help="{content:'提示信息'}"  size="small">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
    <Tag closeable :help="{content:'提示信息'}"  size="paragraph">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
    <Tag closeable :help="{content:'提示信息'}"  size="smallText">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
  </Space>
  <br/>
  <br/>
  <Space>
    <Tag round closeable :help="{content:'提示信息', theme:'light'}">
      标签
      <template #prefix>
          <Icon :icon="Home" />
      </template>
    </Tag>
    <Tag round closeable :help="{content:'提示信息'}"  size="small">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
    <Tag round closeable :help="{content:'提示信息'}"  size="paragraph">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
    <Tag round closeable :help="{content:'提示信息'}"  size="smallText">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
  </Space>
  <br/>
  <br/>
    <Space>
    <Tag :avatar='avatar' round closeable :help="{content:'提示信息', theme:'light'}">
      标签
      <template #prefix>
          <Icon :icon="Home" />
      </template>
    </Tag>
    <Tag :avatar='avatar' round closeable :help="{content:'提示信息'}"  size="small">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
    <Tag :avatar='avatar' round closeable :help="{content:'提示信息'}"  size="paragraph">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
    <Tag :avatar='avatar' round closeable :help="{content:'提示信息'}"  size="smallText">
      标签
      <template #prefix>
          <Icon :icon="Home" size="small"/>
      </template>
    </Tag>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Tag, Icon } from '@xhs/delight'
  import { Home } from '@xhs/delight/icons'
  const avatar = {
    src: "https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png",
    size: "extra-small"
  }
</script>
```

##    填色风格

通过 `theme` 设置标签的填色风格：

```vue
<template>
  <Space>
    <Tag>标签</Tag>
    <Tag theme="solid">标签</Tag>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Tag } from '@xhs/delight'
</script>
```

##    颜色

通过 `color` 设置标签的颜色：

```vue
<template>
  <Space>
    <Space direction="vertical" align="start">
      <Space>
        <Tag>标签</Tag>
        <Tag color="white">标签</Tag>
        <Tag color="orange">标签</Tag>
        <Tag color="red">标签</Tag>
        <Tag color="pink">标签</Tag>
        <Tag color="violet">标签</Tag>
        <Tag color="purple">标签</Tag>
        <Tag color="blue">标签</Tag>
        <Tag color="cyan">标签</Tag>
        <Tag color="teal">标签</Tag>
        <Tag color="green">标签</Tag>
      </Space>
      <Space>
        <Tag theme="solid">标签</Tag>
        <Tag theme="solid" color="orange">标签</Tag>
        <Tag theme="solid" color="red">标签</Tag>
        <Tag theme="solid" color="pink">标签</Tag>
        <Tag theme="solid" color="violet">标签</Tag>
        <Tag theme="solid" color="purple">标签</Tag>
        <Tag theme="solid" color="blue">标签</Tag>
        <Tag theme="solid" color="cyan">标签</Tag>
        <Tag theme="solid" color="teal">标签</Tag>
        <Tag theme="solid" color="green">标签</Tag>
      </Space>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Tag } from '@xhs/delight'
</script>
```

##    可关闭标签

通过 `closeable` 设置标签是否可关闭，点击关闭按钮后会触发 `onClose` 事件：

```vue
<template>
  <Text type="h6" bold>展示案例：</Text>
  <br/>
  <Space>
    <Space direction="vertical" align="start">
      <Space>
        <Tag closeable>标签</Tag>
        <Tag closeable color="white">标签</Tag>
        <Tag closeable color="orange">标签</Tag>
        <Tag closeable color="red">标签</Tag>
        <Tag closeable color="pink">标签</Tag>
        <Tag closeable color="violet">标签</Tag>
        <Tag closeable color="purple">标签</Tag>
        <Tag closeable color="blue">标签</Tag>
        <Tag closeable color="cyan">标签</Tag>
        <Tag closeable color="teal">标签</Tag>
        <Tag closeable color="green">标签</Tag>
      </Space>
      <Space>
        <Tag closeable theme="solid">标签</Tag>
        <Tag closeable theme="solid" color="orange">标签</Tag>
        <Tag closeable theme="solid" color="red">标签</Tag>
        <Tag closeable theme="solid" color="pink">标签</Tag>
        <Tag closeable theme="solid" color="violet">标签</Tag>
        <Tag closeable theme="solid" color="purple">标签</Tag>
        <Tag closeable theme="solid" color="blue">标签</Tag>
        <Tag closeable theme="solid" color="cyan">标签</Tag>
        <Tag closeable theme="solid" color="teal">标签</Tag>
        <Tag closeable theme="solid" color="green">标签</Tag>
      </Space>
    </Space>
  </Space>
  <br/>
  <br/>
  <Text type="h6" bold>使用案例：</Text>
  <br/>
  <Space>
    <template v-for="tag of tags">
      <Tag closeable @close="() => onClose(tag)">{{ tag }}</Tag>
    </template>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Text, Tag } from '@xhs/delight'

  let tags = ref([])

  let count = 15

  while(count > 0) {
    tags.value = [...tags.value, count]

    count -= 1
  }

  function onClose(tag) {
    tags.value = tags.value.filter(t => t !== tag)
  }
</script>
```

##    控制展示

通过 `visible` 控制标签是否展示：

```vue
<template>
  <Space direction="vertical" align="start">
    <Tag :visible="visible" closeable @close="onClose">标签</Tag>
    <Button @click="onRestore">restore tag</Button>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Tag, Button } from '@xhs/delight'

  const visible = ref(true)

  function onClose() {
    visible.value = false
  }

  function onRestore() {
    visible.value = true
  }
</script>
```

##    禁用

通过 `disabled` 设置标签禁用，仅当设置 `closeable` 时隐藏关闭按钮：

```vue
<template>
  <Space>
    <Space direction="vertical" align="start">
      <Space>
        <Tag disabled closeable>标签</Tag>
        <Tag disabled closeable color="white">标签</Tag>
        <Tag disabled closeable color="orange">标签</Tag>
        <Tag disabled closeable color="red">标签</Tag>
        <Tag disabled closeable color="pink">标签</Tag>
        <Tag disabled closeable color="violet">标签</Tag>
        <Tag disabled closeable color="purple">标签</Tag>
        <Tag disabled closeable color="blue">标签</Tag>
        <Tag disabled closeable color="cyan">标签</Tag>
        <Tag disabled closeable color="teal">标签</Tag>
        <Tag disabled closeable color="green">标签</Tag>
      </Space>
      <Space>
        <Tag disabled closeable theme="solid">标签</Tag>
        <Tag disabled closeable theme="solid" color="orange">标签</Tag>
        <Tag disabled closeable theme="solid" color="red">标签</Tag>
        <Tag disabled closeable theme="solid" color="pink">标签</Tag>
        <Tag disabled closeable theme="solid" color="violet">标签</Tag>
        <Tag disabled closeable theme="solid" color="purple">标签</Tag>
        <Tag disabled closeable theme="solid" color="blue">标签</Tag>
        <Tag disabled closeable theme="solid" color="cyan">标签</Tag>
        <Tag disabled closeable theme="solid" color="teal">标签</Tag>
        <Tag disabled closeable theme="solid" color="green">标签</Tag>
      </Space>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Tag } from '@xhs/delight'
</script>
```

##    API 参考

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|size|标签的大小|`'small' \| 'default' \| 'paragraph'  \| 'smallText'`  |'default'|
|theme|标签的风格|`'light' \| 'solid'`|'light'|
|color|标签的颜色|`'grey' \| 'orange' \| 'red' \| 'pink' \| 'violet' \| 'purple' \| 'blue' \| 'cyan' \| 'teal' \| 'green' \| 'white'`|'grey'|
|closeable|标签是否可关闭|`boolean`|false|
|visible|标签是否展示|`boolean`|true|
|disabled|标签禁用状态|`boolean`|false|
|shape|圆角tag|`boolean`|false|
|help|提示信息|`tooltipProps`|`{}`|
|avatar|头像|`avatarProps`|`{}`|
|tooltipProps|文字溢出时的提示（文本默认为tag的text）|`tooltipProps`|`{}`|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|close|标签点击关闭的回调事件|(e: MouseEvent) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|default|标签的内容|
|prefix|标签前缀|