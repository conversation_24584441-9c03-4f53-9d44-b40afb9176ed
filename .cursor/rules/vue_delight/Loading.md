
import { Space, Text } from '@xhs/delight'

## 区域加载 v-loading 默认状况下，Loading 遮罩会插入到绑定元素的子节点。 通过添加 fullscreen 修饰符，可以使遮罩插入至 Dom 中的 body 上。

```vue
  <template>
    <Card v-loading="visible">
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>

    <Button @click="handleClick">切换</Button>
  </template>

  <script setup>
    import { ref } from 'vue'
    import { Card, Button } from '@xhs/delight'

    const visible = ref(true)

    const handleClick = () => {
      visible.value = !visible.value
    }

  </script>
```

## 自定义加载中组件内容 v-loading 可以接受一个响应式对象，用于自定义 Loading 遮罩层。

也可以在绑定了 v-loading 指令的元素上添加 d-loading-text 属性，其值会被渲染为加载文案，并显示在加载图标的下方。 
类似地 d-loading-class、d-loading-background 属性分别用于自定义类名、自定义遮罩层背景色。

```vue
  <template>
    <!-- 项目中如果需要定制化，建议在注册插件的时候定制化，而不是在局部使用的时候定制化 -->
    <Card 
      v-loading="{
        class: 'my-custom-class',
        text: '加载中',
        background: 'rgba(0, 0, 0, .8)',
        visible: visible
      }"
    >
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>

    <Card 
      v-loading="visible"
      d-loading-text="加载中"
      d-loading-background="rgba(0, 0, 0, .8)"
      d-loading-class="my-custom-class"
    >
      北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其名为鹏。鹏之背，不知其几千里也；怒而飞，其翼若垂天之云。是鸟也，海运则将徙于南冥。南冥者，天池也。《齐谐》者，志怪者也。《谐》之言曰：“鹏之徙于南冥也，水击三千里，抟扶摇而上者九万里，去以六月息者也。”
    </Card>

    <Button @click="handleClick">切换</Button>
  </template>

  <script setup>
    import { ref } from 'vue'
    import { Card, Button } from '@xhs/delight'

    const visible = ref(true)

    const handleClick = () => {
      visible.value = !visible.value
    }
  </script>
```

##    API 参考

```ts
interface GlobalOptions {
  text?: string | VNode
  background?: string
  class?: string
  spinner?: VNode
}
```

## 注册       `import { vLoadingPlugin, vLoading } from '@xhs/delight'

    # 全局使用 使用 Launcher， options 是全局默认配置形参       app.vm.use(vLoadingPlugin, options?: GlobalOptions)

    # 局部使用       `export default { directives: { loading: vLoading }}

## 配置项 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|text|显示在加载图标下方的加载文案|string &#124; vnode|-|
|spinner|完全自定义加载icon|vnode|-|
|background|遮罩背景色|string|-|
|class|Loading 的自定义类名|string|-|

## 指令 |名称|说明|类型|
| :- | :- | :- |
|`v-loading`|是否显示动画|boolean|
|`d-loading-text`|显示在加载图标下方的加载文案|string|
|`d-loading-background`|背景遮罩的颜色|string|

## 修复符 |属性|说明|
| :- | :- |
|fullscreen|全屏 loaidng，将 loading 的遮罩层挂载到 body 上面|
|lock|是否锁住 loading 时候的父元素，禁止滚动|