

##    大小

通过 `size` 调整组件的大小：

```vue
<template>
  <Rate size="small"/>
  <Rate />
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'
</script>
```

##    分数范围

通过 `count` 调整组件的分数范围：

```vue
<template>
  <Rate :count="8"/>
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'
</script>
```

### 半分递增 通过 `allowHalf` 设置半分递增：

```vue
<template>
  <Rate allow-half/>
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'
</script>
```

##    清除分数

通过 `allowClear` 设置当第二次点击相同分数时清除分数：

```vue
<template>
  <Rate allow-clear/>
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'
</script>
```

##    展示当前分数

通过 `showScore` 展示当前分数：

```vue
<template>
  <Rate show-score/>
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'
</script>
```

##    展示分数对应的文案

通过 `texts` 和 `showText` 展示当前分数对应的文案：

```vue
<template>
  <Rate :texts="texts" show-text/>
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'

  const texts = {
    0: '打个分吧',
    1: '还需努力',
    2: '马马虎虎',
    3: '继续加油',
    4: '整体不错',
    5: '真棒！'
  }
</script>
```

##    Tooltip

通过 `tooltips` 设置每个分数的提示文案：

```vue
<template>
  <Rate :tooltips="tooltips"/>
  <Rate allow-half :tooltips="tooltips"/>
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'

  const tooltips = {
    0.5: '0.5',
    1: '1',
    1.5: '1.5',
    2: '2',
    2.5: '2.5',
    3: '3',
    3.5: '3.5',
    4: '4',
    4.5: '4.5',
    5: '5'
  }
</script>
```

##    自定义图标

通过 `character` 或 `slots.character` 自定义评分图标：

```vue
<template>
  <Rate :character="Fire"/>
  <Rate allow-half>
    <template #character>
      <Text type="h3" color="current" bold>囧</Text>
    </template>
  </Rate>
</template>

<script setup lang="ts">
  import { Rate, Text } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'
</script>
```

##    禁用

通过 `disabled` 禁用组件：

```vue
<template>
  <Rate disabled/>
</template>

<script setup lang="ts">
  import { Rate } from '@xhs/delight'
</script>
```

##    API 参考

通过设置 Icon 的属性来产生不同的按钮样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|character|[图标](https://delight.devops.xiaohongshu.com/delight/cmp/icon)|(p: IconProps) => string|-|
|size|评分组件的大小|'small' &#124; 'default'|'default'|
|count|评分组件的分数范围|number|5|
|modelValue|展示初始评分|number|-|
|allowHalf|评分组件的半分递增|boolean|false|
|allowClear|评分组件第二次点击相同分数时清除分数|boolean|false|
|showScore|评分组件是否展示当前分数|boolean|false|
|showText|评分组件是否展示当前分数对应的文案|boolean|false|
|texts|评分组件分数与文案的对应关系|Record&#60;number, string&#62;|-|
|tooltips|评分组件分数的提示文案|Record&#60;number, string&#62;|-|
|disabled|禁用评分组件|boolean|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|update:modelValue|评分变化的回调事件|(value: number) => void|-|
|click|鼠标单击的回调事件|(e: MouseEvent) => void|-|
|mousedown|鼠标按下的回调事件|(e: MouseEvent) => void|-|
|mouseenter|鼠标进入的回调事件|(e: MouseEvent) => void|-|
|mouseleave|鼠标离开的回调事件|(e: MouseEvent) => void|-|
|mouseup|鼠标抬起的回调事件|(e: MouseEvent) => void|-|

### 插槽 |插槽|说明|
| :- | :- |
|character|通过自定义内容替换评分图标|