

##    单选

```vue
<template>
  <SegmentControl v-model="value" :options="options" />
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  import { SegmentControl } from '@xhs/delight'

  const value = ref('a')
  
  const options = [
    { label: '树状图', value: 'a' },
    { label: '折线图', value: 'b' },
    { label: '柱状图', value: 'c' },
  ]
</script>
```

##    多选

```vue
<template>
  <SegmentControl v-model="value" :options="options" multiple />
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  import { SegmentControl } from '@xhs/delight'

  const value = ref(['a', 'b'])
  
  const options = [
    { label: '树状图', value: 'a' },
    { label: '折线图', value: 'b' },
    { label: '柱状图', value: 'c' },
  ]
</script>
```

##    风格

`muted = true` 表示选中状态为白色。默认是主题色

```vue
<template>
  <SegmentControl v-model="value" :options="options" multiple muted />
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  import { SegmentControl } from '@xhs/delight'

  const value = ref(['a', 'b'])
  
  const options = [
    { label: '树状图', value: 'a' },
    { label: '折线图', value: 'b' },
    { label: '柱状图', value: 'c' },
  ]
</script>
```

##    可取消 cancelable

`cancelable` 只针对单选的情况下，默认是必选中一项，`cancelable = true` 表示可以取消选中

```vue
<template>
  <SegmentControl v-model="value" :options="options" cancelable />
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  import { SegmentControl } from '@xhs/delight'

  const value = ref('a')
  
  const options = [
    { label: '树状图', value: 'a' },
    { label: '折线图', value: 'b' },
    { label: '柱状图', value: 'c' },
  ]
</script>
```

##    集成 ICON

```vue
<template>
  <SegmentControl v-model="value1" :options="options1" />
  <br />
  <br />
  <SegmentControl v-model="value2" :options="options2" />
  <br />
  <br />
  <SegmentControl v-model="value3" :options="options3" />
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  import { SegmentControl } from '@xhs/delight'
  import { AlignTextLeft, AlignTextCenter, AlignTextRight } from '@xhs/delight/icons'

  const value1 = ref('a')
  const value2 = ref('a')
  const value3 = ref('a')
  
  const options1 = [
    { label: '树状图', value: 'a', icon: AlignTextLeft },
    { label: '折线图', value: 'b', icon: AlignTextCenter },
    { label: '柱状图', value: 'c', icon: AlignTextRight },
  ]

  const options2 = [
    { value: 'a', icon: AlignTextLeft },
    { value: 'b', icon: AlignTextCenter },
    { value: 'c', icon: AlignTextRight },
  ]

  const options3 = [
    { label: '树状图', value: 'a', icon: AlignTextLeft, iconPosition: 'right' },
    { label: '折线图', value: 'b', icon: AlignTextCenter, iconPosition: 'right' },
    { label: '柱状图', value: 'c', icon: AlignTextRight, iconPosition: 'right' },
  ]
</script>
```

##    自定义 ICON

icon 属性可以接受一个 VNode、JSX.Element

```vue
<template>
  <SegmentControl v-model="value" :options="options" />
</template>
<script setup lang="ts">
  import { ref, h } from 'vue'
  import { SegmentControl, Icon } from '@xhs/delight'
  import { AlignTextLeft, AlignTextCenter, AlignTextRight } from '@xhs/delight/icons'

  const value = ref('a')
  
  const options = [
    { 
      label: '树状图', 
      value: 'a', 
      icon: h(Icon, { icon: AlignTextLeft })
    },
    { 
      label: '折线图', 
      value: 'b',
      icon: (
        <Icon color="danger">
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 27 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M13.3333 26.6667C20.6971 26.6667 26.6667 20.6972 26.6667 13.3334C26.6667 5.96962 20.6971 7.82013e-05 13.3333 7.82013e-05C5.96954 7.82013e-05 0 5.96962 0 13.3334C0 20.6972 5.96954 26.6667 13.3333 26.6667ZM13.4136 5.71428C14.1518 5.71428 14.7502 6.31273 14.7502 7.05095V15.071C14.7502 15.8092 14.1518 16.4077 13.4136 16.4077C12.6753 16.4077 12.0769 15.8092 12.0769 15.071V7.05095C12.0769 6.31273 12.6753 5.71428 13.4136 5.71428ZM11.8089 19.3484C11.8089 20.2343 12.5271 20.9524 13.4129 20.9524C14.2988 20.9524 15.0169 20.2343 15.0169 19.3484C15.0169 18.4626 14.2988 17.7444 13.4129 17.7444C12.5271 17.7444 11.8089 18.4626 11.8089 19.3484Z"
              fill="currentColor"
            />
          </svg>
        </Icon>
      )
    },
    { 
      label: '柱状图', 
      value: 'c',
      icon: (
        <span style="width: 16px; height: 16px; color: #f06800;">
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 30 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 21.6616C0 23.6895 1.76424 25.3333 3.94053 25.3333H25.3921C26.0618 25.3333 26.7204 25.1743 27.3058 24.8713C29.2082 23.8865 29.8936 21.6511 28.8367 19.8785L18.111 1.88923C17.7536 1.28986 17.2233 0.795708 16.58 0.462725C14.6776 -0.522073 12.2786 0.116597 11.2217 1.88923L0.495887 19.8785C0.170666 20.424 0 21.0377 0 21.6616Z"
              fill="currentColor"
            />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M16.1277 7.99902C16.1271 7.26261 15.501 6.66614 14.7294 6.66675C13.9578 6.66736 13.3329 7.26483 13.3335 8.00123L13.3393 16.0011C13.3399 16.7375 13.966 17.334 14.7376 17.3334C15.5092 17.3328 16.1341 16.7353 16.1335 15.9989L16.1277 7.99902ZM16.1277 20.6667C16.1277 19.9303 15.5022 19.3333 14.7306 19.3333C13.959 19.3333 13.3335 19.9303 13.3335 20.6667V21.3334C13.3335 22.0698 13.959 22.6667 14.7306 22.6667C15.5022 22.6667 16.1277 22.0698 16.1277 21.3334V20.6667Z"
              fill="white"
            />
          </svg>
        </span> 
      )
    },
  ]
</script>
```

##    API 参考

```
type ValueType = number | string | boolean

interface Option { 
  lable?: string | VNode | JSX.Element
  value: ValueType
  icon?: ((e: IconProps) => string) | VNode | JSX.Element  // 图标的内容，@xhs/delight/icons 默认提供了一套基于 IconPark 的 svg 图标
  iconPosition?: 'left' | 'right'   // 当未写明该属性时，图标默认放在 label 的左侧
  disabled?: boolean
  [props: string]: any  
}

```

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|modelValue (v-model)|选中的值|ValueType &#124; ValueType[]|-|
|options|SegmentControl组件中的选项|Option[]|-|
|multiple|是否可以多选|boolean|false|
|muted|默认是主题色，muted 表示选中为白色|boolean|false|

### 事件 |事件|说明|类型|默认值|
| :- | :- | :- | :- |
|change|变化时回调函数|(v) => void||-|
