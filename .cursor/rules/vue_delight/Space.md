

##    标准间距大小

通过 `size` 传入 `'small'` &#124; `'default'` &#124; `'large'` 调整组件间标准间距的大小：

```vue
<template>
  <Space size="small">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <br/>
  <Space>
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <br/>
  <Space size="large">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button } from '@xhs/delight'
</script>
```

##    自定义间距大小

通过 `size` 传入 `string` &#124; `number` 调整组件间自定义间距的大小：

```vue
<template>
  <Space size="4px">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <br/>
  <Space size="var(--size-space-step-default)">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <br/>
  <Space :size="4">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button } from '@xhs/delight'
</script>
```

##    方向

通过 `direction` 调整间距的方向：

```vue
<template>
  <Space direction="vertical">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button } from '@xhs/delight'
</script>
```

##    块级元素

通过 `block` 设置为块级元素：

```vue
<template>
  <Space justify="end" block>
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button } from '@xhs/delight'
</script>
```

##    对齐方式

通过 `justify`、`align` 调整组件主轴、交叉轴的对齐方式，默认值分别为 'start' 和 'center'。当 `direction` 为 'horizontal' 时，主轴为水平方向，交叉轴为垂直方向；当 `direction` 为 'vertical' 时，主轴为垂直方向，交叉轴为水平方向：

```vue
<template>
  <p>主轴对齐方式:</p>
  <Space block>
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <Space block justify="center">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <Space justify="end" block>
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <p>交叉轴对齐方式:</p>
  <Space align="start" block>
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <Space block >
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
  <br/>
  <Space align="end" block>
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button } from '@xhs/delight'
</script>
```

##    换行

通过 `wrap` 设置允许换行：

```vue
<template>
  <Space wrap :style="{ width: '200px' }">
    <Button size="large">large</Button>
    <Button>default</Button>
    <Button size="small">small</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button } from '@xhs/delight'
</script>
```

##    API 参考

通过设置 Space 的属性来产生不同的间距样式：

|属性|说明|类型|默认值|
| :- | :- | :- | :- |
|size|间距的大小|'small' &#124; 'default' &#124; 'large' &#124; string &#124; number|'default'|
|direction|间距的方向|'horizontal' &#124; 'vertical'|'horizontal'|
|justify|组件主轴的对齐方式|'start' &#124; 'center' &#124; 'end' &#124; 'space-around' &#124; 'space-between' &#124; 'space-evenly'|-|
|align|组件交叉轴的对齐方式|'start' &#124; 'center' &#124; 'end' &#124; 'unset'|'center'|
|wrap|是否允许换行|boolean|false|
|block|是否为块级元素|boolean|false|

### 插槽 |插槽|说明|
| :- | :- |
|default|需要控制间距的组件|