

##    基本用法

```vue
<template>
  <Button @click="open">open</Button>
</template>

<script setup>
  import { Button, toast2 as toast } from '@xhs/delight'

  const open = () => {
    toast.info('This is a normal message')
  }
</script>
```

##    持续时间

通过 `duration` 设置持续时间：
- `duration` 为 `0` 时不会消失，默认 `8000` ms，推荐8000ms或者5000ms。

```vue
<template>
  <Space>
    <Button @click="() => notify(9000)">9000</Button>
    <Button @click="() => notify(0)">0</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button, toast2 as toast } from '@xhs/delight'

  function notify(duration) {
    toast.info({
      content: duration > 0 ? duration + ' ms 后主动消失' : '不会主动消失',
      duration,
      closeable: true
    })
  }
</script>
```

##    提示类型

```vue
<template>
  <Space>
    <Button @click="() => notify('success')">success</Button>
    <Button @click="() => notify('info')">info</Button>
    <Button @click="() => notify('warning')">warning</Button>
    <Button @click="() => notify('danger')">danger</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button, toast2 as toast } from '@xhs/delight'

  function notify(type) {
    toast[type]('This is a ' + type + ' toast')
  }
</script>
```

##    加强提示类型

通过 `strong` 设置加强提示类型：

```vue
<template>
  <Space>
    <Button @click="success">success</Button>
    <Button @click="info">info</Button>
    <Button @click="warning">warning</Button>
    <Button @click="danger">danger</Button>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Button, toast2 as toast } from '@xhs/delight'

  function success() {
    toast.success({
      content: '这是一段描述信息。',
      strong: true
    })
  }

  function danger() {
    toast.danger({
      content: '这是一段描述信息。',
      strong: true
    })
  }

  function warning() {
    toast.warning({
      content: '这是一段描述信息。',
      strong: true
    })
  }

  function info() {
    toast.info({
      content: '这是一段描述信息。',
      strong: true
    })
  }
</script>
```

##    自定义内容

通过给 `content` 传入 `JSX.Element` 自定义描述：

```vue
<template>
  <Space>
    <Button @click="notify">custom content</Button>
  </Space>
</template>

<script setup lang="ts">
  import { h } from 'vue'
  import { Space, Button, Text, toast2 as toast } from '@xhs/delight'
  import { Fire } from '@xhs/delight/icons'

  function notify() {
    toast.open(
      {
        content: h(
          Text,
          {
            icon: {
              icon: Fire,
              theme: 'filled',
              color: 'red-6'
            },
            size: 'large',
            color: 'text-title',
          },
          () => '这是一段描述信息。'
        )
      }
    )
  }
</script>
```

##    默认关闭

通过 `closeable` 设置开启默认关闭：

```vue
<template>
  <Button @click="notify">closeable</Button>
</template>

<script setup lang="ts">
  import { Space, Button, toast2 as toast } from '@xhs/delight'

  function notify() {
    toast.danger({
      content: '点击关闭按钮即可关闭。',
      closeable: true,
    })
  }
</script>
```

##    主动关闭

`toast2.close(removeKey)`

```vue
<template>
  <Button @click="notify">close</Button>
</template>

<script setup>
  import { Button, toast2 as toast } from '@xhs/delight'

  function notify() {
    const key = 'close-key'

    toast.danger({
      key,
      content: '可以主动关闭的 toast。',
      duration: 0,
    })

    setTimeout(() => {
      toast.close(key)
    }, 1000)
  }
</script>
```

##    更新消息内容

可以通过唯一的 key 来更新内容

```vue
<template>
  <Button @click="notify">Open the message box (update by key)</Button>
</template>

<script setup>
  import { Button, toast2 as toast } from '@xhs/delight'

  function notify() {
    const key = 'update-key'

    toast.danger({
      key,
      content: '更新通过唯一的 key',
    })

    setTimeout(() => {
      toast.success({
        key,
        content: 'NEW 更新通过唯一的 key',
      })
    }, 1000)
  }
</script>
```

##    自定义样式

```vue
<template>
  <Button @click="notify">custome style</Button>
</template>

<script setup>
  import { Button, toast2 as toast } from '@xhs/delight'

  function notify() {
    toast.danger({
      content: 'custom style',
      class: 'my-custom-toast',
      style: { background: 'pink' },
    })
  }
</script>

<style>
  .my-custom-toast .d-toast-content{
    color: blue;
  }
</style>
```

##    操作

```vue
<template>
  <Button @click="notify">actions</Button>
</template>

<script setup lang="ts">
  import { Space, Button, toast2 as toast } from '@xhs/delight'

  function notify() {
    const key = '1'
    toast.danger({
      key,
      content: '点击重试再试一次。',
      actions: () => [
        {
          name: '重试',
          onClick: () => { console.log('重试咯...') },
        },
        {
          name: '关闭',
          onClick: () => { toast.close(key) },
        },
      ],
      duration: 0,
    })
  }
</script>
```

##    自定义操作

通过给 `actions` 传入 `JSX.Element` 自定义操作：

```vue
<template>
  <Button @click="notify">actions</Button>
</template>

<script setup lang="ts">
  import { h } from 'vue'
  import { Button, Text, toast2 as toast } from '@xhs/delight'

  function notify() {
    const key = 'a'
  
    toast.danger({
      key,
      content: '点击关闭手动消失。',
      actions: () => [
        h(
          Text,
          {
            style: { color: 'var(--color-danger)', marginLeft: 'auto', },
            size: 'small',
            onClick: () => { toast.close(key) },
          },
          () => '关闭'
        )
      ],
      duration: 0,
    })
  }
</script>
```

##    API 参考

组件提供了一些静态方法，使用方式和参数如下：

- toast2.success(string | options)
- toast2.warning(string | options)
- toast2.danger(string | options)
- toast2.info(string | options)

#### options 对象属性参考 |属性|说明|类型|默认值|
| :- | :- | :- | :- |
|type|提示类型|'info' &#124; 'success' &#124; 'warning' &#124; 'danger'|-|
|content|提示描述|string &#124; JSX.Element|-|
|duration|提示持续时间，单位 ms|number|8000|
|closeable|是否展示默认关闭按钮|boolean|false|
|strong|是否加强提示类型|boolean|false|
|style|自定义内联样式|`Record`|-|
|class|自定义 CSS class|string|-|
|key|当前通知唯一标志|string|-|
|onClose|点击默认关闭按钮时触发的回调函数|() => void|-|
|onClick|点击 toast 触发的回调函数|() => void|-|
|getContainer|配置渲染节点的输出位置|() => HTMLElement|() => document.body|
|actions|通知额外操作|(JSX.Element &#124; VNode)[]  &#124; () => (name: string; onClick: (e: MouseEvent) => void)[]|-|

#### 全局方法 还提供了全局配置和全局销毁方法：

- toast2.config(options)
- toast2.close(key: String)
- toast2.destroy()
