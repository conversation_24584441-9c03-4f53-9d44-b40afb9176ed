import { Random } from 'mockjs'
import { defineMock } from '../_utils'

export default defineMock([
  {
    url: '/api/demo/console',
    timeout: 1000,
    method: 'get',
    response: () => ({
      // 访问量
      visits: {
        dayVisits: Random.float(10000, 99999, 2, 2),
        rise: Random.float(10, 99),
        decline: Random.float(10, 99),
        amount: Random.float(99999, 999999, 3, 5),
      },
    }),
  },
  {
    url: '/api/demo/path',
    method: 'post',
    filePath: '../data/list.json',
  },
  {
    url: '/api/demo/list',
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10 } = query
      return {
        page: Number(page),
        pageSize: Number(pageSize),
        pageCount: 60,
        [`list|${pageSize}`]: [{
          id: '@integer(10,100)',
          name: '@cname()',
          explain: '@cname()',
          isDefault: '@boolean()',
          create_date: '@date(\'yyyy-MM-dd hh:mm:ss\')',
          'status|1': ['normal', 'enable', 'disable'],
        }],
      }
    },
  },
  {
    url: '/api/project/:id/analysis/tab',
    timeout: 100,
    method: 'get',
    disable: true, // 禁用接口
    response: {
      id: 11892,
      projectId: 3,
      userEmail: '<EMAIL>',
      params: '[{"analysisId":885404,"name":"临时分析3","shortcutId":"","isSaved":true,"active":true,"datasetId":-1},{"analysisId":916544,"name":"临时","shortcutId":"","isSaved":true,"active":true,"datasetId":-1},{"analysisId":9226,"name":"临时分析6","shortcutId":"","isSaved":true,"active":true,"datasetId":-1},{"analysisId":1158341,"name":"表格子查询","shortcutId":"","isSaved":true,"active":true,"datasetId":-1},{"analysisId":29135,"name":"堆叠组合图","shortcutId":"","isSaved":true,"active":true,"datasetId":-1}]',
      deleted: 0,
      createdOn: 1734520762967,
      createdBy: '<EMAIL>',
      changedOn: 1741006298913,
      changedBy: '<EMAIL>',
    },
  },
  // {
  //   url: '/api/dataAgent/message/progress/info',
  //   method: 'get',
  //   filePath: '../data/progress.json',
  // },
  // {
  //   url: '/api/dataAgent/searchData',
  //   method: 'post',
  //   filePath: '../data/searchData.json',
  // },
])
