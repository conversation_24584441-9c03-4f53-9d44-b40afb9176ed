import Mock from 'mockjs'
import path from 'node:path'
import fs from 'node:fs'
import { MockMethod } from 'vite-plugin-mock'

type XMockMethod = MockMethod & {
  /** 禁用接口 */
  disable?: boolean
  /** 文件路径, 需相对于/mock */
  filePath?: string
}

/**
 * 定义 mock 数据接口, 在原有的基础上，增加了一层封装
 *
 * @export
 * @param {(XMockMethod|XMockMethod[])} data
 * @return {*}  {XMockMethod[]}
 */
export function defineMock(data: XMockMethod|XMockMethod[]):XMockMethod[] {
  let result = !Array.isArray(data) ? [data] : data

  // 过滤禁用的接口
  result = result.filter((item:XMockMethod) => !item.disable)
  // 转换响应数据
  result.forEach((item:XMockMethod) => {
    const { statusCode = 200 } = item
    if (item.response || item.filePath) {
      let response = item.response
      item.response = function _res(...args) {
        // 开发环境下，设置响应头 mock
        if (this) {
          this.res.setHeader('Mock', 'mock')
        } else {
          // 生产环境下，没有 this, 通过console.warn提醒
          console.warn('mock: ', item.url)
        }

        // 如果存在文件路径，则读取文件内容作为响应数据
        if (item.filePath) {
          const filePath = getPath(item.filePath)
          let fileStr

          try {
            fileStr = fs.readFileSync(filePath, 'utf-8')
            fileStr = JSON.parse(fileStr)
          } catch (e) {
            console.error('mock 文件解析错误: ', filePath)
          }
          response = fileStr
        }
        if (typeof response === 'function') {
          return transformResponse(response.apply(this, args), statusCode)
        }
        return transformResponse(response, statusCode)
      }
    }

    // 非 JSON 请求
    if (item.rawResponse) {
      const rawResponse = item.rawResponse
      item.rawResponse = function _res(...args) {
        // 开发环境下，设置响应头 mock
        if (this) {
          this.res.setHeader('Mock', 'mock')
        } else {
          // 生产环境下，没有 this, 通过console.warn提醒
          console.warn('mock: ', item.url)
        }
        if (typeof rawResponse === 'function') {
          return rawResponse.apply(this, args)
        }
        return rawResponse
      }
    }
  })
  return result
}

/**
 * 转换响应数据
 *
 * @param {unknown} data
 * @param {number} statusCode
 * @return {*}  {Mock.MockjsMock}
 */
function transformResponse(data: unknown, statusCode: number):Mock.MockjsMock {
  if (statusCode === 200) {
    // 成功
    return Mock.mock({
      code: 0,
      success: true,
      errorMsg: null,
      techErrorMsg: null,
      data,
      traceId: 'mock',
      errorData: null,
      mock: true,
    })
  }
  // 异常请求
  return Mock.mock({
    code: statusCode,
    success: false,
    errorMsg: 'mock 请求异常',
    techErrorMsg: 'mock 请求异常',
    data: null,
    traceId: 'mock',
    errorData: null,
    mock: true,
  })
}

/**
 * 获取文件路径
 *
 * @param {string} filePath
 * @return {*}  {string}
 */
function getPath(filePath:string):string {
  let base = path.resolve(__dirname, '../')
  if (filePath[0] === '/') {
    filePath = filePath.slice(1)
  } else {
    const err = new Error()
    Error.captureStackTrace(err, getPath) // 排除当前函数的堆栈
    const stackLines = err.stack?.split('\n') || []

    if (stackLines.length > 1) {
      const callerLine = stackLines[1].trim()

      // 匹配括号内的路径（如 "at Object.<anonymous> (/pathB/b.js:3:1)"）
      const match = callerLine.match(/\(file:\/\/(.+)\/.+:\d+:\d+\)/)
      if (match) {
        base = match[1]
      } else {
      // 匹配无括号的路径（如 "at /pathB/b.js:3:1"）
        const match2 = callerLine.match(/at file:\/\/(.+)\/.+:\d+:\d+$/)
        base = match2 ? match2[1] : './'
      }
    }
  }

  return path.resolve(base, filePath)
}
