<template>
  <div
    :class="[
      modeType === 'aside' ? 'data-agent-container-content-aside' : 'data-agent-container-content',
      { 'show-detail': showDetail }
    ]"
  >
    <div
      class="agent-layout"
      :class="{ 'split-view': showDetail }"
    >
      <!-- 主内容区域 -->
      <div
        class="main-content"
        :class="{ 'half-width': showDetail }"
      >
        <Space
          block
          :class="[modeType === 'aside' ? 'data-agent-main-aside' : 'data-agent-main']"
          direction="vertical"
        >
          <Transition name="fade">
            <div
              v-if="isAgentHome"
              class="agent-title-container"
            >
              <div
                class="agent-title"
                style="box-sizing: border-box;"
                :style="{ padding: modeType === 'aside' ? '0' : '0 calc(50% - 450px)' }"
              >
                <template v-if="modeType === 'single'">
                  <IconSvg
                    width="32"
                    height="32"
                    style="width: 32px; height: 32px; vertical-align: text-bottom;"
                  />
                  <Text>Hi {{ agentStore.userInfo.displayName.split('(')[0] }}，我是你的 AI数据助手</Text>
                </template>
                <template v-else>
                  <IconSvg
                    width="32"
                    height="32"
                    style="width: 32px; height: 32px; vertical-align: text-bottom;"
                  />
                  <Text>你好，我是Data Agent</Text>
                </template>
              </div>
              <Space
                v-if="modeType === 'single'"
                block
                :size="10"
                style="width: 100%; box-sizing: border-box; padding: 0 calc(50% - 450px)"
              >
                <template
                  v-for="item in AgentTypeBtn"
                  :key="item.type"
                >
                  <!-- <div
                v-if="item.type === AgentModeAiType.search && !isBeta"
                :style="{position: 'relative' }"
              >
                <div class="coming-soon">敬请期待</div>
                <Button
                  :key="item.type"
                  :type="active === item.type ? 'primary' : 'default'"
                  :icon="item.icon"
                  disabled
                  style="border-radius: 8px; padding: 0 12px;"
                  @click="handleAiType(item)"
                >{{ item.label }}</Button>
              </div> -->
                  <Tooltip
                    :content="item.tooltip"
                  >
                    <Button
                      :key="item.type"
                      :type="active === item.type ? 'primary' : 'default'"
                      :icon="item.icon"
                      style="border-radius: 8px; padding: 0 12px; height: 36px; position: relative;"
                      @click="handleAiType(item)"
                    >{{ item.label }}
                      <Badge
                        v-if="item.type === AgentModeAiType.analyze"
                        content="升级"
                        style="position: absolute; top: -8px; right: -8px;"
                      />
                    </Button>
                  </Tooltip>
                </template>
              </Space>
            </div>
          </Transition>
          <!-- 放置消息组件 -->
          <Transition name="fade-msg">
            <div
              v-if="!isAgentHome"
              class="session-container-page"
            >
              <SessionContainer
                @get-question="getQuestion"
                @show-detail="handleShowDetail"
              />
            </div>
          </Transition>
          <!-- 侧边栏新会话按钮 -->
          <Space
            v-if="modeType === 'aside' && !isAgentHome"
            justify="end"
            block
            style="width: 100%;"
          >
            <Tooltip content="新会话">
              <Icon
                :icon="AddOne"
                style="cursor: pointer;"
                @click="handleNewSession"
              />
            </Tooltip>
          </Space>
          <!-- 问题输入区域 -->
          <div
            v-if="showEditor"
            class="editor-container"
            style="width: 100%; box-sizing: border-box;"
            :style="{ padding: modeType === 'aside' ? '0' : '0 calc(50% - 450px)' }"
          >
            <Editor
              ref="editorRef"
              :initial-value="dataOrSearchDesc"
              :is-simple-mode="isSimpleMode"
              :analysis-data="analysisData"
              :analysis-agent-code="analysisAgentCode"
              @stop="handleStop"
              @start="handleStart"
              @reset-editor-input="dataOrSearchDesc = ''"
              @dataset-type-change="handleDatasetTypeChange"
              @analysis-agent-change="handleAnalysisAgentChange"
            />
          </div>
          <!-- 推荐问题 -->
          <Transition name="fade">
            <div
              v-if="isAgentHome"
              class="recommend-list-container"
            >

              <template v-if="modeType === 'single'">
                <div
                  v-if="active !== AgentModeAiType.analyze"
                  v-loading="isDataOrSearchLoading"
                  class="recommend-list"
                  style="box-sizing: border-box; padding: 0 calc(50% - 450px)"
                >
                  <Text
                    v-if="listData[activeKey]?.length > 0 && active !== AgentModeAiType.search"
                    class="data-title"
                  >根据你的浏览记录及个人身份 <span>{{ department }}</span> 您可以提问：
                  </Text>
                  <div
                    :class="['recommend-list-card']"
                  >
                    <template v-if="listData[activeKey]?.length > 0">
                      <ul
                        v-for="(child, index) in listData[activeKey]"
                        :key="index"
                        class="card-item"
                      >
                        <li :style="{ width: '100%' }">{{ child?.category || '' }}</li>
                        <li
                          v-for="(item, ind) in child?.questions || []"
                          :key="ind"
                          @click="handleClickRecommend(item)"
                          @mouseenter="hoverItem = item"
                          @mouseleave="hoverItem = null"
                        >
                          <Text
                            tooltip
                            ellipsis
                            class="recommend-list-item-text"
                          >{{ item?.display || '' }}</Text>
                          <Icon
                            :icon="ArrowRight"
                            size="small"
                            theme="filled"
                            :fill="hoverItem === item ? 'var(--color-primary)' : 'var(--color-grey-5)'"
                          />
                        </li>
                      </ul>
                    </template>
                    <!-- <template v-else>
                  <Result
                    status="resultless"
                    title="暂无内容"
                    style="margin: 0 auto;"
                  />
                </template> -->
                  </div>
                </div>
                <!-- 分析类型 -->
                <div
                  v-else
                  v-loading="isLoading || analysisLoading"
                  class="recommend-list recommend-list-ai-analyze"
                  style="box-sizing: border-box; padding: 0 calc(50% - 450px)"
                >
                  <ul>
                    <li
                      v-for="(item, ind) in analysisRecommendList || []"
                      :key="ind"
                      @click="handleClickAnalysisNewRecommend(item)"
                      @mouseenter="hoverItem = item"
                      @mouseleave="hoverItem = null"
                    >
                      <Text
                        tooltip
                        ellipsis
                        class="recommend-list-item-text"
                      >{{ item?.display || '' }}</Text>
                      <Icon
                        :icon="ArrowRight"
                        size="small"
                        theme="filled"
                        :fill="hoverItem === item ? 'var(--color-primary)' : 'var(--color-grey-5)'"
                      />
                    </li>
                  </ul>
                  <!-- <Tabs
                v-model="activeAnalysisTab"
                size="small"
              >
                <TabPane
                  v-for="analysisTab in analysisRecommendTabs"
                  :id="analysisTab.key"
                  :key="analysisTab.key"
                  :label="analysisTab.label"
                />
              </Tabs> -->
                  <!-- <div
                class="recommend-list-analyze"
              >
                <Space
                  align="start"
                  class="recommend-list-analyze-card"
                >
                  <Card
                    v-for="(item, ind) in analysisList"
                    :key="ind"
                    @click="handleClickAnalysisRecommend(item)"
                  >
                    <template #title>
                      <div class="analyze-card-title">
                        <Text class="analyze-card-title-left">
                          <Text class="analyze-card-title-name">{{ item.name }}</Text>
                        </Text>
                      </div>
                    </template>
                    <Tooltip :content="item.desc">
                      <div class="card-content">{{ item.desc }}</div>
                    </Tooltip>
                  </Card>
                </Space>
              </div> -->
                </div>
              </template>
              <template v-else>
                <div
                  v-if="listData[RecommendQuestionsDataType.data]?.length > 0"
                  class="recommend-list"
                >
                  <Text class="data-title">根据你的浏览记录及个人身份 <span>{{ department }}</span> 您可以提问：</Text>
                </div>
                <Space
                  block
                  wrap
                  :size="10"
                  style="width: 100%;"
                >
                  <Button
                    v-for="category in listData[RecommendQuestionsDataType.data]?.map((item: any) => item.category)"
                    :key="category"
                    :type="activeCategory === category ? 'secondary' : 'default'"
                    :style="{ background: activeCategory === category ? '#ECF0FF' : 'var(--Fill-default, #3737580D)' }"
                    style="padding: '0 12px'"
                    @click="handleAiType(category)"
                  >{{ category }}</Button>
                </Space>
                <ul class="recommend-list-aside">
                  <li
                    v-for="(item, ind) in listData[RecommendQuestionsDataType.data]?.find((item: any) => item.category === activeCategory)?.questions || []"
                    :key="ind"
                    @click="handleClickRecommend(item)"
                    @mouseenter="hoverItem = item"
                    @mouseleave="hoverItem = null"
                  >
                    <Text
                      tooltip
                      ellipsis
                      class="recommend-list-item-text"
                    >{{ item?.display || '' }}</Text>
                    <Icon
                      :icon="ArrowRight"
                      size="small"
                      theme="filled"
                      :fill="hoverItem === item ? 'var(--color-primary)' : 'var(--color-grey-5)'"
                    />
                  </li>
                </ul>
              </template>
            </div>
          </Transition>
        </Space>
      </div>

      <!-- 拖拽控制：主内容与详情面板之间 -->
      <DragController
        v-if="showDetail"
        :key="detailPanelKey"
        :min-width="minWidth"
        :max-width="maxWidth"
        :init-left-distance="halfWidth"
        height="100%"
        :need-margin="false"
        align="right"
        left-selector=".main-content"
        right-selector=".detail-panel"
      />

      <!-- 详情面板区域 -->
      <div
        v-if="showDetail"
        :key="detailPanelKey"
        class="detail-panel"
        :style="{ width: `${halfWidth}px` }"
      >
        <DataTagDetail
          :current-tool="currentTool"
          @close="handleDetailClose"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import {
    computed, nextTick, onMounted, onUnmounted, ref, watch,
    provide,
  } from 'vue'
  import { useRoute } from 'vue-router'
  import {
    Space, Text, Button, Icon, Tooltip,
    toast2, Badge,
    // TabPane,  Card, Tabs,
    // Result,
  } from '@xhs/delight'
  import {
    Search,
    ChartLine, Tips, ArrowRight, AddOne,
  } from '@xhs/delight/icons'
  import { useAgentStore } from '@agent/store/agentStore'
  import {
    AgentModeAiType, HistoryTypeMap, RecommendQuestionsDataType,
    SessionContext,
  } from '@agent/types'
  import { Editor } from '@agent/components/editor'
  import { fetchAnalysisApolloConfig, fetchInitRecommendList } from '@agent/services/basicInfo'
  import { SessionContainer } from '@agent/components/sessionContainer'
  import { addListener, removeListener } from '@xhs/redbi-share-utils'
  import { isEmpty } from 'lodash'
  import { useDataAgentTrackerStore } from '@analysis/stores/dataAgentTrackerStore'
  import { DragController } from '@xhs/redbi-share-components'
  import { useSessionStore } from '../../store/sessionStore'
  import IconSvg from '../iconSvg/index.vue'
  import { useAnalysisData } from '../message/analysisData/useAnalysisData'
  import { useAnalysisRecommend } from './useAnalysisRecommend'
  import DataTagDetail from '../message/analysisData/components/DataTagDetail.vue'

  // 详情面板与拖拽条强制重新挂载用的 key
  const detailPanelKey = ref(0)

  // import SearchSvg from '../iconSvg/SearchSvg.vue'
  // import AnalyzeSvg from '../iconSvg/AnalyzeSvg.vue'

  const DataAgentType = [
    {
      type: AgentModeAiType.data,
      key: RecommendQuestionsDataType.data,
      label: HistoryTypeMap[AgentModeAiType.data],
      icon: { icon: ChartLine },
      tooltip: '输入你的取数问题，AI帮你查询',
    },
    {
      type: AgentModeAiType.analyze,
      key: RecommendQuestionsDataType.analyze,
      label: HistoryTypeMap[AgentModeAiType.analyze],
      icon: { icon: Tips },
      tooltip: 'AI帮你分析数据、生成数据报告',
    },
    {
      type: AgentModeAiType.search,
      key: RecommendQuestionsDataType.search,
      label: HistoryTypeMap[AgentModeAiType.search],
      icon: { icon: Search },
      tooltip: 'AI搜索数据知识、数据资产（看板/数据集）',
    },
  ]
  const props = withDefaults(defineProps<{
    isScreen: boolean
    historyQuery: string
  }>(), {
    isScreen: false,
    historyQuery: '',
  })
  const hoverItem = ref<any>()
  const isDataOrSearchLoading = ref(false)
  // const isBeta = window.location.href.includes('beta')
  const AgentTypeBtn = computed(() => (props.isScreen ? DataAgentType.slice(0, 1) : DataAgentType))
  const sessionStore = useSessionStore()
  const agentStore = useAgentStore()
  const { handleTracker } = useDataAgentTrackerStore()
  const modeType = computed(() => agentStore.mode)
  const route = useRoute()
  const dataOrSearchDesc = ref('')
  // 获取浏览器宽度
  const fullWidth = computed(() => window.innerWidth)
  // 半屏宽度
  const halfWidth = computed(() => fullWidth.value / 2)
  const minWidth = computed(() => fullWidth.value * 0.1)
  const maxWidth = computed(() => fullWidth.value * 0.8)
  watch(() => props.historyQuery, newVal => {
    // sessionStore.clickRegenerateMessage(true)
    // 只有在没有URL查询参数且historyQuery有值时才清空输入
    if (!route.query.query && newVal) {
      dataOrSearchDesc.value = ''
    }
  }, { immediate: true })
  const emits = defineEmits(['handle-stop-message'])
  const active = computed(() => agentStore.aiType)
  const activeKey = ref(RecommendQuestionsDataType.data)
  const department = computed(() => `${agentStore.userInfo.departmentNames ? agentStore.userInfo.departmentNames[0] : ''}-${agentStore.userInfo.departmentName}`)
  const activeCategory = ref<any>()
  const listData = ref<any>({})
  const isAgentHome = computed(() => agentStore.isAgentHome)
  const analysisData = ref<any[]>([])
  const analysisAgentCode = computed(() => sessionStore.analysisAgentCode)

  // 详情面板状态管理
  const showDetail = ref(false)
  const currentTool = ref<any>(null)
  const isDetailPanelOpen = ref(false)

  const {
    analysisList, isLoading,
    // analysisRecommendTabs, activeAnalysisTab,
  } = useAnalysisRecommend()
  const analysisRecommendList = ref<any[]>([])
  const analysisLoading = ref(false)
  const editorRef = ref<any>()
  const sessionContainerRef = ref<any>()

  provide('editorOperation', {
    editorRef,
    // 添加上下文
    addContext: (context: SessionContext) => {
      editorRef.value.addContext(context, {
        // 发送埋点额外参数
        data_agent_skill_type: AgentModeAiType.dataQuestion,
        data_agent_data_question_obj: (context as any).context?.analysisUrl,
        data_agent_data_question_source: 'agent',
        data_agent_llm_model: editorRef.value.agentModel,
      })
      setTimeout(() => {
        sessionContainerRef.value?.scrollTo(28, true)
      }, 0)
    },
    scrollTo: (top: number, isAdd = false) => {
      sessionContainerRef.value?.scrollTo(top, isAdd)
    },
    // 修改placeholder
    updatePlaceholder: (text: string) => {
      editorRef.value.updatePlaceholder(text)
    },
    // 设置输入框值
    setEditorValue: (value: string) => {
      editorRef.value.setEditorValue(value)
    },
  })

  const {
    createAnalysisChat, reStartAnalysisChat, stopAnalysisChat, pollingProgressInfo, analysisProjectId,
  } = useAnalysisData()

  provide('AnalysisAttrs', { analysisProjectId })
  provide('DetailPanelOpen', isDetailPanelOpen)

  // 新会话
  const handleNewSession = () => {
    // 返回首页时关闭右侧详情面板
    handleDetailClose()
    agentStore.handleRouteQuery()
    if (active.value === AgentModeAiType.analyze) {
      reStartAnalysisChat()
      return
    }
    agentStore.setIsAgentHome(true)
    sessionStore.setCurrentSession({} as any)
    // 只有在没有URL查询参数时才清空输入
    if (!route.query.query) {
      dataOrSearchDesc.value = '' // 清空输入
    }
    emits('handle-stop-message')
  }

  // const currentSession = computed(() => sessionStore.currentSession)
  const showEditor = computed(() => {
    if (modeType.value === 'aside') {
      return true
    }
    // if (active.value !== AgentModeAiType.analyze) {
    return true
    // }

    // const sessionMsgs = currentSession.value?.messages
    // return Array.isArray(sessionMsgs) && sessionMsgs.length > 0
  })

  const isSimpleMode = computed(() => modeType.value === 'single' && !isAgentHome.value)

  const handleStop = () => {
    if (active.value === AgentModeAiType.analyze) {
      stopAnalysisChat()
    }
  }

  const handleStart = () => {
    if (active.value === AgentModeAiType.analyze) {
      pollingProgressInfo()
    }
  }

  const handleAnalysisAgentChange = (value: string) => {
    analysisRecommendList.value = analysisData.value?.find((item: any) => item.agentInfo.agentCode === value)?.agentInfo?.suggestionQuestion || []
  }
  // 获取分析配置
  const fetchAnalysisSetting = (params: Record<string, any>) => {
    analysisLoading.value = true
    if (!isAgentHome.value) {
      return
    }
    fetchAnalysisApolloConfig(params).then((res: any) => {
      analysisData.value = res
      if (res.length > 0) {
        if (sessionStore.analysisAgentCode) {
          const agent = res.find((item: any) => item.agentInfo.agentCode === sessionStore.analysisAgentCode)
          if (agent?.agentInfo?.agentId || agent?.agentInfo?.agentId === 0) {
            sessionStore.setAnalysisAgentId(agent?.agentInfo?.agentId)
            handleAnalysisAgentChange(sessionStore.analysisAgentCode)
            return
          }
        }
        sessionStore.setAnalysisAgentCode(res[0].agentInfo.agentCode)
        sessionStore.setAnalysisAgentId(res[0].agentInfo.agentId)
        handleAnalysisAgentChange(res[0].agentInfo.agentCode)
      } else {
        analysisRecommendList.value = []
      }
    }).catch((err: any) => {
      toast2.danger(err.data?.errorMsg || '获取分析信息失败')
    }).finally(() => {
      analysisLoading.value = false
    })
  }

  // 切换AI类型
  const handleAiType = (item: any) => {
    if (modeType.value === 'single') {
      // active.value = item.type
      sessionStore.setAnalysisAgentCode('')
      activeKey.value = item.key
      agentStore.setAiType(item.type)
      // 只有在手动切换时才清空输入，URL参数设置时不清空
      if (!route.query.query) {
        dataOrSearchDesc.value = '' // 清空输入
      }
    } else {
      activeKey.value = item
      activeCategory.value = item
      agentStore.setAiType(AgentModeAiType.data)
      // 只有在手动切换时才清空输入，URL参数设置时不清空
      if (!route.query.query) {
        dataOrSearchDesc.value = '' // 清空输入
      }
    }
  }
  watch(() => [active.value, agentStore.isAgentHome], newVal => {
    if (newVal[0] === AgentModeAiType.analyze && newVal[1]) {
      fetchAnalysisSetting({
        projectId: sessionStore.analysisDatesetType || Number(localStorage.getItem('agent_analysis_dataset_type')) || 1,
        agentType: 3,
        status: 2,
      })
    }
  })
  // const handleCollect = (item: { collect: boolean }) => {
  //   // 这里可以添加收藏逻辑
  //   item.collect = !item.collect
  //   console.warn('收藏状态:', item.collect)
  // }
  const handleClickRecommend = (item: { display: string; fill: string }) => {
    editorRef.value?.clearSlotValues?.()
    dataOrSearchDesc.value = item.fill ?? item.display
    if (item) {
      handleTracker('data_agent_suggest_question', {
        data_agent_skill_type: AgentModeAiType[active.value],
        data_agent_query: item.display,
      })
    }
  }
  // 新分析推荐
  const handleClickAnalysisNewRecommend = (item: any) => {
    editorRef.value?.clearSlotValues?.()
    dataOrSearchDesc.value = item.fill ?? item.display
    if (item) {
      handleTracker('data_agent_suggest_question', {
        data_agent_skill_type: AgentModeAiType[active.value],
        data_agent_query: item?.display,
      })
    }
  }
  const handleClickAnalysisRecommend = async (item: any, activeTab?: string) => {
    agentStore.setAiType(AgentModeAiType.analyze)
    // dataOrSearchDesc.value = item.fill ?? item.display
    // if (sessionStore.currentSession?.sessionId) return
    const initMsg = await createAnalysisChat(item, Number(activeTab || sessionStore.analysisDatesetType))
    dataOrSearchDesc.value = initMsg ?? ''
    agentStore.handleIsSessionHome({
      isSessionHome: 'true',
      dagId: item?.dagId,
      activeAnalysisTab: activeTab || sessionStore.analysisDatesetType,
    })
    if (item?.name) {
      handleTracker('data_agent_suggest_question', {
        data_agent_skill_type: AgentModeAiType[active.value],
        data_agent_query: item?.name,
      })
    }
  }

  // 监听当前会话状态，判断是否是新会话
  watch(() => sessionStore.currentSession, newVal => {
    agentStore.setIsAgentHome(!newVal?.sessionId)
  })
  // 监听AI类型变化 提交埋点
  watch(() => active.value, newVal => {
    if (newVal) {
      handleTracker('data_agent_skill_choose', {
        data_agent_skill_type: AgentModeAiType[newVal],
      })
    }
  }, { immediate: true })

  const patchRecommendList = (res: any) => {
    res['商业部'].SCENE_ANALYSIS = res['商业部'].QUERY_ANALYSIS
  }

  // 初始化推荐列表
  const recommendData = ref<any>({})
  const departmentList = ['商业部', '社区部', '交易部']
  const getInitRecommendList = () => {
    isDataOrSearchLoading.value = true
    fetchInitRecommendList().then((res: any) => {
      recommendData.value = res
      patchRecommendList(res)
      let depart = ''
      if (sessionStore.datesetType) {
        depart = departmentList[sessionStore.datesetType - 1]
      }
      listData.value = res[depart || (agentStore.userInfo.departmentNames ? agentStore.userInfo.departmentNames[0] : '')] || {}
      // 如果当前部门没有推荐列表，则使用商业部
      if (isEmpty(listData.value)) {
        listData.value = res['商业部']
        handleDatasetTypeChange(1)
      }
      nextTick(() => {
        if (modeType.value === 'aside') {
          activeCategory.value = listData.value[RecommendQuestionsDataType.data]?.map((item: any) => item.category)[0]
        }
      })
    }).catch((err: any) => {
      console.error('err', err)
    }).finally(() => {
      isDataOrSearchLoading.value = false
    })
  }
  // 切换数据集类型联动推荐列表
  const handleDatasetTypeChange = (value: number) => {
    if (active.value === AgentModeAiType.analyze) {
      sessionStore.setAnalysisAgentCode('')
      fetchAnalysisSetting({
        projectId: value,
        agentType: 3,
        status: 2,
      })
    }
    const depart = sessionStore.datesetType || value
    listData.value = recommendData.value[departmentList[depart - 1]] || {}
    if (isEmpty(listData.value)) {
      listData.value = recommendData.value['商业部']
    }
    if (modeType.value === 'aside') {
      activeCategory.value = listData.value[RecommendQuestionsDataType.data]?.map((item: any) => item.category)[0]
    }
  }
  // 新会话初始化推荐列表
  watch(() => isAgentHome.value, newVal => {
    if (newVal) {
      // 返回首页时关闭右侧详情面板
      handleDetailClose()
      getInitRecommendList()
      if (props.isScreen || modeType.value === 'aside') {
        // 只有在没有URL参数指定aiType时才设置默认值
        if (!route.query.aiType) {
          agentStore.setAiType(AgentModeAiType.data)
        }
      }
    }
  })
  // 消息体中推荐问题传值
  const getQuestion = (question: string, messageId: number = 0) => {
    if (messageId) {
      agentStore.setReclaimMessageId(messageId)
    }
    dataOrSearchDesc.value = question
  }

  // 处理详情面板显示
  const handleShowDetail = (tool: any) => {
    // 关闭
    if (tool === null) {
      showDetail.value = false
      currentTool.value = null
      isDetailPanelOpen.value = false
      return
    }
    // 打开
    currentTool.value = tool
    showDetail.value = true
    isDetailPanelOpen.value = true
    // 切换工具时重置拖拽初始位置：强制重新挂载
    detailPanelKey.value += 1
  }

  // 处理详情面板关闭
  const handleDetailClose = () => {
    showDetail.value = false
    currentTool.value = null
    isDetailPanelOpen.value = false
  }

  const uploadAnalysisAside = () => {
    agentStore.setMode('aside')
  }

  watch(() => route.query.aiType, (newVal: any) => {
    if (newVal) {
      agentStore.setAiType(Number(newVal))
      // active.value = Number(newVal)
    }
  })
  // watch(() => agentStore.aiType, (newVal: any) => {
  //   if (newVal) {
  //     active.value = Number(newVal)
  //   }
  // })

  // 处理URL传递的查询内容和自动提交
  const shouldAutoSubmit = ref(false)
  watch(() => [route.query.query, route.query.autoSubmit], ([queryText, autoSubmit]) => {
    if (queryText && typeof queryText === 'string') {
      // 使用nextTick确保在其他watch执行完之后再设置值
      nextTick(() => {
        dataOrSearchDesc.value = queryText

        // 如果需要自动提交且有内容，设置自动提交标志
        if (autoSubmit === 'true' && queryText.trim()) {
          shouldAutoSubmit.value = true
        }
      })
    }
  }, { immediate: true })

  // 根据url还原分析会话首页
  watch(() => [route.query.isSessionHome, route.query.dagId, route.query.activeAnalysisTab, analysisList.value], val => {
    // 如果有历史记录参数，优先恢复历史记录，不创建新会话
    if (route.query.historyQuerySessionId) return
    if (val[0] && val[1] && val[2] && val[0] === 'true' && val[3] && val[3]?.length > 0) {
      const analysisItem = analysisList.value.find((item: any) => item.dagId === Number(val[1]))
      if (analysisItem) {
        handleClickAnalysisRecommend(analysisItem, val[2] as string)
      }
    }
  }, {
    immediate: true,
    deep: true,
  })

  onMounted(() => {
    addListener('reStart-analysis-chat', reStartAnalysisChat)
    addListener('upload-analysis-aside', uploadAnalysisAside)
    addListener('data_agent_session_analysis_polling', pollingProgressInfo)

    // 只有在没有URL参数指定aiType时才设置默认值
    if (!route.query.aiType) {
      agentStore.setAiType(AgentModeAiType.data)
    }
    nextTick(() => {
      getInitRecommendList()
    })
  })

  onUnmounted(() => {
    removeListener('reStart-analysis-chat', reStartAnalysisChat)
    removeListener('upload-analysis-aside', uploadAnalysisAside)
    removeListener('data_agent_session_analysis_polling', pollingProgressInfo)
  })
</script>

<style lang="stylus" scoped>
.agent-layout
  width 100%
  height 100%
  display flex
  position relative

.main-content
  width 100%
  height 100%
  transition: width 0.3s ease
  &.half-width
    display: flex
    flex-direction: column
    flex: 1
    overflow-y auto
    .session-container
      padding 0 20px!important
    .editor-container
      padding 0 20px!important

.detail-panel
  // width 360px
  height 100%
  overflow: hidden
  border-left: 1px solid rgb(232, 232, 232)
  border-top: 1px solid rgb(232, 232, 232)
  border-radius: 8px 0 0 0

.data-agent-container-content
  width 100%
  height 100%
  overflow-y auto
  display flex
  justify-content center
  animation: showAnimation 0.2s ease-out
  .data-agent-main
    display flex
    width 100%
    height 100%
    padding-top 10px
    box-sizing border-box
    padding-bottom 10px
    .agent-title-container
      width 100%
      .agent-title
        width 100%
        display flex
        align-items center
        gap 12px
        margin-bottom 10px
        margin-top 70px
        :deep(.d-text)
          color var(--text-title, #000000D9)
          font-size 20px
          font-weight 500
    .session-container-page
      flex 1
      width 100%
      overflow-y auto
    .recommend-list-container
      width 100%
      .recommend-list
        width 100%
        margin-top 50px
        .data-title
          padding-left 12px
          margin-bottom 16px
          span
            color var(--text-title, #000000D9)
            font-weight 500
            padding-left 12px
        .recommend-list-card
          width 100%
          display grid
          align-items start
          gap 12px
          // 当项目数量小于等于4个时，均分列宽
          &:has(.card-item:nth-child(-n+1))
            grid-template-columns 1fr

          &:has(.card-item:nth-child(2):last-child)
            grid-template-columns repeat(2, 1fr)

          &:has(.card-item:nth-child(3):last-child)
            grid-template-columns repeat(3, 1fr)

          &:has(.card-item:nth-child(n+4))
            grid-template-columns repeat(4, 1fr)

          ul
            list-style none
            background var(--object-color-neutral-grey-0, #FAFAFA)
            padding 8px 12px 0 12px
            border-radius 8px
            margin 8px 0
            li
              list-style none
              display flex
              height 28px
              // max-width 268px
              font-size 12px
              align-items center
              justify-content space-between
              cursor pointer
              border-radius 4px
              // background-color var(--color-grey-2)
              margin-bottom 4px
              padding 0
              .recommend-list-item-text
                max-width 240px
                color var(--text-paragraph, #000000B2)
              &:hover
                .recommend-list-item-text
                  color var(--text-title, #000000D9)
            li:first-child
              color var(--text-title, #000000D9)
              font-weight 500
              line-height 28px
              font-size 12px
              background-color transparent
              padding 0
              margin 0
    .recommend-list-ai-analyze
      margin-top 10px!important
      min-height 100px
      ul
        list-style none
        padding 8px 0
        margin 60px 0 0 0
        border-radius 8px
        display flex
        flex-direction row
        flex-wrap wrap
        gap 12px
        li
          list-style none
          display flex
          font-size 12px
          align-items center
          background var(--fill-default, rgba(0, 0, 0, 0.03))
          justify-content space-between
          cursor pointer
          border-radius 4px
          // background-color var(--color-grey-2)
          margin-bottom 4px
          padding 8px 12px
          &:hover
            background var(--fill-default, rgba(0, 0, 0, 0.05))
          .recommend-list-item-text
            max-width 240px
            color var(--text-paragraph, #000000B2)
            margin-right 12px
          &:hover
            .recommend-list-item-text
              color var(--text-title, #000000D9)
    .recommend-list-analyze
      width 100%
      margin-top 12px
      .analyze-title
        color var(--text-title, #000000D9)
        font-weight 500
        line-height 44px
      .recommend-list-analyze-card
        flex-wrap wrap
        gap 12px
        justify-content flex-start
      :deep(.d-card)
        width 292px
        cursor pointer
        border-radius 8px
        &:hover
          // box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
          border 1px solid var(--color-primary)
        .d-card-header
          padding 10px 12px 5px
        .d-card-content
          border none
          padding 0px 12px 5px
          .card-content
            height calc(var(--size-text-line-height-small) * 2)
            flex-shrink 0
            align-self stretch
            overflow hidden
            color var(--color-text-description)
            text-overflow ellipsis
            display -webkit-box
            -webkit-line-clamp 2
            -webkit-box-orient vertical
            font-family "PingFang SC"
            font-size var(--size-text-small)
            font-style normal
            font-weight var(--size-text-font-weight-default)
            line-height var(--size-text-line-height-small)
        .analyze-card-title
          display flex
          justify-content space-between
          align-items center
          .analyze-card-title-left
            .analyze-card-title-name
              font-size 12px
              font-weight 500
            .analyze-card-tag
              margin-left 8px
              padding 0 3px
  @keyframes showAnimation {
    0%{
      transform translateY(10%)
      opacity: 0.3;
    }
    100% {
      transform translateY(0)
      opacity: 1;
    }
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.2s ease;
  }

  .fade-enter-from,
  .fade-leave-to{
    opacity: 0;
  }

  .fade-msg-enter-active {
    animation: fade-msg-in 0.4s;
  }
  .fade-msg-leave-active {
    animation: fade-msg-in 0;
  }
  @keyframes fade-msg-in {
    0% {
      height 0;
    }
    100% {
      height: 100%;
    }
  }

.data-agent-container-content
  &.show-detail
        overflow-y hidden
</style>
<style lang="stylus" scoped>
.data-agent-container-content-aside
  width 100%
  height 100%
  overflow-y auto
  padding 0 14px 14px
  box-sizing border-box
  .data-agent-main-aside
    width 100%
    height 100%
    .agent-title-container
      width 100%
      .agent-title
        width 100%
        margin-top 30px
        width 100%
        display flex
        align-items center
        gap 12px
        :deep(.d-text)
          color var(--text-title, #000000D9)
          font-size 20px
          font-weight 500
          line-height 48px
    .session-container-page
      width 100%
      height 100%
      overflow-y auto
    .recommend-list
      width 100%
      margin-top 30px
      .data-title
        margin-bottom 12px
        span
          color var(--text-title, #000000D9)
          font-weight 500
    .recommend-list-aside
      list-style none
      padding 0
      margin 12px 0 0 0
      width 100%
      li
        list-style none
        display flex
        height 28px
        width 330px
        // padding 8px
        align-items center
        justify-content space-between
        cursor pointer
        border-radius 4px
        font-size 12px
        // background-color var(--color-grey-2)
        // margin-bottom 8px
        // padding 0 8px
        .recommend-list-item-text
          max-width 250px
          color var(--text-paragraph, #000000B2)
        &:hover
          .recommend-list-item-text
            color var(--text-title, #000000D9)
.coming-soon
  padding 0px 4px
  gap 2px
  position absolute
  right: -24px;
  top: -13px;
  border-radius var(--size-radius-default)
  background var(--color-blue-1)
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  color: var(--color-blue-8);
  text-overflow: ellipsis;
  font-family: "PingFang SC";
  font-size: var(--size-text-small);
  font-style: normal;
  font-weight: var(--size-text-font-weight-bold);
  line-height: var(--size-text-line-height-small) /* 166.667% */;
</style>
