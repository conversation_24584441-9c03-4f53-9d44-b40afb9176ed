import { fetchAnalysisRecommendList } from '@agent/services/basicInfo'
import { useSessionStore } from '@agent/store/sessionStore'
import { toast2 } from '@xhs/delight'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const useAnalysisRecommend = () => {
  const route = useRoute()
  const analysisRecommend = ref<Record<string, any>>({})
  const activeAnalysisTab = ref<string>('1')
  const isLoading = ref(false)
  const sessionStore = useSessionStore()

  const analysisRecommendTabs = computed(() => {
    const tempMap: Record<string, string> = {
      1: '商业部',
      2: '社区',
      3: '交易',
    }
    return Object.keys(analysisRecommend.value).map((item: any) => ({
      key: item,
      label: tempMap[item],
    }))
  })

  const analysisList = computed(() => analysisRecommend.value[activeAnalysisTab.value] || [])

  onMounted(async () => {
    // 以路由参数为主，如果有 activeAnalysisTab 参数，优先使用
    if (route.query.activeAnalysisTab) {
      activeAnalysisTab.value = route.query.activeAnalysisTab as string
      sessionStore.setAnalysisDatasetType(Number(route.query.activeAnalysisTab))
    }

    isLoading.value = true
    fetchAnalysisRecommendList().then((res: any) => {
      analysisRecommend.value = res
    }).catch((err: any) => {
      toast2.danger(err.data?.errorMsg || '获取分析推荐失败')
    }).finally(() => {
      isLoading.value = false
    })
  })

  return {
    activeAnalysisTab,
    analysisList,
    analysisRecommendTabs,
    analysisRecommend,
    isLoading,
  }
}
