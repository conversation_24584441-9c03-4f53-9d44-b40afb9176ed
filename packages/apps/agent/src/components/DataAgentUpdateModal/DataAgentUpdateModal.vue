<template>
  <Modal
    v-model:visible="modalVisible"
    :closeable="false"
    size="598px"
    :z-index="1003"
    class="data-agent-update-modal"
    confirm-text="前往体验"
    cancel-text="我知道了"
    cancel-type="default"
    @confirm="handleConfirm"
    @cancel="handleClose"
  >
    <div class="data-agent-update-content">
      <div>
        <img :src="dataAskImg" style="width: 100%;" />
      </div>
      <div class="update-section">
        <Text class="section-title">「数据解读」功能上线，取数后一键提问：</Text>
        <div class="feature-item">
          <Text class="feature-text">
            <Icon :size="16"><EntryIcon /> </Icon>
            查询热门笔记后，让AI总结这批笔记特征</Text>
        </div>
        <div class="feature-item">
          <Text class="feature-text">
            <Icon :size="16"><EntryIcon /> </Icon>
            查询客户周度数据后，让AI分析下本周消耗波动的原因</Text>
        </div>
        <div class="feature-item">
          <Text class="feature-text">
            <Icon :size="16"><EntryIcon /> </Icon>
            查询客户计划/创意数据后，让AI分析下哪些计划/创意存在异常</Text>
        </div>
      </div>

      <div class="update-section">
        <div class="feature-item">
          <Text class="feature-text feature-text-support">支持「自助取数」和「AI取数」两大取数入口；支持所有数据集；模型可自由切换，支持Deepseek满血版/Qwen/Kimi</Text>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
  /**
   * @note DataAgent功能更新弹框
   * <AUTHOR>
   * @date 2025-07-22 14:32:32
 * @Last Modified by: <EMAIL>
 * @Last Modified time: 2025-07-22 18:01:26
   */
  import { ref, watch } from 'vue'
  import { Modal, Text, Icon } from '@xhs/delight'
  import { DATA_AGENT_UPDATE_MODAL_FLAG_KEY } from './constants'
  import EntryIcon from '../iconSvg/EntryIcon.vue'

  const props = defineProps<{
    modelValue: boolean
  }>()
  const emits = defineEmits<{
    'update:modelValue': [value: boolean]
    'confirm': []
  }>()

  const modalVisible = ref(props.modelValue)
  const dataAskImg = new URL('./data_ask.png', import.meta.url).href

  const handleConfirm = () => {
    emits('confirm')
    emits('update:modelValue', false)
  }

  const handleClose = () => {
    emits('update:modelValue', false)
  }

  // 监听props变化
  watch(() => props.modelValue, newVal => {
    modalVisible.value = newVal
    if (newVal) {
      // 只要展示过就记一次
      localStorage.setItem(DATA_AGENT_UPDATE_MODAL_FLAG_KEY, 'true')
    }
  })

  watch(modalVisible, newVal => {
    emits('update:modelValue', newVal)
  })
</script>

<style lang="stylus" scoped>
.update-section
  margin-bottom 20px

  &:last-child
    margin-bottom 0

.section-title
  font-size 16px
  font-weight 500
  color var(--color-text-title)
  line-height 64px
  padding-left 24px

.feature-item
  display flex
  align-items flex-start
  padding 0 24px
  margin-bottom 4px

  &:last-child
    margin-bottom 0

.feature-text
  font-size 14px
  color var(--text-title, rgba(0, 0, 0, 0.85))
  line-height 22px
  flex 1
.feature-text-support
  color var(--text-placeholder, rgba(0, 0, 0, 0.42))
</style>
<style lang="stylus">
.data-agent-update-modal .d-modal-content
  padding 0!important
  border-radius 4px!important
</style>
