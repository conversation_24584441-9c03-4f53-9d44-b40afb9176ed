<template>
  <Space
    direction="vertical"
    size="16px"
    align="start"
    block
  >
    <template v-if="reportMessageType === 'OLD_REPORT'">
      <AnalysisProcess :results="ResultOptions" />
      <AnalysisResult
        v-if="hasReportResult"
        :report-doc="reportDoc"
        :report-url="reportUrl"
        :md-content="mdContent"
        :message="message"
      />
    </template>
    <template v-else-if="reportMessageType === 'PLANING'">
      <!-- 分析规划 -->
      <AnalysisPlan :message="message" />
    </template>
    <template v-else-if="reportMessageType === 'EXEC'">
      <!-- 执行结果 -->
      <AnalysisExec
        :message="message"
        @show-detail="$emit('show-detail', $event)"
      />
    </template>
    <template v-else-if="message.content.aiContent?.mdContent">
      <Markdown
        :sse-type="message.isHistory ? undefined : 'polling'"
        :sse-interval="2500"
        :content="{ text: message.content.aiContent?.mdContent }"
        :custom-style="{ backgroundColor: 'transparent', padding: '0' }"
      />
    </template>
    <template v-else-if="message.status === 'RUNNING'">
      <LoadingMessage />
    </template>
  </Space>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { Space } from '@xhs/delight'
  import { Markdown } from '@xhs/techfe-bi-components'
  import { ThinkingProcessNodeState } from '@agent/types/message'
  import LoadingMessage from '@agent/components/sessionContainer/LoadingMessage.vue'
  import AnalysisProcess from './components/AnalysisProcess.vue'
  import AnalysisResult from './components/AnalysisResult.vue'
  import AnalysisPlan from './components/AnalysisPlan.vue'
  import AnalysisExec from './components/AnalysisExec.vue'
  import { getReportMessageType, RUNNING_STAGE } from './useAnalysisData'

  const props = defineProps<{
    message: any
  }>()

  defineEmits<{(e: 'show-detail', tool: any): void
  }>()

  const isStop = computed(() => props.message.status !== 'RUNNING')
  const isNotFail = computed(() => props.message.status !== 'FAIL')
  const reportDoc = computed(() => props.message.content.aiContent?.reportDoc)
  const reportUrl = computed(() => props.message.content.aiContent?.reportUrl)
  const mdContent = computed(() => props.message.content.aiContent?.mdContent)
  const reportMessageType = computed(() => getReportMessageType(props.message.content.aiContent))

  // 结果可能有两种，一种redoc, 一种是markdown
  const hasReportResult = computed(() => isStop.value && isNotFail.value && (reportDoc.value || mdContent.value))

  const InitResult = [
    {
      title: '问题理解',
      key: 'understandingContent',
      content: '',
      state: ThinkingProcessNodeState.RUNNING,
      stage: RUNNING_STAGE.START,
    },
    {
      title: '问题规划',
      key: 'planingContent',
      content: '',
      state: ThinkingProcessNodeState.INIT,
      stage: RUNNING_STAGE.STAGE1,
    },
    {
      title: '数据查询',
      key: 'dataQueryContent',
      content: '',
      state: ThinkingProcessNodeState.INIT,
      stage: RUNNING_STAGE.STAGE2,
    },
    {
      title: '数据分析',
      key: 'dataAnalysisContent',
      content: '',
      state: ThinkingProcessNodeState.INIT,
      stage: RUNNING_STAGE.STAGE3,
    },
  ]

  const ResultOptions = computed(() => {
    const aiContent = props.message.content.aiContent || {}
    const stopBefore = props.message.content.stopBefore

    const getContent = (item: any, runningStage: number) => {
      if (runningStage < item.stage) {
        return ''
      }

      if (runningStage - item.stage > 0) {
        return aiContent[item.key]
      }

      // 阶段三直接给出
      if (runningStage - item.stage === 0 && runningStage === RUNNING_STAGE.STAGE3) {
        return aiContent[item.key]
      }

      return ''
    }

    const getStatus = (item: any, runningStage: number, status: string) => {
      const shouldPause = ['STOPPED', 'SUCCESS', 'FAIL'].includes(status)

      if (runningStage < item.stage) {
        return ThinkingProcessNodeState.INIT
      }

      if (runningStage - item.stage > 0) {
        return ThinkingProcessNodeState.SUCCESS
      }
      if (shouldPause) {
        return status
      }
      return ThinkingProcessNodeState.RUNNING
    }

    let contentStage
    if (typeof stopBefore === 'number') {
      contentStage = stopBefore
    } else {
      contentStage = props.message.content.stage || 0
    }
    const stepList = contentStage === RUNNING_STAGE.START ? InitResult.slice(0, 2) : InitResult

    const result = stepList.map(e => ({
      ...e,
      state: getStatus(e, contentStage, props.message.status),
      content: getContent(e, contentStage),
    }))

    return result
  })
</script>

<style scoped lang="stylus">
  .analysis-card-container
      height: 110px;
      display: flex;
</style>
