<template>
  <Space
    direction="vertical"
    size="8px"
    block
    align="start"
    class="recommend-container"
  >
    <Space
      direction="vertical"
      size="2px"
      :style="{ marginBottom: '4px' }"
      align="start"
    >
      <Text
        v-for="(line, index) in infos"
        :key="index"
      >
        {{ line }}
      </Text>
    </Space>
    <Text>你可以提问:</Text>
    <Space :style="{ flexWrap: 'wrap' }">
      <Tag
        v-for="question in message?.content?.suggestions || []"
        :key="question"
        style="padding: 2px 6px"
        @click="emit('click', question)"
      >
        {{ question }}
      </Tag>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import { Space, Text, Tag } from '@xhs/delight'
  import { computed } from 'vue'

  const props = defineProps<{
    message?: any
  }>()

  const infos = computed(() => props.message?.content?.openingRemarks?.split('\n') || [])

  const emit = defineEmits<{(e: 'click', question: string): void }>()
</script>

<style scoped lang="stylus">
  .recommend-container
    :deep(.d-tag)
      &:hover
        cursor: pointer
</style>
