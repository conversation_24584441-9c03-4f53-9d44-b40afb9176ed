import { createNewSession, getAgentMessage } from '@agent/services/basicInfo'
import { useAgentStore } from '@agent/store/agentStore'
import { useSessionStore } from '@agent/store/sessionStore'
import { AgentModeAiType } from '@agent/types'
import {
  AgentMessageTitle, AgentMessageType, MessageStatus, SessionStatus,
} from '@agent/types/message'
import { useTimeoutPoll } from '@vueuse/core'
import { toast2 } from '@xhs/delight'
import { getRandomMessageId } from '@agent/utils'
import { get } from 'lodash'
import { computed, ref } from 'vue'
import { useDataAgentTrackerStore } from '@analysis/stores/dataAgentTrackerStore'

// 用于区分状态
export const RUNNING_STAGE = {
  START: 0, // 开始、问题理解
  STAGE1: 1, // 问题规划
  STAGE2: 2, // 数据查询
  STAGE3: 3, // 数据分析
  STOPPED: 4, // 停止
}

export const getUserMessage = (option: Record<string, any>): any => {
  const userMessage = {
    messageId: option.messageId,
    sessionId: option.sessionId,
    title: '',
    role: 'user' as const,
    content: {
      type: 'markdown' as const,
      text: option.content?.content,
      ...option.content,
    },
    type: 'text' as const,
    status: 'SUCCESS' as MessageStatus,
  }

  return userMessage
}

// eslint-disable-next-line
export const getReportMessageType = (aiContent?: any): string => {

  const sessionStore = useSessionStore()
  const agentId = sessionStore.analysisAgentId

  if (!agentId || aiContent?.dataAnalysisContent) {
    return 'OLD_REPORT'
  }

  if (aiContent?.stage) {
    // 有可能计划没有生成，但已经产生了结果
    const steps = aiContent?.analysisExecuteContent?.steps
    const hasSteps = Array.isArray(steps) && steps.length > 0
    const hasResult = aiContent?.mdContent
    if (aiContent?.stage === 'PLANING' && hasSteps && hasResult) {
      return 'EXEC'
    }
    return aiContent?.stage
  }

  if (aiContent?.analysisPlanContent) {
    if (aiContent?.analysisExecuteContent) {
      // 执行过程&结果
      return 'EXEC'
    }
    return 'PLANING'
  }
  return 'UNKNOWN'
}

// eslint-disable-next-line
export const isNormalAnalysisResult = (aiContent?: any): boolean => {
  const messageType = getReportMessageType(aiContent)
  return messageType !== 'PLANING' && messageType !== 'UNKNOWN'
}

const getUpdateQuestionUnderstandingContent = (llmContent: string) => {
  if (!llmContent) {
    return {}
  }

  let llmContentObj
  try {
    llmContentObj = JSON.parse(llmContent)
  } catch (error) {
    console.warn('解析llmContent失败', error)
    return {}
  }

  const {
    确认解析内容: confirmContent,
    是否需要澄清: needClarify,
    ...queryParams
  } = llmContentObj

  return {
    confirmContent,
    needClarify,
    queryParams,
  }
}

export const getAssistantMessage = (option: Record<string, any>, title: string): any => {
  const status = option.content?.aiContent?.status
  const isSuccess = status === 'SUCCESS'
  const isRunning = status === 'RUNNING'
  const llmContent = get(
    option.content,
    'aiContent.evaluateContext.questionUnderstandingContext.assistantCompletion.llmContent',
  )
  const { confirmContent, queryParams } = getUpdateQuestionUnderstandingContent(llmContent)

  let stopBefore = RUNNING_STAGE.START
  if (isRunning) {
    stopBefore = RUNNING_STAGE.START
  } else if (isSuccess) {
    stopBefore = RUNNING_STAGE.STOPPED
  } else if (confirmContent) {
    stopBefore = RUNNING_STAGE.STAGE3
  }

  // 错误处理
  let errorMsg
  const errorType = option.content?.aiContent?.errorType
  if (errorType === 'FETCH_DATA_EMPTY') {
    // 只保留questionParams.value对象中value不为空的属性
    const filteredParams = Object.fromEntries(
      Object.entries(queryParams).filter(([, value]) => value !== undefined && value !== null && value !== ''),
    )
    errorMsg = `${JSON.stringify(filteredParams)}数据查询为空，请你确认输入是否完整`
  } else {
    errorMsg = option.content?.aiContent?.errorMsg
  }

  const assistantMessage = {
    messageId: option.messageId,
    sessionId: option.sessionId,
    title,
    errorMsg,
    role: 'assistant' as const,
    content: {
      // TODO: 这里目前写死的
      type: AgentMessageType[3],
      stopBefore,
      stage: RUNNING_STAGE.STOPPED,
      aiContent: {
        ...option.content?.aiContent,
        understandingContent: confirmContent,
      },
    },
    isHistory: true,
    type: 'text' as const,
    status: isSuccess ? 'SUCCESS' : isRunning ? 'RUNNING' : 'FAIL' as MessageStatus,
    traceId: option.traceId,
  }

  return assistantMessage
}

export const getAnalysisSessionFromHistory = (msgs: Record<string, any>[]): any => {
  const analysisScene = msgs[0]?.content?.analysisScene
  const agentId = msgs[0]?.content?.agentId
  const sessionStore = useSessionStore()
  sessionStore.setAnalysisAgentCode(msgs[0]?.content?.analysisScene)
  sessionStore.setAnalysisAgentId(agentId)
  const messages = msgs.map((msg: any) => {
    if (msg.content?.role === 'user') {
      return getUserMessage(msg)
    }
    let assistantTitle = 'AI分析'
    const messageType = getReportMessageType(msg?.content?.aiContent)
    const isRunningStatus = msg?.content?.aiContent?.status === 'RUNNING'

    if (messageType !== 'OLD_REPORT') {
      if (messageType === 'PLANING') {
        assistantTitle = isRunningStatus ? AgentMessageTitle.ANALYSIS_PLAINING : '已完成分析规划'
      } else {
        assistantTitle = isRunningStatus ? AgentMessageTitle.ANALYSIS_EXEC : '已完成分析'
      }
    }

    return getAssistantMessage(msg, assistantTitle)
  })

  const session = {
    sessionId: messages[0].sessionId,
    scene: analysisScene, // 这里需要后端补充信息后改掉
    type: AgentModeAiType.analyze,
    messages, // AI分析可能会有虚拟消息，需要保留
    status: 'EDITING' as SessionStatus,
  }

  return session
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const useAnalysisData = () => {
  const { handleTracker } = useDataAgentTrackerStore()
  const sessionStore = useSessionStore()
  const agentStore = useAgentStore()

  // 用于控制轮询的暂停（或者结束）与恢复
  const isStopped = ref(false)
  const timeOutResRef = ref()
  const runningStage = ref(RUNNING_STAGE.START)
  const pollingCount = ref(0) // 调试的时候用过，暂无其它作用
  const questionUnderstandingContent = ref('')
  const questionParams = ref({})
  const projectId = ref(0)
  const outputContent = ref('') // 测试
  // 用于记录当前会话的场景信息
  const oldSceneData = ref()

  const currentSession = computed(() => sessionStore.currentSession)
  const lastMessage = computed(
    () => currentSession.value?.messages && currentSession.value?.messages[currentSession.value?.messages.length - 1],
  )

  const initStatus = () => {
    isStopped.value = false
    questionUnderstandingContent.value = ''
    questionParams.value = {}
    runningStage.value = RUNNING_STAGE.START
    pollingCount.value = 0
    outputContent.value = ''
  }

  const createAnalysisChat = async (item: any, project: number) => {
    if (!item) {
      return
    }
    projectId.value = project
    oldSceneData.value = item

    let sessionId = null
    try {
      sessionId = await createNewSession({ sessionType: agentStore.aiType })
    } catch (error: any) {
      toast2.danger(error.data?.errorMsg || '创建会话失败')
      return
    }

    const anentMessage = {
      messageId: getRandomMessageId(),
      sessionId,
      title: item?.name || 'AI分析',
      role: 'assistant' as const,
      content: {
        type: 'analysisRecommend' as const,
        suggestions: item.suggestions,
        openingRemarks: item.openingRemarks,
      },
      type: 'text' as const,
      status: '' as any,
    }

    sessionStore.setCurrentSession({
      sessionId,
      scene: item.type,
      type: AgentModeAiType[agentStore.aiType],
      messages: [anentMessage], // AI分析可能会有虚拟消息，需要保留
      status: 'EDITING' as SessionStatus,
    })

    // 如果是goodStrategy，则需要将第一个推荐问题自动填入输入框
    if (item.type === 'GOOD_STRATEGY') {
      if (!Array.isArray(item.suggestions) || item.suggestions.length === 0) {
        toast2.danger('没有推荐问题')
        return
      }
      return item.suggestions[0] ?? ''
    }
  }

  const reStartAnalysisChat = () => {
    const sceneItem = oldSceneData.value
    if (!sceneItem) {
      return
    }

    timeOutResRef.value?.pause?.()
    createAnalysisChat(sceneItem, projectId.value)
  }

  const stopAnalysisChatPolling = () => {
    timeOutResRef.value?.pause?.()
  }

  const stopAnalysisChat = () => {
    isStopped.value = true
    timeOutResRef.value?.pause?.()
    sessionStore.updateMessage(lastMessage.value?.messageId as string, {
      status: 'STOPPED' as MessageStatus,
    })
  }

  const getPatchedMessage = (
    response: any,
    lastMsg: any,
    status: string,
    stage?: number,
    stopBefore?: number,
  ) => {
    const errorType = response?.aiContent?.errorType
    let errorMsg

    const messageType = getReportMessageType(response?.aiContent)
    let title = oldSceneData.value?.name || 'AI分析'
    if (messageType !== 'OLD_REPORT') {
      if (status === 'RUNNING') {
        if (messageType === 'PLANING') {
          title = AgentMessageTitle.ANALYSIS_PLAINING
        } else if (messageType === 'EXEC') {
          title = AgentMessageTitle.ANALYSIS_EXEC
        } else {
          title = AgentMessageTitle.THINKING
        }
      } else {
        switch (messageType) {
          case 'PLANING':
            title = '已完成分析规划'
            break
          case 'EXEC':
            title = '已完成分析'
            break
          default:
            title = 'AI分析'
            break
        }
      }
    }

    if (errorType === 'FETCH_DATA_EMPTY') {
      // 只保留questionParams.value对象中value不为空的属性
      const filteredParams = Object.fromEntries(
        Object.entries(questionParams.value).filter(([, value]) => value !== undefined && value !== null && value !== ''),
      )
      errorMsg = `${JSON.stringify(filteredParams)}数据查询为空，请你确认输入是否完整`
    } else {
      errorMsg = response?.aiContent?.errorMsg
    }

    const patchedMessage = {
      status: status as MessageStatus,
      title,
      errorMsg,
      errorType,
      content: {
        ...lastMsg.content,
        aiContent: {
          ...response?.aiContent,
          understandingContent: questionUnderstandingContent.value,
        },
      },
    }

    if (typeof stopBefore === 'number') {
      patchedMessage.content.stopBefore = stopBefore
    }

    if (typeof stage === 'number') {
      patchedMessage.content.stage = stage
    }

    return patchedMessage
  }

  const waitTime = async (ms: number, isNeedResume = true) => {
    timeOutResRef.value?.pause?.()
    await new Promise(resolve => setTimeout(resolve, ms * 1000))
    if (isNeedResume && !isStopped.value) {
      timeOutResRef.value?.resume?.()
    }
  }

  const isPolling = ref(false)

  const getProgressInfo = async () => {
    const lastMsg = lastMessage.value
    const sessionId = currentSession.value?.sessionId
    if (isPolling.value) {
      return
    }
    isPolling.value = true

    if (isStopped.value || !lastMsg || !sessionId) {
      stopAnalysisChatPolling()
      isPolling.value = false
      return
    }
    // 接口查询
    const params = {
      sessionId,
      answerMessageId: lastMsg.messageId,
      analysisScene: currentSession.value?.scene,
    }

    let response = null
    try {
      response = await getAgentMessage(params)
    } catch (err: any) {
      toast2.danger(err.data?.errorMsg || '获取分析过程异常')
      timeOutResRef.value?.pause?.()
      isPolling.value = false
      return
    }
    const messageType = getReportMessageType(response?.aiContent)

    const responseStatus = response?.aiContent?.status

    let shouldPause = false
    if (['STOPPED', 'FAIL'].includes(responseStatus)) {
      shouldPause = true
    } else if (responseStatus === 'SUCCESS') {
      // 分析规划消息如果成功，那么立即停止轮询
      shouldPause = ['PLANING', 'EXEC', 'UNKNOWN'].includes(messageType) ? true : runningStage.value >= RUNNING_STAGE.STAGE3
    }

    if (['PLANING', 'EXEC', 'UNKNOWN'].includes(messageType) && !shouldPause) {
      // 分析规划消息处理
      const patchedMessage = getPatchedMessage(
        response,
        lastMsg,
        'RUNNING',
        RUNNING_STAGE.STAGE1,
      )
      sessionStore.updateMessage(lastMsg.messageId as string, patchedMessage)
      isPolling.value = false
      return
    }
    // 历史分析消息类型处理
    const llmContent = get(
      response,
      'aiContent.evaluateContext.questionUnderstandingContext.assistantCompletion.llmContent',
    )

    const { confirmContent, queryParams } = getUpdateQuestionUnderstandingContent(llmContent)

    if (confirmContent) {
      questionUnderstandingContent.value = confirmContent
      questionParams.value = queryParams
    }

    if (!shouldPause) {
      if (runningStage.value === RUNNING_STAGE.START) {
        await waitTime(2)
        if (isStopped.value) {
          isPolling.value = false
          return
        }
        if (confirmContent) {
          // 进入下一阶段
          runningStage.value = RUNNING_STAGE.STAGE1
          // 更新状态信息
          const patchedMessage = getPatchedMessage(
            response,
            lastMsg,
            'RUNNING',
            RUNNING_STAGE.STAGE1,
          )
          sessionStore.updateMessage(lastMsg.messageId as string, patchedMessage)
        }
        isPolling.value = false
        return
      }

      // 如果收到planingContent的回答，则暂停轮询，等待10秒后恢复轮询
      if (runningStage.value === RUNNING_STAGE.STAGE1) {
        await waitTime(5)
        if (isStopped.value) {
          isPolling.value = false
          return
        }
        if (response?.aiContent?.planingContent) {
          runningStage.value = RUNNING_STAGE.STAGE2 // 切换状态
          // 更新状态信息
          const patchedMessage = getPatchedMessage(
            response,
            lastMsg,
            'RUNNING',
            RUNNING_STAGE.STAGE2,
          )
          sessionStore.updateMessage(lastMsg.messageId as string, patchedMessage)
        }
        isPolling.value = false
        return
      }

      if (runningStage.value === RUNNING_STAGE.STAGE2 && response?.aiContent?.dataQueryContent) {
        await waitTime(5)
        if (isStopped.value) {
          isPolling.value = false
          return
        }
        runningStage.value = RUNNING_STAGE.STAGE3 // 切换状态
        // 更新状态信息
        const patchedMessage = getPatchedMessage(
          response,
          lastMsg,
          'RUNNING',
          RUNNING_STAGE.STAGE3,
        )
        sessionStore.updateMessage(lastMsg.messageId as string, patchedMessage)
        isPolling.value = false
        return
      }

      if (runningStage.value === RUNNING_STAGE.STAGE3 && response?.aiContent?.dataAnalysisContent) {
        // 更新状态信息
        const patchedMessage = getPatchedMessage(
          response,
          lastMsg,
          'RUNNING',
          RUNNING_STAGE.STAGE3,
        )
        sessionStore.updateMessage(lastMsg.messageId as string, patchedMessage)
        isPolling.value = false
        return
      }
    }

    if (shouldPause) {
      // if (responseStatus === 'SUCCESS') {
      //   await waitTime(18, false)
      // }
      isStopped.value = true
      timeOutResRef.value?.pause?.()
      let stopBefore
      if (response?.aiContent?.errorType === 'FETCH_DATA_EMPTY') {
        stopBefore = RUNNING_STAGE.STAGE2
      } else if (responseStatus === 'SUCCESS') {
        stopBefore = RUNNING_STAGE.STOPPED
      } else {
        stopBefore = runningStage.value
      }
      const patchedMessage = getPatchedMessage(
        response,
        lastMsg,
        responseStatus,
        RUNNING_STAGE.STOPPED,
        stopBefore,
      )
      runningStage.value = RUNNING_STAGE.STOPPED
      sessionStore.updateMessage(lastMsg.messageId as string, patchedMessage)

      // 查询完成时的埋点上报
      handleAnalysisTracker(response, responseStatus)
      isPolling.value = false
    }
    isPolling.value = false
  }

  // 埋点上报函数
  const handleAnalysisTracker = (response: any, status: string) => {
    const currentSession_ = currentSession.value
    if (!currentSession_) return
    const query = response?.aiContent?.query || ''
    // 提取分析报告内容 url
    const report = response?.aiContent?.reportUrl || ''
    // 提取模型输出内容
    const flowId = response?.aiContent?.flowId
    const modelOutput = flowId ? `https://aimi.devops.xiaohongshu.com/mark/admin/prompt/getSessionContext?flowId=${flowId}&includeAll=true` : 'flowId不存在'
    const payload = {
      data_agent_session_id: String(currentSession_.sessionId),
      // 分析名称
      data_agent_analysis_name: currentSession_.scene || `scene不存在，请检查sessionId:${currentSession_.sessionId}`,
      // 语料内容
      data_agent_query: query,
      // 模型输出内容
      data_agent_model_output: modelOutput,
      // 分析报告内容
      data_agent_analysis_report: report,
      // 查询是否成功
      data_agent_is_query_success: String(status === 'SUCCESS'),
      // 错误类型
      data_agent_error_type: response?.aiContent?.errorType || '',
      // traceId
      trace_id: response?.traceId,
    }
    handleTracker('data_agent_user_analysis', payload)
  }

  const pollingProgressInfo = async () => {
    const timeOutRes = useTimeoutPoll(getProgressInfo, 2500, {
      immediate: true,
    })

    timeOutResRef.value = timeOutRes
    // 状态初始化
    initStatus()
  }

  return {
    analysisProjectId: projectId,
    createAnalysisChat,
    reStartAnalysisChat,
    stopAnalysisChat,
    pollingProgressInfo,
  }
}
