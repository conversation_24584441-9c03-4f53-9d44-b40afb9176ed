<template>
  <div
    v-if="executeContent"
    style="width: 100%"
  >
    <!-- 分析执行步骤 -->
    <AnalysisSteps
      :steps="stepsContentFlow"
      :string-field-status="stringFieldStatus"
      @show-detail="$emit('show-detail', $event)"
    />
  </div>
  <template v-else-if="message.status === 'RUNNING'">
    <LoadingMessage />
  </template>

  <div
    v-if="reportContentFlow"
    style="width: 100%"
  >
    <AnalysisResult
      :report-url="reportContentFlow?.['分析报告']?.['分析链接']"
      :md-content="reportContentFlow?.['分析报告']?.['分析报告']"
      :message="message"
      has-flow
    />
  </div>
</template>

<script setup lang="ts">
  /**
   * @note 分析规划
   * <AUTHOR>
   * @date 2025-08-04 12:21:16
 * @Last Modified by: cheng<PERSON><PERSON>@xiaohongshu.com
 * @Last Modified time: 2025-08-18 16:26:14
   */

  import { ref, watch, computed } from 'vue'
  import LoadingMessage from '@agent/components/sessionContainer/LoadingMessage.vue'
  import { useJsonFlow } from '@agent/utils/jsonFlow'
  import AnalysisResult from './AnalysisResult.vue'
  import AnalysisSteps from './AnalysisSteps.vue'

  const props = defineProps<{
    message: any
  }>()

  defineEmits<{(e: 'show-detail', tool: any): void }>()

  const executeContent = ref(props.message.content.aiContent?.analysisExecuteContent || '')
  const reportContent = ref(props.message.content.aiContent?.analysisReportContent || '')
  const steps = ref(props.message.content.aiContent?.analysisExecuteContent?.steps || [])
  const flowValue = computed(() => ({
    value1: steps.value, // 执行步骤信息
    value2: reportContent.value, // 分析报告信息
  }))

  // 将处于 RUNNING 的 toolStatus 统一置为 STOPPED（用于外层停止后清理状态）
  const normalizeRunningToolStatus = () => {
    steps.value = (steps.value || []).map((step: any) => ({
      ...step,
      // 将步骤本身的运行状态也置为 STOPPED
      任务状态: step?.['任务状态'] === 'RUNNING' ? 'STOPPED' : step?.['任务状态'],
      tools: (step.tools || []).map((tool: any) => ({
        ...tool,
        toolStatus: tool?.toolStatus === 'RUNNING' ? 'STOPPED' : tool?.toolStatus,
      })),
    }))
  }

  const flowOptions = ref({
    enable: !props.message.isHistory,
    totalTime: 3000,
    chunkTime: 50, // 字符串分片时间 150ms
    chunkSize: 1, // 字符串分片大小 2 个字符
    fieldList: [ // 这些字段不参与流式计算
      'value1',
      'analysisMethod',
      'questionDescription',
      '任务ID',
      '任务状态',
      'type',
      '取数url',
      '分析结果',
      'analysisElems',
      'toolStatus',
    ],
    fieldOptions: {
      'value2.分析报告.分析报告': {
        chunkTime: 16,
        chunkSize: 5,
      },
    },
  })
  const jsonFlow = useJsonFlow(flowValue, flowOptions)
  const stepsContentFlow = computed(() => (Array.isArray(jsonFlow.result.value.value1) ? jsonFlow.result.value.value1 : []))
  const reportContentFlow = computed(() => jsonFlow.result.value.value2)
  const stringFieldStatus = computed(() => jsonFlow.stringFieldStatus.value)

  watch(
    () => props.message.content.aiContent,
    (newVal: any) => {
      executeContent.value = newVal?.analysisExecuteContent
      reportContent.value = newVal?.analysisReportContent
      steps.value = Array.isArray(newVal?.analysisExecuteContent?.steps) ? newVal?.analysisExecuteContent?.steps : []
      // 如果外层消息已停止，进入时需要同步清理 RUNNING 状态
      if (props.message.status === 'STOPPED') {
        normalizeRunningToolStatus()
      }
    },
  )

  // 外层消息停止时，取消内部 RUNNING 状态
  watch(
    () => props.message.status,
    (status: string) => {
      if (status === 'STOPPED') {
        normalizeRunningToolStatus()
      }
    },
    { immediate: true },
  )
</script>

<style scoped lang="stylus"></style>
