<!--
 * @Date: 2025-06-06 17:48:28
 * @LastEditors: 尼禄(张子蓥) <EMAIL>
 * @LastEditTime: 2025-06-07 11:49:27
 * @Description:
-->
<template>
  <!-- 思考过程 -->
  <ThinkingProcess :nodes="results">
    <template #understandingContent="{ node }">
      <Markdown
        v-if="node.content"
        :content="{ text: (node.content) }"
        :sse-type="'polling'"
        :sse-interval="2500"
        style="opacity: 0.5"
        :custom-style="{ backgroundColor: 'transparent', padding: '0' }"
      />
    </template>
    <template #planingContent="{ node }">
      <Text v-if="node.content?.length < 5">
          {{ '\n' }}
      </Text>
      <Markdown
        v-else
        :content="{ text: (node.content) }"
        :sse-type="'polling'"
        :sse-interval="2500"
        style="opacity: 0.5"
        :custom-style="{ backgroundColor: 'transparent', padding: '0' }"
      />
    </template>
    <template #dataQueryContent="{ node }">
      <Space direction="vertical" size="2px" align="start">
        <Text v-for="innerText in parseLines(node.content as string)" :key="innerText">
          {{ innerText }}
        </Text>
      </Space>
    </template>
    <template #dataAnalysisContent="{ node }">
      <!-- 模式为轮询， 轮询间隔为2500ms -->
      <Markdown
        v-if="node.content"
        :content="{ text: node.content }"
        :sse-type="'polling'"
        :sse-interval="2500"
        style="opacity: 0.5; border-left: 2px solid rgba(0, 0, 0, 0.2); padding: 0 10px; margin-left: -10px;"
        :custom-style="{ backgroundColor: 'transparent', padding: '0', border: 'none' }"
        @scroll-to-bottom="handleScrollToBottom"
      />
    </template>
  </ThinkingProcess>
</template>

<script setup lang="ts">
  // @ts-nocheck
  import { Text, Space } from '@xhs/delight'
  import ThinkingProcess from '@agent/components/message/pickData/components/ThinkingProcess.vue'
  import { Markdown } from '@xhs/techfe-bi-components'
  import { publish } from '@xhs/redbi-share-utils'

  defineProps<{
    results: {
      key: string
      title: string
      content: string
      state: any
    }[]
  }>()

  function parseLines(content: string) {
    return content
      ?.split('\n')
      ?.map(line => line.trim())
      ?.filter(line => line && /^\d+\./.test(line))
      ?.map(line => line.trim())
  }

  function handleScrollToBottom(data: any) {
    publish('scroll-to-bottom', data)
  }
</script>

<style scoped lang="stylus">
  .analysis-step-container
    width: '100%'

    :deep(.d-steps-item-main)
      flex: 1

  .analysis-data-footer
    width: 778px
    height: 40px
    position: absolute
    bottom: 0px
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FCFCFD 100%)
</style>
