<template>
  <div class="pick-data-container analysis-steps-container">
    <!-- 思考过程 -->
    <ThinkingProcess :nodes="showNodes">
      <template #analysisContext="{ node, index }">
        <div
          v-if="(node as any)?.content?.reasoning"
          style="margin-bottom: 8px"
        >
          <AnimatedText
            :model-value="(node.content as any).reasoning"
            :is-streaming="stringFieldStatus[`value1[${index}].reasoning`] === false"
            color="text-paragraph"
          />
        </div>
        <template
          v-for="(tool, toolIndex) in (node as any).content.tools"
          :key="tool.key"
        >
          <!-- 取数 -->
          <Space
            v-show="tool.问题描述"
            direction="vertical"
            align="start"
            size="2px"
            class="thinking-process-description"
          >
            <!-- 取数tag -->
            <Tag
              v-if="tool.问题描述"
              size="small"
              class="normal-tag"
              :class="{ 'selected-tag': selectedDataTag === `pickData_${tool.key}`, 'disabled-tag': tool.toolStatus !== 'SUCCESS' }"
              @click="onTagClick(`pickData_${tool.key}`, tool)"
            >
              <Space
                size="4px"
                style="padding: 2px; max-width: 100%;"
              >
                <Icon
                  :icon="Form"
                  size="small"
                  color="text-paragraph"
                />
                <Text
                  color="text-paragraph"
                  class="analysis-tag-text"
                  size="small"
                >取数{{ node.status }}</Text>
                <Text
                  color="text-paragraph"
                  ellipsis
                  style="max-width: 100%"
                  class="analysis-tag-text"
                  tooltip
                  :tooltip-props="{ theme: 'light' }"
                >
                  {{ tool.问题描述 }}
                </Text>

                <div
                  v-if="tool.toolStatus === 'RUNNING'"
                  class="bubble-loading__dots"
                >
                  <span />
                  <span />
                  <span />
                </div>

                <!-- 执行异常展示 -->
                <Tooltip
                  v-else-if="tool.toolStatus === 'FAILED' && tool.取数失败原因"
                  theme="light"
                  :content="tool.取数失败原因"
                >
                  <div class="pick-data-error-text">
                    <Text
                      color="warning"
                      ellipsis
                    >
                      <Icon
                        :icon="Caution"
                        size="small"
                        color="warning"
                      />
                      执行异常
                    </Text>
                  </div>
                </Tooltip>
                <Icon
                  v-else
                  :icon="ArrowRightUp"
                  size="small"
                  color="text-paragraph"
                />
              </Space>
            </Tag>
          </Space>
          <!-- 分析 -->
          <Space
            v-show="tool.分析思路"
            direction="vertical"
            align="start"
            size="2px"
            class="thinking-process-description"
          >
            <Tag
              v-if="tool.分析思路"
              size="small"
              class="normal-tag"
              :style=" tool.问题描述 ? { marginTop: '0' } : {}"
              :class="{ 'selected-tag': selectedDataTag === `analysis_${tool.key}`, 'disabled-tag': tool.toolStatus === 'RUNNING' }"
              @click="onTagClick(`analysis_${tool.key}`, tool)"
            >
              <Space
                size="4px"
                style="padding: 2px; max-width: 100%;"
              >
                <Icon
                  :icon="Tips"
                  size="small"
                  color="text-paragraph"
                />
                <Text
                  color="text-paragraph"
                  class="analysis-tag-text"
                  size="small"
                >分析</Text>
                <Text
                  color="text-paragraph"
                  ellipsis
                  style="max-width: 100%;"
                  class="analysis-tag-text"
                  tooltip
                  :tooltip-props="{ theme: 'light' }"
                >
                  {{ tool.分析思路 }}
                </Text>
                <div
                  v-if="tool.toolStatus === 'RUNNING'"
                  class="bubble-loading__dots"
                >
                  <span />
                  <span />
                  <span />
                </div>
                <Tooltip
                  v-else-if="tool.toolStatus === 'FAILED' && tool.分析失败原因"
                  theme="light"
                  :content="tool.分析失败原因"
                >
                  <Text
                    color="warning"
                    ellipsis
                    underline="dotted"
                  >
                    <Icon
                      :icon="Caution"
                      size="small"
                      color="warning"
                    />
                    执行异常
                  </Text>
                </Tooltip>
                <Icon
                  v-else
                  :icon="ArrowRightUp"
                  size="small"
                  color="text-paragraph"
                />
              </Space>
            </Tag>
          </Space>
          <div v-if="tool.反思">
            <AnimatedText
              :model-value="tool.反思"
              :is-streaming="stringFieldStatus[`value1[${index}].tools[${toolIndex}].反思`] === false"
              color="text-paragraph"
            />
          </div>
        </template>
      </template>
    </ThinkingProcess>
    <!-- {{ analysisUrl }} -->
  </div>
</template>
<script setup lang="ts">
  import {
    ref, onMounted, watch, inject,
  } from 'vue'
  import { ThinkingProcessNode, ThinkingProcessNodeState } from '@agent/types/message'
  import {
    Tag, Icon, Text, Space, Tooltip,
  } from '@xhs/delight'
  import {
    ArrowRightUp, Form, Tips, Caution,
  } from '@xhs/delight/icons'
  import ThinkingProcess from '@agent/components/message/pickData/components/ThinkingProcess.vue'
  import AnimatedText from './AnimatedText.vue'

  const props = defineProps<{
    steps: any[]
    stringFieldStatus: Record<string, boolean>
  }>()

  const emit = defineEmits<{(e: 'show-detail', tool: any): void
  }>()

  // 添加选中状态管理
  const selectedDataTag = ref<string | null>(null)
  const isDetailPanelOpen = inject('DetailPanelOpen', ref(false)) as any
  // 监听详情面板关闭，清除选中态
  watch(isDetailPanelOpen, (val: boolean) => {
    if (!val) selectedDataTag.value = null
  })
  type ThinkingProcessNodeTemp = ThinkingProcessNode & { content: any }

  const showNodes = ref<ThinkingProcessNodeTemp[]>([
    {
      key: 'analysisContext',
      title: '',
      state: ThinkingProcessNodeState.RUNNING,
      content: {
        reasoning: '',
        tools: [],
      },
      link: '',
      stepType: 'analysisContext',
    },
  ])

  // 处理取数tag点击
  const handleDataTagClick = (key: string) => {
    selectedDataTag.value = selectedDataTag.value === key ? null : key
    if (selectedDataTag.value) {
      // 解析key，提取类型和工具key
      const [type, toolKey] = key.split('_')
      const isPickData = type === 'pickData'

      // 使用数组方法找到匹配的工具
      const foundTool = showNodes.value
        .filter(node => node.content?.tools)
        .flatMap(node => node.content.tools)
        .find((tool: any) => tool.key === toolKey)

      if (foundTool) {
        // 将工具数据和类型信息一起传递
        const toolWithType = {
          ...foundTool,
          toolType: isPickData ? '取数' : '分析',
        }
        emit('show-detail', toolWithType)
      }
    } else {
      emit('show-detail', null)
    }
  }
  // RUNNING 时禁用点击
  const onTagClick = (key: string, tool: any) => {
    if (tool?.toolStatus !== 'SUCCESS') return
    handleDataTagClick(key)
  }
  const generateBlockId = (): string => Array.from({ length: 32 }, () => Math.floor(Math.random() * 16).toString(16)).join('')

  function initSteps() {
    if (!props.steps) return
    // 根据steps 转换为showNodes
    showNodes.value = props.steps?.map((item: any) => ({
      key: item['任务ID'],
      title: item['任务名称'],
      state: item['任务状态'],
      content: {
        ...item,
        tools: item.tools?.map((tool: any) => ({
          ...tool,
          key: generateBlockId(),
        })),
      },
      stepType: 'analysisContext',
    }))
  }
  watch(() => props.steps, () => {
    initSteps()
  })

  onMounted(() => {
    initSteps()
  })
</script>

<style lang="stylus" scoped>

  .pick-data-container
    display flex
    flex-direction: column
    align-items: center
    justify-content: center
    width: 100%
    height: 100%
    :deep(.understanding-strong)
      color: rgba(0, 0, 0, 0.7)
      font-weight: 500
  .normal-tag
    max-width: 100%
    border: 1px solid transparent
    border-radius: 4px
    padding: 2px 4px
    cursor: pointer
    margin: 0 0 8px 0
    &.disabled-tag
      cursor: not-allowed

  .selected-tag
    border: 1px solid #1890ff !important
    background-color: rgba(35,114,251,.1) !important

  .open-analysis-button
    width: 100%
    height: 32px
    background-color: rgba(0, 0, 0, 0.03)
    justify-content: space-between
    border-radius: 4px
    cursor pointer
    margin: 12px 0 0;
    padding: 0 10px;
    &:hover
      background-color: rgba(0, 0, 0, 0.05)
  .aside-mode-card
    width: 100%
    height: 300px
    border: 1px solid rgba(0, 0, 0, 0.1)
    border-radius: 4px

  // bubble loading 样式
  .bubble-loading__dots
    display: flex
    gap: 4px

  .bubble-loading__dots span
    width: 6px
    height: 6px
    background-color: var(--color-text-description)
    border-radius: 50%
    animation: bubble-pulse 1.4s infinite ease-in-out both

  .bubble-loading__dots span:nth-child(1)
    animation-delay: -0.32s

  .bubble-loading__dots span:nth-child(2)
    animation-delay: -0.16s

  @keyframes bubble-pulse
    0%, 80%, 100%
      transform: scale(0)
    40%
      transform: scale(1)
  .thinking-process-description
    display: flex

  // 最后一步都需一条竖线，连接内容
  .analysis-steps-container
    :deep(.d-steps-item:last-of-type .d-steps-item-head .d-steps-item-line)
      width: 1px
      display: block
  .analysis-tag-text
    font-weight: 400
  .pick-data-error-text
    cursor: pointer
    display: inline-flex
    align-items: center
    border-bottom: 1px dashed rgb(141,141,141)
    padding-bottom: 1px

</style>
