<template>
  <div class="data-tag-detail-container">
    <!-- 头部 -->
    <div class="detail-header">
      <Space
        justify="space-between"
        align="center"
        style="width: 100%;"
      >
        <!-- 左上角展示任务描述 -->
        <Text
          color="text-title"
          bold
        >
          {{ taskDescription }}
        </Text>
        <!-- 右上角收起按钮 -->
        <Button
          type="light"
          size="small"
          style="flex-shrink: 0"
          @click="handleClose"
        >
          <Icon
            :icon="ToRight"
          />
        </Button>
      </Space>
    </div>

    <!-- 详情内容 -->
    <div class="data-tag-detail-content">
      <DataTagDetailContent :current-tool="currentTool" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    Space, Text, Button, Icon,
  } from '@xhs/delight'
  import { ToRight } from '@xhs/delight/icons'
  import DataTagDetailContent from './DataTagDetailContent.vue'

  const props = defineProps<{
    currentTool: any
  }>()

  const emit = defineEmits<{(e: 'close'): void
  }>()

  // 计算任务描述
  const taskDescription = computed(() => {
    if (!props.currentTool) return '执行详情'

    const toolType = props.currentTool.toolType || '工具'
    const description = props.currentTool?.['问题描述'] || props.currentTool?.['分析思路'] || ''

    if (description) {
      return description
    }

    return `${toolType}执行详情`
  })

  const handleClose = () => {
    emit('close')
  }
</script>

<style lang="stylus" scoped>
.data-tag-detail-container
  width: 100%
  height: 100%
  display: flex
  flex-direction: column
  background: rgb(250, 250, 250)

.detail-header
  padding: 12px 20px 0
  flex-shrink: 0

.data-tag-detail-content
  padding: 16px 20px
  flex: 1
  overflow-y: auto

.status-section
  display: flex
  align-items: center

.detail-section
  width: 100%

.content-box
  background-color: rgba(0, 0, 0, 0.02)
  border-radius: 6px
  padding: 12px
  border: 1px solid rgba(0, 0, 0, 0.06)
  margin-top: 8px

.result-content
  max-height: 300px
  overflow-y: auto

.error-content
  background-color: rgba(255, 0, 0, 0.05)
  border-color: rgba(255, 0, 0, 0.15)

.analysis-elems
  .field-label
    min-width: 60px
    flex-shrink: 0
</style>
