<template>
  <template v-if="isLastMessage && planContent">
    <div class="analysis-plan-container">
      <Text>我计划按照以下步骤分析，如需调整可手动编辑，或告诉我：</Text>
      <AnimatedTextArea
        :model-value="planContentFlow.result.value"
        :rows="1"
        autosize
        :is-streaming="planContentFlow.isStreaming.value"
        class="analysis-plan-textarea"
        @update:modelValue="handlePlanContentChange"
        @input="handlePlanContentInput"
      />
      <div v-if="planContent && !planContentFlow.isStreaming.value">
        <!-- 只有成功状态会展示这个按钮 -->
        <Button
          :icon="Tips"
          class="analysis-plan-btn"
          @click="handleStartAnalysis"
        >开始分析</Button>
      </div>
    </div>
  </template>

  <template v-else-if="planContent">
    <Text class="analysis-plan-content">{{ planContent }}</Text>
  </template>
  <template v-else-if="message.status === 'RUNNING'">
    <LoadingMessage />
  </template>
</template>

<script setup lang="ts">
  /**
   * @note 分析规划
   * <AUTHOR>
   * @date 2025-08-04 12:21:16
 * @Last Modified by: <EMAIL>
 * @Last Modified time: 2025-08-15 18:39:26
   */

  import {
    computed, ref, watch,
  } from 'vue'
  import { Text, Button } from '@xhs/delight'
  import { useSessionStore } from '@agent/store/sessionStore'
  import { Tips } from '@xhs/delight/icons'
  import { publish } from '@xhs/redbi-share-utils'
  import LoadingMessage from '@agent/components/sessionContainer/LoadingMessage.vue'
  import { MessageStatus } from '@agent/types/message'
  import { useJsonFlow } from '@agent/utils/jsonFlow'
  import AnimatedTextArea from './AnimatedTextArea.vue'

  const sessionStore = useSessionStore()

  const props = defineProps<{
    message: any
  }>()

  const planContent = ref(props.message.content.aiContent?.analysisPlanContent?.['分析计划Md'] || '')

  const flowOptions = ref({
    enable: !props.message.isHistory,
    totalTime: 4000, // 总时间 4 秒
    chunkTime: 16, // 字符串分片时间 150ms
    chunkSize: 1, // 字符串分片大小 2 个字符
  })
  const planContentFlow = useJsonFlow(planContent, flowOptions)

  const handlePlanContentInput = () => {
    flowOptions.value.enable = false
  }

  const isFlowRunning = computed(() => sessionStore.isFlowRunning)

  const currentSession = computed(() => sessionStore.currentSession)

  // 是否是最后一条消息
  const isLastMessage = computed(
    () => currentSession.value?.messages[currentSession.value?.messages.length - 1]?.messageId
      === props.message.messageId,
  )

  const handlePlanContentChange = (val: string) => {
    if (isFlowRunning.value || flowOptions.value.enable) {
      return
    }
    planContent.value = val
  }

  const handleStartAnalysis = async () => {
    const currentInput = planContent.value.trim()
    if (!currentInput) {
      return
    }
    sessionStore.updateMessage(props.message.messageId, {
      status: 'SUCCESS' as MessageStatus,
      title: '已完成分析规划',
      content: {
        ...props.message.content,
        aiContent: {
          ...props.message.content.aiContent,
          analysisPlanContent: {
            ...props.message.content.aiContent.analysisPlanContent,
            分析计划Md: planContent.value, // 更新这条消息规划内容
          },
        },
      },
    })
    publish('send-message', {
      sendContent: '开始分析',
      from: 'analysis',
      extra: {
        planContent: currentInput,
      },
    })
  }

  watch(
    () => props.message.content.aiContent?.analysisPlanContent,
    (newVal: any) => {
      planContent.value = newVal?.['分析计划Md'] || ''
    },
  )
</script>

<style scoped lang="stylus">
.analysis-plan-container
  position relative
  width 100%
  .analysis-plan-textarea {
    width 100%
    display block
    margin 8px 0
  }
  .analysis-plan-btn {
    background: var(--color-item-toggle-active, #EBF2FF);
    color: var(--color-text-toggle-active, #3077F1);
  }
.analysis-plan-content {
  white-space: pre-line;
}
</style>
