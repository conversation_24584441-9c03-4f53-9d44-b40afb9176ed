<template>
  <div class="animated-text-container">
    <Text
      ref="textRef"
      v-bind="$attrs"
      class="animated-text"
    >
      {{ displayValue }}
    </Text>
    <div
      v-if="showOverlay && overlayPosition.x && isStreaming"
      class="fade-overlay"
      :style="{
        left: (overlayPosition.x - 80 > 0 ? overlayPosition.x - 80 : 0) + 'px',
        top: overlayPosition.y + 'px',
        width: overlayPosition.width + 'px',
        height: overlayPosition.height + 'px',
        transition: 'left 0.1s ease-out, top 0.1s ease-out',
      }"
    />
  </div>
</template>

<script setup lang="ts">
  import {
    computed, ref, watch, nextTick, onMounted,
  } from 'vue'
  import { Text } from '@xhs/delight'

  const props = defineProps<{
    modelValue: string
    isStreaming?: boolean
  }>()

  const textRef = ref()
  const displayValue = computed(() => props.modelValue)

  const showOverlay = ref(false)
  const overlayPosition = ref({
    x: 0, y: 0, width: 0, height: 0,
  })
  const lastPosition = ref({ x: 0, y: 0 }) // 记录上一次位置

  // 计算蒙层位置
  const calculateOverlayPosition = async () => {
    await nextTick()

    // 获取 Text 组件的内部元素
    const textElement = textRef.value?.$el as HTMLElement
    if (!textElement) return

    const text = props.modelValue

    // 获取 text 的样式
    const computedStyle = window.getComputedStyle(textElement)
    const lineHeight = parseInt(computedStyle.lineHeight, 10) || 20

    // 创建临时元素来模拟 text 的渲染，获取实际行数
    const tempDiv = document.createElement('div')
    tempDiv.style.font = computedStyle.font
    tempDiv.style.fontSize = computedStyle.fontSize
    tempDiv.style.fontFamily = computedStyle.fontFamily
    tempDiv.style.fontWeight = computedStyle.fontWeight
    tempDiv.style.whiteSpace = 'pre-wrap'
    tempDiv.style.wordWrap = 'break-word'
    tempDiv.style.overflowWrap = 'break-word'
    tempDiv.style.position = 'absolute'
    tempDiv.style.visibility = 'hidden'
    tempDiv.style.pointerEvents = 'none'
    tempDiv.style.width = `${textElement.clientWidth}px`
    tempDiv.style.lineHeight = computedStyle.lineHeight
    tempDiv.style.boxSizing = 'border-box'
    tempDiv.style.minHeight = '0'
    tempDiv.style.maxHeight = 'none'

    // 设置文本内容
    tempDiv.textContent = text

    document.body.appendChild(tempDiv)

    // 获取实际的行数（通过计算高度）
    const actualHeight = tempDiv.scrollHeight
    const actualLineCount = Math.ceil(actualHeight / lineHeight)

    // 获取最后一行文本
    // 我们需要找到最后一行实际的内容
    const lines = text.split('\n')
    let lastLineText = lines[lines.length - 1] || ''

    // 如果最后一行很长，需要计算它在自动换行后的实际位置
    if (lastLineText.length > 0) {
      // 创建一个只包含最后一行文本的临时元素
      const lastLineDiv = document.createElement('div')
      lastLineDiv.style.font = computedStyle.font
      lastLineDiv.style.fontSize = computedStyle.fontSize
      lastLineDiv.style.fontFamily = computedStyle.fontFamily
      lastLineDiv.style.fontWeight = computedStyle.fontWeight
      lastLineDiv.style.whiteSpace = 'pre-wrap'
      lastLineDiv.style.wordWrap = 'break-word'
      lastLineDiv.style.overflowWrap = 'break-word'
      lastLineDiv.style.position = 'absolute'
      lastLineDiv.style.visibility = 'hidden'
      lastLineDiv.style.pointerEvents = 'none'
      lastLineDiv.style.width = `${textElement.clientWidth}px`
      lastLineDiv.style.lineHeight = computedStyle.lineHeight
      lastLineDiv.style.boxSizing = 'border-box'

      // 只设置最后一行文本
      lastLineDiv.textContent = lastLineText

      document.body.appendChild(lastLineDiv)

      // 计算最后一行文本的宽度
      const lastLineHeight = lastLineDiv.scrollHeight
      const lastLineActualLines = Math.ceil(lastLineHeight / lineHeight)

      // 如果最后一行有自动换行，我们需要计算最后一部分的宽度
      if (lastLineActualLines > 1) {
        // 找到最后一行文本在自动换行后的最后一部分
        const words = lastLineText.split('')
        let lastPartStart = 0

        for (let i = 0; i < words.length; i++) {
          const testText = words.slice(0, i + 1).join('')
          const testSpan = document.createElement('span')
          testSpan.style.font = computedStyle.font
          testSpan.style.fontSize = computedStyle.fontSize
          testSpan.style.fontFamily = computedStyle.fontFamily
          testSpan.style.fontWeight = computedStyle.fontWeight
          testSpan.style.whiteSpace = 'pre'
          testSpan.style.position = 'absolute'
          testSpan.style.visibility = 'hidden'
          testSpan.textContent = testText

          document.body.appendChild(testSpan)
          const testWidth = testSpan.offsetWidth
          document.body.removeChild(testSpan)

          if (testWidth > textElement.clientWidth) {
            // 发生换行，记录新行的开始位置
            lastPartStart = i
            break
          }
        }

        // 获取最后一部分的文本
        lastLineText = words.slice(lastPartStart).join('')
      }

      document.body.removeChild(lastLineDiv)
    }

    // 计算最后一行文本的宽度
    const tempSpan = document.createElement('span')
    tempSpan.style.font = computedStyle.font
    tempSpan.style.fontSize = computedStyle.fontSize
    tempSpan.style.fontFamily = computedStyle.fontFamily
    tempSpan.style.fontWeight = computedStyle.fontWeight
    tempSpan.style.whiteSpace = 'pre'
    tempSpan.style.position = 'absolute'
    tempSpan.style.visibility = 'hidden'
    tempSpan.style.pointerEvents = 'none'
    tempSpan.textContent = lastLineText

    document.body.appendChild(tempSpan)
    const lastLineWidth = tempSpan.offsetWidth
    document.body.removeChild(tempSpan)
    document.body.removeChild(tempDiv)

    // 计算蒙层位置和尺寸
    const overlayWidth = 100 // 固定宽度100px
    const overlayHeight = lineHeight

    const newX = lastLineWidth
    const newY = (actualLineCount - 1) * lineHeight

    // 添加位置平滑处理，减少抖动
    const smoothedX = newX
    const smoothedY = newY

    overlayPosition.value = {
      x: smoothedX,
      y: smoothedY,
      width: overlayWidth,
      height: overlayHeight,
    }

    // 更新上一次位置
    lastPosition.value = { x: newX, y: newY }
  }

  // 监听流式状态变化
  watch(
    () => props.isStreaming,
    newVal => {
      if (newVal) {
        showOverlay.value = true
      } else {
        showOverlay.value = false
        // 重置位置记录
        lastPosition.value = { x: 0, y: 0 }
      }
    },
    { immediate: true },
  )

  // 监听文本变化，重新计算蒙层位置
  watch(
    () => props.modelValue,
    () => {
      if (props.isStreaming) {
        calculateOverlayPosition()
      }
    },
  )
  onMounted(() => {
    if (textRef.value && props.isStreaming) {
      calculateOverlayPosition()
    }
  })

</script>

<style scoped lang="stylus">
  .animated-text-container
    position relative
    width 100%

  .animated-text
    width 100%
    display block

  .analysis-tag-text
    font-weight: 400

  .fade-overlay
    position absolute
    background linear-gradient(to right, transparent 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.95) 100%)
    pointer-events none
    z-index 10
    border-radius 2px
</style>
