<template>
  <Space
    :id="resultId"
    direction="vertical"
    :style="{ width: '100%' }"
    align="start"
  >
    <template v-if="reportDoc">
      <Redoc
        style="width: 100%"
        :value="reportDoc"
      />
      <!-- 在REDoc中打开 -->
      <Space
        v-if="reportUrl"
        class="open-redoc-button"
        @click="openRedoc"
      >
        <Text
          color="text-title"
          bold
        >
          在REDoc中打开
        </Text>
        <Icon
          :icon="ArrowRight"
          color="text-paragraph"
        />
      </Space>
    </template>
    <template v-else-if="mdContent">
      <Markdown
        :content="{ text: mdContent }"
        :sse-type="message.isHistory && hasFlow ? undefined : 'polling'"
        :sse-interval="2500"
        :custom-style="{ backgroundColor: 'transparent', padding: '0' }"
        @scroll-to-bottom="handleScrollToBottom"
      />
      <!-- 在REDoc中打开 -->
      <Space
        v-if="reportUrl && !isFlowRunning"
        class="open-redoc-button"
        @click="openRedoc"
      >
        <Text
          color="text-title"
          bold
        >
          一键复制报告, 并在REDoc中打开
        </Text>
        <Icon
          :icon="ArrowRight"
          color="text-paragraph"
        />
      </Space>
      <Space
        v-else-if="!isFlowRunning"
        class="open-redoc-button"
        @click="copyHTML"
      >
        <Text
          color="text-title"
          bold
        >
          一键复制报告, 可以粘贴到Redoc
        </Text>
        <Icon
          :icon="ArrowRight"
          color="text-paragraph"
        />
      </Space>
    </template>
  </Space>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import {
    Space, Text, Icon, toast2,
  } from '@xhs/delight'
  import { ArrowRight } from '@xhs/delight/icons'
  import Redoc from '@agent/components/message/redoc/Index.vue'
  import { Markdown } from '@xhs/techfe-bi-components'
  import { publish } from '@xhs/redbi-share-utils'
  import copy from 'copy-to-clipboard'
  import { useSessionStore } from '@agent/store/sessionStore'

  const sessionStore = useSessionStore()

  const props = defineProps<{
    reportDoc?: string
    reportUrl?: string
    mdContent: string
    message: any
    hasFlow?: boolean
  }>()

  const openRedoc = () => {
    if (!props.reportDoc) {
      copyHTML()
    }
    window.open(props.reportUrl, '_blank')
  }

  const isFlowRunning = computed(() => sessionStore.isFlowRunning)

  function handleScrollToBottom(data: any) {
    publish('scroll-to-bottom', data)
  }

  // 生成随机的 block-id
  const generateBlockId = (): string => Array.from({ length: 32 }, () => Math.floor(Math.random() * 16).toString(16)).join('')
  const resultId = ref<string>(generateBlockId())
  // 增强版的 wc-analysis-preview 转换函数
  const convertWcAnalysisPreviewToAnchorAdvanced = (htmlContent: string): string => {
    // 创建临时DOM容器
    const tempContainer = document.createElement('div')
    tempContainer.innerHTML = htmlContent

    // 处理 li 标签内的 wc-analysis-preview
    const liElements = tempContainer.querySelectorAll('li')
    liElements.forEach(li => {
      const wcAnalysisElements = li.querySelectorAll(':scope > wc-analysis-preview')
      wcAnalysisElements.forEach(wcElement => {
        const analysisUrl = wcElement.getAttribute('analysis-url')
        if (analysisUrl) {
          // 解码 HTML 实体
          const decodedUrl = analysisUrl
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')

          // 移除 wc-analysis-preview 元素
          wcElement.remove()
          const decodedUrlList = decodedUrl.split('||')
          const eles:any[] = []
          decodedUrlList.forEach(url => {
            // 在 li 后面创建 a 标签
            const linkElement = document.createElement('a')
            linkElement.href = url
            linkElement.setAttribute('data-title', 'RedBI')
            linkElement.setAttribute('data-view-type', 'preview')
            linkElement.target = '_blank'

            const spanElement = document.createElement('span')
            spanElement.textContent = url
            linkElement.appendChild(spanElement)
            eles.push(linkElement)
          })
          // 在 li 后面插入换行和链接
          li.after(document.createTextNode('\n'))
          li.after(...eles)
        }
      })
    })

    // 处理其他独立的 wc-analysis-preview 标签
    const wcAnalysisElements = tempContainer.querySelectorAll('wc-analysis-preview')
    wcAnalysisElements.forEach(wcElement => {
      const analysisUrl = wcElement.getAttribute('analysis-url')
      if (analysisUrl) {
        // 解码 HTML 实体
        const decodedUrl = analysisUrl
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')

        const decodedUrlList = decodedUrl.split('||')
        const eles:any[] = []
        decodedUrlList.forEach(url => {
          // 创建 a 标签
          const linkElement = document.createElement('a')
          linkElement.href = url
          linkElement.setAttribute('data-title', 'RedBI')
          linkElement.setAttribute('data-view-type', 'preview')
          linkElement.target = '_blank'

          const spanElement = document.createElement('span')
          spanElement.textContent = url

          linkElement.appendChild(spanElement)
          eles.push(linkElement)
        })
        // 替换 wc-analysis-preview 元素
        wcElement.replaceWith(...eles)
      }
    })

    return tempContainer.innerHTML
  }

  // 清理 HTML 内容的函数
  const cleanHtmlContent = (html: string): string => (
    html
      // 移除多余的空白字符
      .replace(/\s+/g, ' ')
      // 移除标签之间的空白
      .replace(/>\s*</g, '><')
      // 移除开头和结尾的空白
      .trim()
      // 移除连续的换行符
      .replace(/\n\s*\n/g, '\n')
      // 移除 HTML 注释
      .replace(/<!--[\s\S]*?-->/g, '')
      // 移除空的 div 和 span
      .replace(/<(div|span)[^>]*>\s*<\/\1>/g, '')
      // 移除只包含空白字符的元素
      .replace(/<(div|span|p)[^>]*>\s*<\/\1>/g, '')
      // 移除多余的空格
      .replace(/\s{2,}/g, ' ')
  )

  // 复制纯HTML内容
  const copyHTML = () => {
    try {
      const redocElement = document.getElementById(resultId.value)
      if (!redocElement) {
        toast2.warning('未找到渲染组件')
        return
      }

      const editorContent = redocElement.querySelector('.markdown-body') || redocElement

      if (!editorContent) {
        toast2.warning('无法获取渲染内容')
        return
      }

      // 复制转换后的 HTML 内容
      let htmlContent = editorContent.innerHTML

      // 将 wc-analysis-preview 标签替换为 a 标签
      htmlContent = convertWcAnalysisPreviewToAnchorAdvanced(htmlContent)

      // 清理 HTML 内容，去除多余的空行和空白
      htmlContent = cleanHtmlContent(htmlContent)

      if (copy(htmlContent, { format: 'text/html' })) {
        toast2.success('报告内容已复制到剪贴板，请在Redoc中粘贴')
      } else {
        toast2.warning('复制失败')
      }
    } catch (error) {
      console.error('复制HTML失败:', error)
      toast2.warning('复制失败')
    }
  }

</script>

<style scoped lang="stylus">
.open-redoc-button
  width: 100%
  height: 32px
  background-color: rgba(0, 0, 0, 0.03)
  justify-content: space-between
  border-radius: 4px
  cursor pointer
  margin: 12px 0 0;
  padding: 0 10px;
  &:hover
    background-color: rgba(0, 0, 0, 0.05)
</style>
