<template>
  <div class="pick-data-container">
    <!-- {{ sessionStore.currentSession?.messages }} -->
    <!-- {{ showNodes }} -->
    <!-- {{ sessionStore.currentSession?.status }} -->
    <div v-if="currentTool.toolType === '取数'">

      <!-- 思考过程 -->
      <ThinkingProcess :nodes="showNodes">
        <template #understandingIntentionContext="{ node }">
          <Space
            v-show="node.content"
            direction="horizontal"
            align="start"
            size="4px"
            class="thinking-process-description"
          >
            <Text
              color="text-description"
              v-html="formatTextWithBrackets(node.content)"
            />
          </Space>
        </template>
        <template #selectOneDatasetContext="{ node }">
          <Space
            v-show="node.content"
            direction="vertical"
            align="start"
            size="2px"
            class="thinking-process-description"
          >
            <Tag
              v-if="node.content"
              size="small"
              :style="{
                padding: '4px 6px',
              }"
            >
              <Link
                v-if="node.link"
                :href="node.link"
                muted
                size="small"
                target="_blank"
                style="display: flex; align-items: center;"
              >
                <Icon
                  size="small"
                  style="margin: 0px 8px 4px 2px;"
                >
                  <DatasetSvg />
                </Icon>
                <Text
                  color="text-paragraph"
                  style="padding: 0px 2px; font-weight: normal"
                >
                  {{ node.content }}
                </Text>
                <Icon
                  size="small"
                  color="text-paragraph"
                  style="margin: 0px 4px 1px 4px;"
                >
                  <ShareSvg />
                </Icon>
              </Link>
              <Text
                v-else
                color="text-paragraph"
                style="font-weight: normal"
              >
                {{ node.content }}
              </Text>
            </Tag>
          </Space>
        </template>
      </ThinkingProcess>

      <!-- 判断当前模式 single aside -->
      <template v-if="currentMode === 'single'">

        <!-- 如果是取数类型，保持现状渲染逻辑 -->
        <!-- 分析结果图表 -->
        <AnalysisPreview
          v-show="analysisUrl"
          :analysis-url="analysisUrl"
          :page-from="'analysisData'"
        />
        <!-- 在自助分析打开 -->
        <Space
          v-show="analysisUrl"
          class="open-analysis-button"
          @click="openAnalysis"
        >
          <Text
            color="text-paragraph"
            bold
          >
            在自助分析打开
          </Text>
          <Icon
            style="width: 14px; height: 14px;line-height: 14px;"
            color="text-paragraph"
          >
            <ShareSvg />
          </Icon>
        </Space>
      </template>
    </div>
    <div v-else>
      <Markdown
        :content="{ text: currentTool['分析结果'] || '' }"
        :custom-style="{ backgroundColor: 'transparent', padding: '0' }"
        @scroll-to-bottom="handleScrollToBottom"
      />
    </div>
    <!-- {{ analysisUrl }} -->

  </div>
</template>
  <script  setup lang="ts">
  import {
    ref, computed, watch,
  } from 'vue'
  import {
    AgentFetchResponseContent, ThinkingProcessNode, ThinkingProcessNodeState,
  } from '@agent/types/message'
  import { get } from 'lodash'

  import {
    Tag, Link, Icon, Text, Space,
  } from '@xhs/delight'
  import { useRoute } from 'vue-router'
  import { useAgentStore } from '@agent/store/agentStore'
  import ShareSvg from '@agent/components/iconSvg/ShareSvg.vue'
  import DatasetSvg from '@agent/components/iconSvg/DatasetSvg.vue'
  import ThinkingProcess from '@agent/components/message/pickData/components/ThinkingProcess.vue'
  import AnalysisPreview from '@agent/components/message/pickData/components/AnalysisPreview.vue'
  import { Markdown } from '@xhs/techfe-bi-components'
  import { publish } from '@xhs/redbi-share-utils'

  const props = defineProps<{
    currentTool: any
  }>()

  const agentStore = useAgentStore()

  const route = useRoute()
  const analysisUrl = ref('')
  const showNodes = ref<ThinkingProcessNode[]>([
    {
      key: 'selectOneDatasetContext', title: '匹配数据集', state: ThinkingProcessNodeState.INIT, content: '', link: '',
    },
    {
      key: 'fieldsContext', title: '涉及字段', state: ThinkingProcessNodeState.INIT, content: '', link: '',
    },
  ])
  const currentMode = computed(() => (agentStore.isOpenAgent === 'true' ? 'single' : agentStore.mode))

  // 处理滚动到底部事件
  function handleScrollToBottom(data: any) {
    publish('scroll-to-bottom', data)
  }

  function transferContent(context: AgentFetchResponseContent, key: string) {
    try {
      if (key === 'selectOneDatasetContext') {
        return get(context, '数据集名称') || ''
      }
      if (key === 'fieldsContext') {
        // 涉及字段阶段时 将字符串数组转换为类型安全的对象数组
        const measureContent = (get(context, '指标') || []) as any[]
        const dimensionContent = (get(context, '维度') || []) as any[]
        const filterContent = (get(context, '筛选条件') || []) as any[]

        const mappedMeasures = (Array.isArray(measureContent) && measureContent.length
          ? measureContent
          : ['-']
        ).map((item: any) => (typeof item === 'string'
          ? { 字段名称: item, 聚合方式: '' }
          : item))

        const mappedDimensions = (Array.isArray(dimensionContent) && dimensionContent.length
          ? dimensionContent
          : ['-']
        ).map((item: any) => (typeof item === 'string'
          ? { 字段名称: item }
          : item))

        const mappedFilters = (Array.isArray(filterContent) && filterContent.length
          ? filterContent
          : ['-']
        ).map((item: any) => (typeof item === 'string'
          ? { 字段名称: item?.split(' ')[0] || item, 匹配类型: item?.split(' ')[1] || '', 条件: item?.split(' ').slice(2).join(' ') || '' }
          : item))

        return {
          measures: mappedMeasures,
          dimensions: mappedDimensions,
          filters: mappedFilters,
        }
      }
      return ''
    } catch (error) {
      return ''
    }
  }

  function transferLink(context: AgentFetchResponseContent, key: string) {
    if (key === 'selectOneDatasetContext') {
      const datasetId = get(context, '数据集id') || ''
      if (datasetId) {
        return `/dataset/list?id=${datasetId}&projectId=${route.query.projectId}`
      }
      return ''
    }
    return ''
  }

  function initOpenAgentSession() {
    const context = props.currentTool.analysisElems as AgentFetchResponseContent

    if (!context) return

    // 更新节点状态
    showNodes.value = showNodes.value.map((item: ThinkingProcessNode) => ({
      ...(item as any),
      state: ThinkingProcessNodeState.SUCCESS,
      content: transferContent(context, item.key),
      link: transferLink(context, item.key),
      stepType: props.currentTool.toolType,
    }))

    // 更新分析结果图表url
    analysisUrl.value = get(props.currentTool, '取数url') || ''
  }

  const openAnalysis = () => {
    if (analysisUrl.value) {
      agentStore.setIsAsideOpen(true)
      const url = new URL(analysisUrl.value)
      const analysisId = Number(url.searchParams.get('analysisId') || url.searchParams.get('resourceId'))
      const shortcutId = url.searchParams.get('shortcutId')
      let targetUrl = `/analysis/edit?projectId=${route.query.projectId || 4}&analysisId=${analysisId}`
      if (shortcutId) {
        targetUrl += `&shortcutId=${shortcutId}`
      }
      window.open(targetUrl)
    }
  }

  // 处理方括号内容加粗显示
  const formatTextWithBrackets = (content: any) => {
    if (!content) return ''

    // 如果是字符串，处理方括号加粗
    if (typeof content === 'string') {
      return content.replace(/\「([^\」]+)\」/g, '<span class="understanding-strong">「$1」</span>').replace(/\[([^\]]+)\]/g, '<span class="understanding-strong">[$1]</span>')
    }

    // 如果是对象（fieldsContext的情况），返回空字符串，因为这种情况应该由专门的模板处理
    return ''
  }
  // 监听 props.currentTool 的变化
  watch(
    () => props.currentTool,
    newTool => {
      if (newTool) {
        initOpenAgentSession()
      }
    },
    { deep: true, immediate: true },
  )

  </script>

  <style lang="stylus" scoped>

  .pick-data-container
    display flex
    flex-direction: column
    // align-items: center
    justify-content: center
    width: 100%
    :deep(.understanding-strong)
      color: rgba(0, 0, 0, 0.7)
      font-weight: 500
  .open-analysis-button
    width: 100%
    height: 32px
    background-color: rgba(0, 0, 0, 0.03)
    justify-content: space-between
    border-radius: 4px
    cursor pointer
    margin: 12px 0 0;
    padding: 0 10px;
    &:hover
      background-color: rgba(0, 0, 0, 0.05)
  .aside-mode-card
    width: 100%
    height: 300px
    border: 1px solid rgba(0, 0, 0, 0.1)
    border-radius: 4px
  </style>
