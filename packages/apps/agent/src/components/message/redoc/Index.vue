<template>
  <div class="redoc-container">
    <!-- <Button
      size="small"
      :icon="FullScreenOne"
      :style="{
        position: 'absolute',
        right: '20px',
        zIndex: 1,
      }"
      @click="previewRedoc"
    /> -->
    <red-editor
      :value="docValue"
      :box-style="style"
      :is-image-click-preview-on="false"
      readonly
      @reditorinject="inject"
    />
    <Modal
      v-model:visible="visible"
      title="分析结果"
      full-screen
      outside-closeable
      @confirm="handleClose"
      @cancel="handleClose"
    >
      <red-editor
        :value="docValue"
        :box-style="style"
        :is-image-click-preview-on="false"
        readonly
        @reditorinject="inject"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { toast2, Modal } from '@xhs/delight'
  // import { FullScreenOne } from '@xhs/delight/icons'

  const props = defineProps<{
    value: string
  }>()

  const visible = ref(false)

  function handleClose() {
    visible.value = false
  }

  const docValue = computed(() => {
    let value: Record<string, any> = {}

    try {
      value = JSON.parse(props.value)
    } catch (error) {
      if (value) {
        toast2.danger('解析失败')
      }
    }

    let fixValue

    if (value?.children) {
      fixValue = value?.children
    } else {
      fixValue = [
        {
          type: 'paragraph',
          blockId: '09419e284f7f71f00decab5ee37c362a',
          update: '500006442_2024-03-14T09:59:06.409Z',
          au: '500006442',
          ...value,
        },
      ]
    }

    return JSON.stringify(fixValue)
  })

  // @ts-ignore
  const inject = () => {
    //
  }

  const style = JSON.stringify({
    width: '100%',
    // border: '1px solid red',
    borderRadius: '4px',
    backgroundColor: '#f7f7f7',
    padding: '0px',
  })
</script>

<style scoped lang="stylus">
  .redoc-container
    width: 100%;
    padding: 6px;
    position: relative;

    :deep().doc-title
      display none
  :deep().sdk-editor
    padding 0
</style>
