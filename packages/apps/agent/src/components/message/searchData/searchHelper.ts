import { AgentModeAiType } from '@agent/types'
import { AgentMessageType, MessageStatus, SessionStatus } from '@agent/types/message'
import { get } from 'lodash'
import { getRandomMessageId } from '@agent/utils'

const getUserMessage = (option: Record<string, any>): any => {
  const userMessage = {
    messageId: option.messageId,
    sessionId: option.sessionId,
    title: '',
    role: 'user' as const,
    content: {
      type: 'markdown' as const,
      text: option.content?.content,
    },
    type: 'text' as const,
    status: 'SUCCESS' as MessageStatus,
  }

  return userMessage
}

const getAssistantMessage = (option: Record<string, any>): any => {
  const assistantMessage = {
    title: '为你找到如下数据内容:',
    role: 'assistant' as const,
    type: 'text' as const,
    isHistory: true,
    ...option,
  }

  return assistantMessage
}

export const getSearchSessionFromHistory = (msgs: Record<string, any>[]): any => {
  const messages = msgs.map((msg: any) => {
    if (msg.content?.role === 'user') {
      return getUserMessage(msg)
    }
    // 回显转换
    const newOption: Record<string, any> = {}
    const result = msg.content
    if (get(result, 'aiContent.status') === 'SUCCESS') {
      newOption.status = get(result, 'aiContent.status')
      newOption.messageId = msg.messageId
      newOption.sessionId = msg.sessionId
      newOption.content = {
        type: AgentMessageType[2],
        text: '',
        markDownString: get(result, 'aiContent.summary'),
      }
      newOption.traceId = msg.traceId
    } else {
      newOption.status = get(result, 'aiContent.status')
      newOption.errorMsg = get(result, 'aiContent.errorMsg')
      newOption.errorType = get(result, 'aiContent.errorType')
      newOption.content = {
        type: AgentMessageType[2],
      }
      newOption.traceId = msg.traceId
    }
    return getAssistantMessage(newOption)
  })

  const session = {
    sessionId: messages[0].sessionId,
    type: AgentModeAiType[2],
    messages, // AI分析可能会有虚拟消息，需要保留
    status: 'EDITING' as SessionStatus,
  }
  return session
}

export const getSearchAssistantMessage = (
  result: Record<string, any>[],
  option: Record<string, any>,
): any => {
  const status = get(result, 'aiContent.status')
  const isSuccess = status === 'SUCCESS'
  const newOption: Record<string, any> = { ...option, status }

  if (isSuccess) {
    newOption.content = {
      type: AgentMessageType[2],
      text: '',
      markDownString: get(result, 'aiContent.summary'),
    }
  } else {
    newOption.errorMsg = get(result, 'aiContent.errorMsg')
    newOption.errorType = get(result, 'aiContent.errorType')
    newOption.content = {
      type: AgentMessageType[2],
    }
  }
  return getAssistantMessage(newOption)
}

export const getAssistantRunningMessage = (option: Record<string, any>): any => {
  const assistantMessage = {
    messageId: getRandomMessageId(),
    sessionId: option.sessionId,
    title: '正在为你查找...',
    role: 'assistant' as const,
    content: {
      type: AgentMessageType[2],
      text: '',
    },
    type: 'text' as const,
    status: 'RUNNING' as MessageStatus,
  }

  return assistantMessage
}
