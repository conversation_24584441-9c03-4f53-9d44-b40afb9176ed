<!--
 * @Date: 2025-06-09 11:27:32
 * @LastEditors: 尼禄(张子蓥) <EMAIL>
 * @LastEditTime: 2025-06-09 12:12:55
 * @Description:
-->
<template>
  <div class="clarification-container">
    <Text color="text-title">{{ message?.clarificationQuestion ?? '抱歉，当前问题我无法理解，请提供更多信息' }}</Text>
  </div>
</template>
<script setup lang="ts">
// 澄清问题
  import { Text } from '@xhs/delight'
  import { AgentMessage } from '@agent/types/message'

  defineProps<{
    message: AgentMessage
  }>()

</script>
<style scoped>
.clarification-container {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}
</style>
