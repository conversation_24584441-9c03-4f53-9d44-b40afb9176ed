<template>
  <div class="data-ask-message">
    <div
      v-if="message.warnMsg"
      class="data-ask-message-warn"
    >
      <Text>
        {{ warningMsg[0] }}
      </Text>
      <Text
        v-if="warningMsg[1]"
        class="underline"
        tooltip="当前数据内容超过模型最大接收量，可尝试精简字段或补充筛选条件"
      >
        {{ warningMsg[1] }}
      </Text>
    </div>
    <LoadingMessage v-if="showLoading" />
    <!-- 思考过程 -->
    <ThinkingProcess
      v-if="results.length > 0"
      :nodes="results"
    >
      <template #reasoningContent="{ node }">
        <Markdown
          v-if="node.content"
          :content="{ text: node.content }"
          style="opacity: 0.5; border-left: 2px solid rgba(0, 0, 0, 0.2); padding: 0 10px; margin-left: -10px;"
          :custom-style="{ backgroundColor: 'white', padding: '0', border: 'none' }"
          @scroll-to-bottom="handleScrollToBottom"
        />
      </template>
    </ThinkingProcess>
    <Markdown
      :message="message"
      :content="message.content"
      :custom-style="{ backgroundColor: 'transparent', maxHeight: 'none' }"
      :message-id="message.messageId"
    />
  </div>
</template>
<script setup lang="ts">
  import { computed } from 'vue'
  import { Text } from '@xhs/delight'
  import { AgentMessage, DataAskContext } from '@agent/types/message'
  import ThinkingProcess from '@agent/components/message/pickData/components/ThinkingProcess.vue'
  import { Markdown } from '@xhs/techfe-bi-components'
  import { publish } from '@xhs/redbi-share-utils'
  import LoadingMessage from '@agent/components/sessionContainer/LoadingMessage.vue'

  const props = defineProps<{
    message: AgentMessage
  }>()

  const results = computed(() => {
    const content = props.message.content as DataAskContext
    // 判断 type 字段确保是 dataAsk 类型
    if (content && content.type === 'dataAsk' && content.reasoningContent && content.reasoningContent.enable) {
      return [content.reasoningContent]
    }
    return []
  })

  // 分割警告信息, 添加下划线
  const warningMsg = computed(() => {
    const msg = props.message.warnMsg
    if (!msg) {
      return ['']
    }
    const index = msg.indexOf('数据量太大，')
    if (index === -1) {
      return [msg]
    }
    return [msg.slice(0, index + 6), msg.slice(index + 6)]
  })

  const showLoading = computed(() => {
    const content = props.message.content as DataAskContext
    if (content && content.type === 'dataAsk' && props.message.status === 'RUNNING' && (content.text === '' && !content.reasoningContent?.content)) {
      return true
    }
    return false
  })

  function handleScrollToBottom(data: any) {
    publish('scroll-to-bottom', data)
  }
</script>

<style lang="stylus" scoped>
.data-ask-message-warn
  // --color-text-paragraph: #e77c05;
  margin-left: 22px;
  margin-bottom: 10px;
  .underline
    background: linear-gradient(to right, transparent 50%, black 50%) repeat-x;
    background-size: 4px 1px;
    background-position: bottom;
</style>
