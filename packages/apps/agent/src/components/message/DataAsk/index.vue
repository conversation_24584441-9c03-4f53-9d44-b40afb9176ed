<template>
  <div class="data-ask">
    <Tooltip
      v-for="item in list"
      :key="item.title"
      :content="item.tooltip"
    >
      <Button
        type="light"
        size="small"
        :disabled="disabled"
        :class="['data-ask-btn', getRuntimeFlag(item) ? 'show-new-notice' : '']"
        @click="handleClick(item)"
      >
        <Icon
          size="small"
          style="margin-right: 4px;"
        >
          <component :is="item.icon" />
        </Icon>
        {{ item.title }}
        <Space />
      </Button>
    </Tooltip>
  </div>
</template>
<script setup lang="ts">
  import {
    computed, inject, onUnmounted, ref, onMounted,
  } from 'vue'
  import { get } from 'lodash'
  import {
    Button, Tooltip, Icon, Space,
    toast2 as toast,
  } from '@xhs/delight'
  import { DataAskType } from '@agent/types'
  import { AgentMessage } from '@agent/types/message'
  import { useUserInfoStore } from '@xhs/redbi-share-stores'
  import { addListener } from '@xhs/redbi-share-utils'
  import { useContextStore } from '@agent/store/contextStore'
  import { useSessionStore } from '@agent/store/sessionStore'
  import { useDataAgentTrackerStore } from '@analysis/stores/dataAgentTrackerStore'
  import { list, DataAskItem } from './config'

  const props = defineProps<{
    message: AgentMessage
    mode: 'aside' | 'single'
  }>()

  const analysisTitle = ref('')
  const showRedPointFlag = ref(false)
  const contextStore = useContextStore()
  const sessionStore = useSessionStore()
  // 提问问题
  const queryText = computed(() => {
    // 获取上一个用户类型的问题
    const messages = sessionStore.currentSession?.messages
    if (messages) {
      const index = messages.findIndex(item => item.messageId === props.message.messageId)
      if (index > 0) {
        const lastMessage:any = messages[index - 1]
        if (lastMessage.role === 'user') {
          return lastMessage.content?.text || ''
        }
      }
    }
    return ''
  })

  const analysisQueryDsl = computed(() => contextStore.getQueryDsl(analysisId.value))

  const analysisId = computed(() => {
    const url = get(props.message.content, 'genAnalysisContext.assistantCompletion.analysisUrl')
    const _url = new URL(url)
    return Number(
      _url.searchParams.get('analysisId') || _url.searchParams.get('resourceId'),
    )
  })

  const disabled = computed(() => {
    if (props.mode === 'aside') {
      // 侧栏时, 不限制是否存在数据
      return !analysisQueryDsl.value?.hasData
    }
    return !analysisQueryDsl.value?.hasData
  })

  const removeUpdateAnalysisTitle = addListener('UPDATE_ANALYSIS_TITLE', ({ title, analysisId: _analysisId }: { title: string; analysisId: number }) => {
    if (_analysisId === analysisId.value) {
      analysisTitle.value = title
    }
  })

  onUnmounted(() => {
    removeUpdateAnalysisTitle()
  })

  // 获取数据提问上下文
  function getContext() {
    const queryDsl = analysisQueryDsl.value?.dsl
    const analysisUrl = get(props.message.content, 'genAnalysisContext.assistantCompletion.analysisUrl')
    if (!queryDsl) {
      return null
    }
    const { datasetId } = queryDsl
    return {
      title: (props.mode === 'single' && queryText.value) || analysisTitle.value,
      type: DataAskType.chart,
      analysisUrl,
      datasetId,
      queryDsl,
    }
  }

  const editorOperation = inject<{
    addContext:(data: any) => void
  }>('editorOperation')
  const userInfoStore = useUserInfoStore()
  const { handleTracker } = useDataAgentTrackerStore()

  const handleClick = (item: DataAskItem) => {
    const context = getContext()
    if (!context) {
      toast.danger('无法获取分析结果')
      return
    }

    // 通知 agent 输入框
    editorOperation?.addContext({
      context,
      input: item.value,
      focus: true,
      messageId: props.message.messageId,
      placeholder: item.placeholder,
    })

    if (item.title === '数据解读') {
      showRedPointFlag.value = true
      localStorage.setItem('SHOW_DATA_ASK_RED_POINT_NEW_NOTICE', 'true')
      // 数据解读按钮点击
      handleTracker('data_agent_data_question', {
        data_agent_email: userInfoStore.userInfo.userEmail,
        data_agent_time: `${new Date().getTime()}`,
        data_agent_data_question_source: 'agent',
        data_agent_btn_type: '数据解读',
      })
    }
  }

  const getRuntimeFlag = (item: DataAskItem) => {
    if (item.title !== '数据解读') return false
    if (disabled.value) return false
    if (showRedPointFlag.value) return false
    return true
  }

  onMounted(() => {
    showRedPointFlag.value = !!localStorage.getItem('SHOW_DATA_ASK_RED_POINT_NEW_NOTICE')
    if (!list.find(item => item.title === '数据解读')?.hidden) {
      // 数据解读按钮曝光
      handleTracker('data_agent_data_question', {
        data_agent_email: userInfoStore.userInfo.userEmail,
        data_agent_time: `${new Date().getTime()}`,
        data_agent_data_question_source: 'agent',
        data_agent_btn_type: '数据解读',
      }, 'IMPRESSION')
    }
  })
</script>
<style scoped lang="stylus">
.data-ask
  .data-ask-btn
    padding 0 6px
    // color #00000087
    &.disabled
      opacity 0.7
    position relative
    &.show-new-notice
      &::after
        content ''
        position absolute
        top 4px
        right 4px
        width 6px
        height 6px
        background-color var(--color-danger)
        border-radius 50%
        border 1px solid var(--color-white)
</style>
