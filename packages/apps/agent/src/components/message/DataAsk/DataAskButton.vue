<template>
  <div v-show="visible">
    <Tooltip
      content="向AI提问，让他帮你分析这份数据"
      :validate="()=>isDataAskBtnVisible||false"
    >
      <Button
        ref="dataAskBtnRef"
        type="secondary"
        class="data-ask-btn"
        @click="handleClick(analysisId, shortcutId)"
      > {{ isDataAskBtnVisible?'AI 数据解读':'AI 取数' }} <AgentStar class="agent-star" /></Button>
      <DataAskButtonNewNotice
        :target="dataAskBtnRef"
        :show="isDataAskBtnVisible && visible"
      />
    </Tooltip>
  </div>
  <Divider
    v-if="visible"
    style="height: 20px;"
    direction="vertical"
  />

  <!-- 隐藏的入口,用于打开侧栏 -->
  <div class="agent-entry-btn">
    <AgentEntry
      ref="agentEntryRef"
      :mode="'aside'"
      :user-info="userInfoStore?.userInfo"
      @toggle-aside="(value: boolean) => emits('toggle-aside', value)"
    />
  </div>
</template>
<script setup lang="ts">
  import {
    computed, nextTick, ref, watch,
  } from 'vue'
  import {
    Button, toast2 as toast, Tooltip, Divider,
  } from '@xhs/delight'
  import { publish } from '@xhs/redbi-share-utils'
  import { useContextStore } from '@agent/store/contextStore'
  import { DataAskType } from '@agent/types'
  import AgentStar from '@agent/components/iconSvg/star.vue'
  import { useChatbotDisplay } from '@analysis/hooks/useChatbotDisplay'
  import AgentEntry from '@agent/AgentEntry.vue'
  import { useUserInfoStore } from '@xhs/redbi-share-stores'
  import { useRoute } from 'vue-router'
  import { useAsideAgentStore } from '@analysis/stores/asideAgentStore'
  import { useDataAgentTrackerStore } from '@analysis/stores/dataAgentTrackerStore'

  import { list } from './config'
  import DataAskButtonNewNotice from './DataAskButtonNewNotice.vue'

  // 数据解读
  const dataAskItem = list.find(item => item.title === '数据解读')

  const contextStore = useContextStore()

  const props = defineProps<{
    analysisId: number
    analysisTitle: string
    projectId: number
    isSettingAvailable?: boolean
  }>()

  const emits = defineEmits<{(e: 'toggle-aside', value: boolean): void
  }>()

  const route = useRoute()
  const shortcutId = computed(() => route.query.shortcutId as string)
  const analysisQueryDsl = computed(() => contextStore.getQueryDsl(props.analysisId, shortcutId.value))
  const { isChatbotDisplay } = useChatbotDisplay()
  const userInfoStore = useUserInfoStore()
  const agentEntryRef = ref()
  const dataAskBtnRef = ref<InstanceType<typeof Button>>()
  const asideAgentStore = useAsideAgentStore()

  // 是否展示AI取数入口
  const isAgentEntry = computed(() => props.projectId === 4 && isChatbotDisplay.value && !asideAgentStore.isAsideOpen)

  // 是否展示 AI数据解读
  const isDataAskBtnVisible = computed(() => analysisQueryDsl.value?.hasData)

  const visible = computed(() => isAgentEntry.value || isDataAskBtnVisible.value)
  const { handleTracker } = useDataAgentTrackerStore()

  // 获取数据提问上下文
  function getContext(queryDsl?:any) {
    queryDsl = queryDsl || analysisQueryDsl.value?.dsl
    const analysisUrl = window.location.href
    if (!queryDsl) {
      return null
    }
    const { datasetId } = queryDsl
    return {
      title: props.analysisTitle,
      type: DataAskType.analysis,
      analysisUrl,
      datasetId,
      queryDsl,
    }
  }

  // 打开侧栏
  async function handleOpenAgent() {
    // 如果侧栏没有打开,打开侧栏
    if (!asideAgentStore.isAsideOpen) {
      const btn = document.getElementById('aside-agent-btn')
      if (btn) {
        btn.click()
      } else {
        // 如果侧栏按钮不存在,则等待100ms后重新打开侧栏
        agentEntryRef.value?.handleClick()
        return new Promise(resolve => {
          setTimeout(() => {
            handleOpenAgent()
            resolve(true)
          }, 100)
        })
      }
    }
    return Promise.resolve(true)
  }

  const handleClick = async (analysisId: number, _shortcutId: string) => {
    // 如果侧栏没有打开,打开侧栏
    await handleOpenAgent()

    // 数据解读按钮点击
    handleTracker('data_agent_data_question', {
      data_agent_email: userInfoStore.userInfo.userEmail,
      data_agent_time: `${new Date().getTime()}`,
      data_agent_data_question_source: 'analysis',
      data_agent_btn_type: !isDataAskBtnVisible.value ? 'AI取数' : '数据解读',
    })

    nextTick(() => {
      if (!isDataAskBtnVisible.value) {
        return
      }
      const context = getContext()
      if (!context) {
        toast.danger('无法获取分析结果')
        return
      }
      publish('ADD_DATA_ASK_CONTEXT', {
        context,
        getContext() {
          const queryDsl = contextStore.getQueryDsl(analysisId, _shortcutId)?.dsl
          // !锁定点击按钮时候的分析跟shortcutId, 避免用户切换分析跟shortcutId, 导致上下文不一致
          const _context = getContext(queryDsl)
          if (!_context) {
            return null
          }
          return _context
        },
        input: dataAskItem?.value,
        placeholder: dataAskItem?.placeholder,
        focus: true,
      })
    })
  }
  watch(() => props.isSettingAvailable, newVal => {
    if (newVal && typeof isDataAskBtnVisible.value === 'boolean') {
      setTimeout(() => {
        // 数据解读按钮曝光
        handleTracker('data_agent_data_question', {
          data_agent_email: userInfoStore.userInfo.userEmail,
          data_agent_time: `${new Date().getTime()}`,
          data_agent_data_question_source: 'analysis',
          data_agent_btn_type: !isDataAskBtnVisible.value ? '数据解读' : 'AI取数',
        }, 'IMPRESSION')
      }, 1000)
    }
  }, {
    immediate: true,
  })
</script>
<style lang="stylus" scoped>
  .data-ask-btn-wrapper
    position relative
  .data-ask-btn {
    position relative
    padding-right 20px !important
    .agent-star {
      position absolute
      top 8px
      right 10px
    }
    &.disabled {
      opacity: 0.5;
    }
  }
  .agent-entry-btn
    width 0
    height 0
    overflow hidden
    opacity 0
    position absolute
</style>
