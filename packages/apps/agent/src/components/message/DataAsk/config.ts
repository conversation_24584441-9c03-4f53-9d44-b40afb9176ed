import DataAskIcon from './icons/DataAskIcon.vue'
import DataInterpretationIcon from './icons/DataInterpretationIcon.vue'
import FactorIcon from './icons/FactorIcon.vue'
import TrendIcon from './icons/TrendIcon.vue'

export type DataAskItem = {
  title: string
  icon: any
  tooltip: string
  value?: string
  placeholder?: string
  hidden?: boolean
}

export const list: DataAskItem[] = [
  {
    title: '数据提问',
    icon: DataAskIcon,
    tooltip: '向AI提问，让他帮你分析这份数据',
    value: '你可以对于这份数据提问，如销量最高的商品是哪个',
    hidden: true,
  },
  {
    title: '数据解读',
    icon: DataInterpretationIcon,
    tooltip: '让AI解读一下这份数据',
    value: '请帮我解读这份数据，包括数据概览、主要发现、指导建议',
    placeholder: '你可以继续向我提问，如销量最高的商品是哪个',
  },
  {
    title: '波动归因',
    icon: FactorIcon,
    tooltip: '让AI分析下指标波动的原因',
    hidden: true,
  },
  {
    title: '趋势预测',
    icon: TrendIcon,
    tooltip: '让AI预测下未来的数据',
    hidden: true,
  },
].filter(item => !item.hidden)
