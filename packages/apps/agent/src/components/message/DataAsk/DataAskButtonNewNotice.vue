<template>
  <Popover
    :target="target?.$el"
    trigger="manual"
    :visible="visible"
    arrow
  >
    <template #content>
      <Space
        class="data-ask-button-new-notice"
        direction="vertical"
        :size="12"
        align="start"
      >
        <Space
          class="title"
          :size="8"
        >
          <Tag
            color="blue"
            size="small"
          >NEW</Tag>
          <Text
            type="h5"
            bold
          >AI 数据解读</Text>
        </Space>
        <Space class="content">
          <Text class="content-text">试一试让AI帮你解读这份数据，也可自由提问～</Text>
        </Space>
        <Space
          justify="end"
          style="width: 100%"
        >
          <Button
            type="primary"
            @click="handleClose"
          >我知道了</Button>
        </Space>
      </Space>
    </template>
  </Popover>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import {
    Space, Button, Text, Tag, Popover,
  } from '@xhs/delight'

  const props = defineProps<{
    show?: boolean
    target?: InstanceType<typeof Button>
  }>()

  const FLAG_KEY = 'SHOW_DATA_ASK_BUTTON_NEW_NOTICE'
  const visible = ref(false)

  watch(
    [() => props.target, () => props.show],
    ([target, show]) => {
      if (show && target?.$el) {
        const flag = !!localStorage.getItem(FLAG_KEY)
        if (!flag) {
          visible.value = true
          localStorage.setItem(FLAG_KEY, 'true')
        }
      }
    },
    { immediate: true },
  )

  const handleClose = () => {
    visible.value = false
  }
</script>

<style lang="stylus" scoped>
  .data-ask-button-new-notice
    width 300px
    padding 16px 20px
    .content-text
      font-size 14px
      line-height 22px
      color rgba(0, 0, 0, 0.7)
</style>
