<template>
  <Space
    class="thinking-process-container"
    direction="vertical"
    align="start"
  >
    <Steps direction="vertical">
      <Step
        v-for="(node, index) in nodes"
        :key="node.key"
        :class="{
          'thinking-process-step-success': node.state === ThinkingProcessNodeState.SUCCESS,
        }"
      >
        <template #icon>
          <StepIcon :state="node.state" />
        </template>
        <template #title>
          <Text
            color="text-title"
            bold
            :style="{
              cursor: 'pointer',
              marginLeft: '4px',
            }"
            @click="() => triggerOpen(node)"
          >
            {{ node.title }}
            <span
              v-if="node.content"
            >
              <Icon
                :icon="Right"
                :style="{
                  transform: fold[node.key] ? 'rotate(0deg)' : 'rotate(90deg)',
                }"
              />
            </span>
          </Text>
        </template>
        <template #description>
          <div
            :style="{
              display: fold[node.key] ? 'none' : 'block',
            }"
          >
            <slot
              :name="getSlotName(node)"
              :node="node"
              :index="index"
            >
              <Space
                v-if="
                  typeof node.content === 'object' &&
                    node.content !== null &&
                    JSON.stringify(node.content) !== '{}'
                    && getSlotName(node) === 'pickDataContext'
                "
                direction="vertical"
                align="start"
                size="2px"
                style="width: 100%"
              >
                <Space align="start">
                  <Text
                    color="text-description"
                    class="field-title"
                  >维度</Text>
                  <Space
                    size="0"
                    wrap
                  >
                    <Text
                      v-if="!getDimensions(node.content)?.length"
                      color="primary"
                      bold
                    >-</Text>
                    <Text
                      v-for="(item, index) in getDimensions(node.content)"
                      v-else
                      :key="typeof item === 'string' ? item : item['字段名称']"
                      color="primary"
                      class="field-name"
                      bold
                    >
                      {{ typeof item === 'string' ? item : item['字段名称'] }}{{ index !== (getDimensions(node.content)?.length || 0) - 1 ? '、' : '' }}
                    </Text>
                  </Space>
                </Space>
                <Space align="start">
                  <Text
                    color="text-description"
                    class="field-title"
                  >指标</Text>
                  <Space
                    size="0"
                    wrap
                  >
                    <Text
                      v-for="(item, index) in getMeasures(node.content)"
                      :key="typeof item === 'string' ? item : item['字段名称']"
                      color="success"
                      class="field-name"
                      bold
                    >
                      {{ typeof item === 'string' ? item : `${item['字段名称']}${item['聚合方式'] ? `(${item['聚合方式']})` : ''}` }}{{ index !== (getMeasures(node.content)?.length || 0) - 1 ? '、' : '' }}
                    </Text>
                  </Space>
                </Space>
                <Space align="start">
                  <Text
                    color="text-description"
                    class="field-title"
                  >筛选</Text>
                  <Space
                    size="0"
                    wrap
                  >
                    <Text
                      v-for="(item, index) in getFilters(node.content)"
                      :key="typeof item === 'string' ? item : item['字段名称']"
                      color="primary"
                      class="field-name"
                      bold
                      ellipsis
                      tooltip
                      :style="{ maxWidth: '200px' }"
                    >
                      {{ formatFilterItem(item) }}{{ index !== (getFilters(node.content)?.length || 0) - 1 ? '、' : '' }}
                    </Text>
                  </Space>
                </Space>
              </Space>
              <Text v-else-if="getSlotName(node) === 'pickDataContext'">{{ node.content }}</Text>
            </slot>
          </div>
        </template>
      </Step>
    </Steps>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Steps, Step, Space, Text, Icon,
  } from '@xhs/delight'
  import { Right } from '@xhs/delight/icons'
  import { ThinkingProcessNode, ThinkingProcessNodeState } from '@agent/types/message'
  import StepIcon from './StepIcon.vue'

  const fold = ref<Record<string, boolean>>({})
  const triggerOpen = (node: ThinkingProcessNode) => {
    fold.value[node.key] = !fold.value[node.key]
  }

  withDefaults(
    defineProps<{
      nodes?: ThinkingProcessNode[]
    }>(),
    {
      nodes: () => [],
    },
  )

  // 兼容不同格式的维度数据
  const getDimensions = (content: any) => {
    if (!content) return []

    // 新格式：直接的字符串数组
    if (content['维度'] && Array.isArray(content['维度'])) {
      return content['维度']
    }

    // 旧格式：对象数组
    if (content.dimensions && Array.isArray(content.dimensions)) {
      return content.dimensions
    }

    return []
  }

  // 兼容不同格式的指标数据
  const getMeasures = (content: any) => {
    if (!content) return []

    // 新格式：直接的字符串数组
    if (content['指标'] && Array.isArray(content['指标'])) {
      return content['指标']
    }

    // 旧格式：对象数组
    if (content.measures && Array.isArray(content.measures)) {
      return content.measures
    }

    return []
  }

  // 兼容不同格式的筛选条件数据
  const getFilters = (content: any) => {
    if (!content) return []

    // 新格式：直接的字符串数组
    if (content['筛选条件'] && Array.isArray(content['筛选条件'])) {
      return content['筛选条件']
    }

    // 旧格式：对象数组
    if (content.filters && Array.isArray(content.filters)) {
      return content.filters
    }

    return []
  }

  // 格式化筛选条件项
  const formatFilterItem = (item: any) => {
    // 如果是字符串，直接返回
    if (typeof item === 'string') {
      return item
    }

    // 如果是对象，根据字段组装显示内容
    if (typeof item === 'object' && item !== null) {
      const fieldName = item['字段名称']
      const matchType = item['匹配类型']
      const condition = item['条件']

      // 只有字段名称的情况
      if (fieldName && !matchType && !condition) {
        return fieldName
      }

      // 有字段名称和匹配类型但没有条件的情况
      if (fieldName && matchType && !condition) {
        return `${fieldName}-${matchType}`
      }

      // 完整的情况：字段名称-匹配类型-条件
      if (fieldName && matchType && condition) {
        // 手动处理condition过长的情况 展示前三个后面使用等代替
        if (matchType === 'in' || matchType === 'not in') {
          const conditionArr = condition.split(' ')
          if (conditionArr.length > 3) {
            return `${fieldName}-${matchType}-${conditionArr.slice(0, 3).join(' ')}等)`
          }
          return `${fieldName}-${matchType}-${condition}`
        }
        return `${fieldName}-${matchType}-${condition}`
      }

      // 其他情况，只返回字段名称或空字符串
      return fieldName || ''
    }

    return ''
  }

  const getSlotName = (node: ThinkingProcessNode) => {
    // 如果有 stepType 字段，根据它来决定槽名称
    if (node.stepType === '取数') {
      return 'pickDataContext'
    }
    // 其他分析类型都使用 analysisContext 槽
    if (node.stepType) {
      return 'analysisContext'
    }

    // 兼容旧的 key 格式
    if (['pickDataContext', 'fieldsContext'].includes(node.key)) {
      return 'pickDataContext'
    }
    if (node.key.includes('analysisContext')) {
      return 'analysisContext'
    }

    // 默认返回原来的 key
    return node.key
  }
</script>

<style scoped lang="stylus">
  .thinking-process-container
    // background-color: #FFF;
    width: 100%;
    margin-bottom: 16px;
    position: relative;
    left: -6px;
    .field-title
      width 24px
      white-space nowrap
    .field-name
      white-space nowrap
    .thinking-process-description
            margin-top 4px
            margin-bottom 4px
    .thinking-process-step-success
        :deep(.d-steps-item-head .d-steps-item-line)
            color: #00000014;
            background-color: #00000014;
        :deep(.d-icon.--color-primary)
            color: #00b940;
    :deep(.d-steps-item)
        margin-bottom 8px
    :deep(.d-steps-item-main)
        flex 1
    :deep(.d-steps-item-description)
        position relative
    :deep(.d-steps-item:last-child)
        margin-bottom 0
    :deep(.d-steps-item-vertical .d-steps-item-head .d-steps-item-line)
        top 26px
        bottom 2px
    :deep(.d-steps-item .d-steps-item-head .d-steps-item-presets-icon)
        position relative
        width 12px
        height 12px
        top 4px
        left 6px
    :deep(.d-steps-item .d-steps-item-head .d-steps-item-icon)
        position relative
        width 12px
        height 12px
        top 4px
        left 6px
    :deep(.d-steps-item .d-steps-item-head .d-steps-item-presets-icon-success)
        color: #386bff;
        background-color: #386bff;
    :deep(.d-steps-item-main)
        max-width 100%
</style>
