<template>

  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_1937_28681)">
      <foreignObject
        x="-4.52906"
        y="-4.52906"
        width="41.0581"
        height="41.0581"
      ><div
        xmlns="http://www.w3.org/1999/xhtml"
        style="backdrop-filter:blur(2.26px);clip-path:url(#bgblur_1_1937_28681_clip_path);height:100%;width:100%"
      /></foreignObject><g
        filter="url(#filter0_i_1937_28681)"
        data-figma-bg-blur-radius="4.52906"
      >
        <path
          d="M0 3.2C0 1.43269 1.34315 0 3 0H29C30.6569 0 32 1.43269 32 3.2V28.8C32 30.5673 30.6569 32 29 32H3C1.34315 32 0 30.5673 0 28.8V3.2Z"
          fill="#E2E2E2"
        />
      </g>
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.6138 6.45312C11.6138 5.90084 12.0615 5.45312 12.6138 5.45312H13.6138C14.1661 5.45312 14.6138 5.90084 14.6138 6.45312V20.9531C14.6138 21.2293 14.39 21.4531 14.1138 21.4531H12.1138C11.8377 21.4531 11.6138 21.2293 11.6138 20.9531V6.45312ZM5.61382 15.4526C5.61382 14.9004 6.06153 14.4526 6.61382 14.4526H7.61382C8.1661 14.4526 8.61382 14.9004 8.61382 15.4526V20.9526C8.61382 21.2288 8.38996 21.4526 8.11382 21.4526H6.11382C5.83767 21.4526 5.61382 21.2288 5.61382 20.9526V15.4526ZM18.6138 10.4531C18.0615 10.4531 17.6138 10.9008 17.6138 11.4531V20.9531C17.6138 21.2293 17.8377 21.4531 18.1138 21.4531H20.1138C20.39 21.4531 20.6138 21.2293 20.6138 20.9531V11.4531C20.6138 10.9008 20.1661 10.4531 19.6138 10.4531H18.6138ZM23.6138 16.4531C23.6138 15.9008 24.0615 15.4531 24.6138 15.4531H25.6138C26.1661 15.4531 26.6138 15.9008 26.6138 16.4531V20.9531C26.6138 21.2293 26.39 21.4531 26.1138 21.4531H24.1138C23.8377 21.4531 23.6138 21.2293 23.6138 20.9531V16.4531ZM5 23.748C4.44772 23.748 4 24.1958 4 24.748V25.548C4 26.1003 4.44772 26.548 5 26.548H27C27.5523 26.548 28 26.1003 28 25.548V24.748C28 24.1958 27.5523 23.748 27 23.748H5Z"
        fill="white"
      />
    </g>
    <defs>
      <filter
        id="filter0_i_1937_28681"
        x="-4.52906"
        y="-4.52906"
        width="41.0581"
        height="41.0581"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood
          flood-opacity="0"
          result="BackgroundImageFix"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="31.7034" />
        <feComposite
          in2="hardAlpha"
          operator="arithmetic"
          k2="-1"
          k3="1"
        />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_1937_28681"
        />
      </filter>
      <clipPath
        id="bgblur_1_1937_28681_clip_path"
        transform="translate(4.52906 4.52906)"
      ><path d="M0 3.2C0 1.43269 1.34315 0 3 0H29C30.6569 0 32 1.43269 32 3.2V28.8C32 30.5673 30.6569 32 29 32H3C1.34315 32 0 30.5673 0 28.8V3.2Z" />
      </clipPath><clipPath id="clip0_1937_28681">
        <rect
          width="32"
          height="32"
          fill="white"
        />
      </clipPath>
    </defs>
  </svg>

</template>
  <script setup lang="ts">

  </script>
