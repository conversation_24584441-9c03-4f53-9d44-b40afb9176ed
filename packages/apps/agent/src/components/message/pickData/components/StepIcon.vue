<template>

  <!-- 加载中 -->
  <Icon
    v-if="state === ThinkingProcessNodeState.RUNNING"
    size="small"
    class="d-spin thinking-process-icon"
    color="primary"
  >
    <IconLoading />
  </Icon>
  <!-- 成功 -->
  <Icon
    v-else-if="state === ThinkingProcessNodeState.SUCCESS"
    size="small"
    class="thinking-process-icon"
    color="primary"
  >
    <IconSuccess />
  </Icon>
  <!-- 失败 -->
  <Icon
    v-else-if="state === ThinkingProcessNodeState.FAIL"
    size="small"
    class="thinking-process-icon"
    color="danger"
  >
    <IconInit />
  </Icon>
  <!-- 停止 -->
  <Icon
    v-else-if="state === ThinkingProcessNodeState.STOPPED"
    size="small"
    class="thinking-process-icon"
    color="text-disabled"
  >
    <IconStop />
  </Icon>
  <!-- 未加载 -->
  <Icon
    v-else
    size="small"
    class="thinking-process-icon"
    color="text-disabled"
  >
    <IconInit />
  </Icon>
</template>

<script setup lang="ts">
  import { Icon } from '@xhs/delight'
  import {
    ThinkingProcessNodeState,
  } from '@agent/types/message'
  import IconLoading from './icon/Loading.vue'
  import IconSuccess from './icon/Success.vue'
  import IconInit from './icon/Init.vue'
  import IconStop from './icon/Stop.vue'

  defineProps({
    state: {
      type: String,
      default: '',
    },
  })
</script>
<style lang="stylus" scoped>
.thinking-process-icon
    position: relative;
    width: 12px;
    height: 12px;
    top: 4px;
    left: 6px;
</style>
