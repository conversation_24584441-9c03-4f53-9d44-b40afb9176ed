<template>
  <div class="preview-wrapper">
    <div
      v-if="analysisUrl"
      ref="iframeContainer"
      class="preview-page-con"
    >
      <Button
        v-show="!!initAnalysisId"
        size="small"
        type="light"
        :icon="FullScreenOne"
        class="preview-page-con-btn"
        :style="{
          position: 'absolute',
          right: '42px',
          top: '2px',
          zIndex: 1,
        }"
        @click="visible = true"
      />
      <!-- 直接使用 AnalysisChart 组件 -->
      <div
        v-if="shouldLoad && !!initAnalysisId"
        class="analysis-chart-container"
        :style="{
          width: '100%',
          height: `${dynamicHeight}px`,
          border: '0',
          boxSizing: 'border-box',
          overflow: 'auto',
        }"
      >
        <AnalysisChart
          :id="chartId"
          :key="`${componentKey}-${initAnalysisId}`"
          :setting="chartSetting"
          :filters="[]"
          render-mode="preview"
          :show-operation="true"
          :page-from="pageFrom"
        />
      </div>
    </div>
  </div>
  <!-- 全屏预览使用 AnalysisChart 组件 -->
  <Modal
    v-model:visible="visible"
    :title="title"
    class="preview-modal"
    full-screen
    :with-footer="false"
    :z-index="9999"
    @cancel="handleClose"
  >
    <div
      class="chart-preview-con"
      style="height: 100%"
    >
      <AnalysisChart
        v-if="!!initAnalysisId"
        :id="`agent-analysis-fullscreen-${initAnalysisId}`"
        :key="`fullscreen-${componentKey}-${initAnalysisId}`"
        :setting="chartSetting"
        :filters="[]"
        render-mode="preview"
        :show-operation="false"
        :style="{
          width: '100%',
          height: '100%',
          border: '0',
          boxSizing: 'border-box',
        }"
        allow="clipboard-read; clipboard-write"
      />
    </div>
  </Modal>
</template>

<script setup lang="ts">
  import {
    ref, onMounted, watch, onUnmounted,
    nextTick, computed,
  } from 'vue'
  import {
    Modal, Button,
  } from '@xhs/delight'
  import { FullScreenOne } from '@xhs/delight/icons'
  import { AnalysisChart } from '@dashboard/components/charts'
  import { addListener, removeListener } from '@xhs/redbi-share-utils'
  import { useAgentStore } from '@agent/store/agentStore'

  // const origin = window.location.origin
  const props = defineProps<{
    analysisUrl: string
    title?: string
    messageId?: string | number
    pageFrom?: string
  }>()

  const emit = defineEmits<{(e: 'chart-rendered', messageId: string | number): void
  }>()

  const initAnalysisId = ref()
  const initShortcutId = ref()
  const projectId = ref(4)

  const iframeContainer = ref()
  const shouldLoad = ref(false)
  const observer = ref<IntersectionObserver | null>(null)

  const analysisEditRef = ref()
  const visible = ref(false)
  const handleClose = () => {
    visible.value = false
  }

  // 动态高度相关
  const dynamicHeight = ref(400)
  const chartId = computed(() => `agent-analysis-${initAnalysisId.value}`)
  const agentStore = useAgentStore()

  // 添加组件重新渲染的 key，用于强制重新渲染
  const componentKey = ref(0)

  // 监听模式变化，强制重新渲染图表
  watch(() => agentStore.mode, (newMode, oldMode) => {
    if (oldMode === 'aside' && newMode === 'single') {
      // 从侧边模式切换到全屏模式时，强制重新渲染
      componentKey.value += 1
      // 重置高度
      dynamicHeight.value = 400
      // 强制设置 shouldLoad 为 true，确保图表能够渲染
      shouldLoad.value = true
    }
  })

  // 监听 shouldLoad 和 initAnalysisId 的变化，当图表开始渲染时立即通知父组件
  watch([() => shouldLoad.value, () => initAnalysisId.value], ([newShouldLoad, newAnalysisId]) => {
    if (newShouldLoad && newAnalysisId && props.messageId) {
      // 图表开始渲染，立即通知父组件
      emit('chart-rendered', props.messageId)
    }
  })

  const heightListenerCallback = (data: any) => {
    // 严格的事件隔离 - 只处理属于当前图表的事件
    const currentAnalysisId = initAnalysisId.value
    if (!currentAnalysisId) return
    // 检查图表ID是否匹配
    if (data.chartId && data.chartId !== chartId.value) {
      return
    }

    // 检查分析ID是否匹配
    if (data.analysisId && data.analysisId !== currentAnalysisId.toString()) {
      return
    }

    let renderSize = 400
    if (data.maxY <= 0) return

    // 只有在内容高度较小时才应用动态高度（通常是表格类型）
    // 如果原始高度很大，说明是折线图等需要固定高度的图表
    const shouldApplyDynamicHeight = data.maxY < 300 // 原始高度小于300认为是表格类型

    if (!shouldApplyDynamicHeight) {
      // 内容高度较大的图表（如折线图、柱状图等）保持固定高度
      renderSize = 400
    } else if (data.showPagination) {
      // 内容高度较小的图表（如表格）- 有分页时保持固定高度
      renderSize = 400
    } else if (data.maxY + 70 < 400) {
      // 内容高度较小的图表（如表格）- 无分页时应用动态高度
      renderSize = data.maxY + 70
    } else {
      // 内容高度较小但仍然超过400px的图表
      renderSize = 400
    }

    dynamicHeight.value = renderSize

    // 通知父组件图表已渲染完成
    if (props.messageId) {
      emit('chart-rendered', props.messageId)
    }
  }

  // 构造图表设置
  const chartSetting = computed(() => {
    if (!initAnalysisId.value) return {}

    return {
      analysisId: initAnalysisId.value,
      style: {
        common: {
          title: {
            text: '分析预览',
            enable: true,
            fontSize: 14,
          },
        },
      },
      data: {
        datasetId: null,
      },
      advanced: {},
    }
  })

  watch(
    () => props.analysisUrl,
    val => {
      if (!val) {
        initAnalysisId.value = undefined
        initShortcutId.value = undefined
        dynamicHeight.value = 400 // 重置高度
        shouldLoad.value = false // 重置加载状态
      } else {
        const url = new URL(val)
        initAnalysisId.value = Number(
          url.searchParams.get('analysisId') || url.searchParams.get('resourceId'),
        )
        initShortcutId.value = Number(url.searchParams.get('shortcutId')) || undefined
        projectId.value = Number(url.searchParams.get('projectId'))
        dynamicHeight.value = 400 // 重置高度
        componentKey.value += 1 // 强制重新渲染

        if (analysisEditRef.value) {
          analysisEditRef.value.jumpToTemplate({
            analysisId: Number(initAnalysisId.value),
            shortcutId: initShortcutId.value,
          })
        }
      }

      // 当 analysisUrl 变化时，检查容器并设置观察
      nextTick(() => {
        if (iframeContainer.value && !shouldLoad.value) {
          observer.value?.observe(iframeContainer.value)
        }
      })
    }, { immediate: true },
  )

  onMounted(() => {
    // 创建 Intersection Observer（提前创建，避免重复创建）
    observer.value = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          shouldLoad.value = true
          observer.value?.unobserve(entry.target)
        }
      })
    }, {
      rootMargin: '100px',
      threshold: 0.1,
    })

    // 添加高度监听器
    addListener('S2_LAYOUT_RENDER_HEIGHT', heightListenerCallback)
  })

  onUnmounted(() => {
    observer.value?.disconnect()
    removeListener('S2_LAYOUT_RENDER_HEIGHT', heightListenerCallback)
  })
</script>

<style lang="stylus" scoped>
  .preview-wrapper
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;

  .preview-page-con
    background: #ffffff;
    border: 1px solid rgb(243, 244, 245);
    border-radius: 4px;
    width: 100%;
    position relative
    display flex
    flex-direction column
    align-items center
    overflow: hidden;

  .analysis-chart-container
    padding: 2px 10px;

  .preview-page-con-btn
    :deep(.d-icon.--size-icon-default)
      width 14px
      height 14px
      line-height 14px
</style>
