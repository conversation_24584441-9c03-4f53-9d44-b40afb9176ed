import { transferDataAskMessage } from '@agent/utils/dataAsk'
import { AgentFetchResponseContent, ThinkingProcessNodeState } from '@agent/types/message'

export interface TransferPickDataDeps {
  aiType: number
  mode: string
  setDataSetType: (value: number) => void
  defaultDatasetType: number
  getMessageTitle: (...args: any[]) => any
  transferMessageToView: (evaluateContext: AgentFetchResponseContent, query?: string) => any
}

// 从历史会话恢复时，转换服务端消息为前端可用结构
export const transferPickDataSessionToView = (res: any[], deps: TransferPickDataDeps): {
  sessionId: number
  type: 'data'
  messages: any[]
  status: 'EDITING'
} => {
  const {
    aiType,
    mode,
    setDataSetType,
    defaultDatasetType,
    getMessageTitle,
    transferMessageToView,
  } = deps
  const lastItem = res[res.length - 1]
  if (lastItem?.content?.analysisScene === 'FOLLOWUP_QUERY' && lastItem?.content?.role === 'user') {
    res.push({
      content: null,
      messageId: -Number(lastItem.messageId),
      role: 'assistant',
      dataAsk: true,
      sessionId: lastItem.sessionId,
      sessionType: lastItem.sessionType,
      isHistory: true,
      traceId: lastItem.traceId,
    })
  }

  const list = res.map((item: any) => {
    // 内容为空的情况
    if (item.content === null) {
      return {
        messageId: item.messageId,
        role: 'assistant',
        sessionId: item.sessionId,
        status: 'FAIL',
        title: item.dataAsk ? '数据解读' : '思考过程',
        type: 'text',
        errorMsg: '会话未成功完成',
        errorType: item.dataAsk ? 'DATA_ASK_HISTORY_ERROR' : 'SESSION_NOT_COMPLETE',
        content: {
          type: item.dataAsk ? 'dataAsk' : 'markdown',
          text: '',
          reasoningContent: null,
        },
        isHistory: true,
        traceId: item.traceId,
      }
    }

    if (item?.content?.role === 'user') {
      if (item.content?.analysisScene === 'FOLLOWUP_QUERY') {
        // 提问
        return {
          content: {
            text: item?.content?.content,
            type: 'markdown',
            askContext: [{
              title: item?.content?.context?.analysisName,
              type: mode === 'single' ? 1 : 2,
              queryDsl: null,
              analysisUrl: item?.content?.context?.analysisUrl,
            }],
          },
          messageId: item.messageId,
          role: 'user',
          sessionId: item.sessionId,
          status: 'SUCCESS',
          title: '思考过程',
          type: 'text',
          traceId: item.traceId,
        }
      }
      // 从历史会话恢复时，设置数据集类型
      setDataSetType(item?.content?.projectId || Number(localStorage.getItem('agent_dataset_type')) || defaultDatasetType)
      return {
        content: {
          text: item?.content?.content,
          type: 'markdown',
        },
        messageId: item.messageId,
        role: item?.content?.role || 'assistant',
        sessionId: item.sessionId,
        status: 'SUCCESS',
        title: '思考过程',
        type: 'text',
        isHistory: true,
        traceId: item.traceId,
      }
    }

    // 是数据解读
    if (item.content?.analysisScene === 'FOLLOWUP_QUERY') {
      // 回答
      let dataAskMessage: any = {
        messageId: item.messageId,
        role: 'assistant',
        sessionId: item.sessionId,
        status: 'SUCCESS',
        title: '数据解读',
        type: 'text',
        content: {
          type: 'dataAsk',
          text: '',
          reasoningContent: null,
        },
        isHistory: true,
        traceId: item.traceId,
      }
      try {
        const content = JSON.parse(item.content.content)
        if (content.warning) {
          const { message, type } = transferDataAskMessage(content.warning)
          if (type === 'warn') {
            dataAskMessage.warnMsg = message
          }
        }
        dataAskMessage = {
          ...dataAskMessage,
          content: {
            text: content.content,
            type: 'dataAsk',
            reasoningContent: content.reasoningContent !== '' ? {
              enable: true,
              key: 'reasoningContent',
              content: content.reasoningContent,
              title: '深度思考',
              state: ThinkingProcessNodeState.SUCCESS,
            } : null,
          },
        }
      } catch (error) {
        dataAskMessage.content.text = ''
        if (item.content?.context?.errorMessage) {
          const { message, type } = transferDataAskMessage(item.content?.context?.errorMessage)
          if (type === 'error') {
            dataAskMessage.errorMsg = message
          }
        }
        dataAskMessage = {
          ...dataAskMessage,
          status: 'FAIL',
          errorMsg: dataAskMessage.errorMsg || '数据解读失败',
          errorType: 'DATA_ASK_HISTORY_ERROR',
        }
      }
      return dataAskMessage
    }

    const transferMessage = item.content?.aiContent?.evaluateContext ? transferMessageToView(item.content?.aiContent?.evaluateContext as AgentFetchResponseContent, item.content?.aiContent?.query) : {
      type: 'pickData',
      query: item.content?.aiContent?.query,
      understandingIntentionContext: {
        status: 'RUNNING',
      },
    }

    const parsedContent = JSON.parse((transferMessage as AgentFetchResponseContent)?.understandingAnalysisContext?.assistantCompletion?.llmContent || '{}')

    const current: any = {
      content: {
        ...transferMessageToView(item.content?.aiContent?.evaluateContext, item.content?.aiContent?.query),
        type: 'pickData',
      },
      clarificationQuestion: parsedContent['澄清提问'],
      messageId: item.messageId,
      role: item?.content?.role || 'assistant',
      sessionId: item.sessionId,
      status: item.content?.aiContent?.status || 'SUCCESS',
      title: getMessageTitle(aiType, item.content?.aiContent?.status) || '思考过程',
      errorMsg: item.content?.aiContent?.errorMsg,
      errorType: item.content?.aiContent?.errorType,
      feedbackInfo: item?.feedbackInfo,
      type: 'text',
      isHistory: true,
      traceId: item.traceId,
    }
    // 是否澄清 添加推荐问题
    if (item.content?.aiContent?.status === 'CLARIFICATION') {
      try {
        const suggestedQuestions = JSON.parse(item.content?.aiContent?.evaluateContext?.understandingAnalysisContext?.assistantCompletion?.llmContent)['建议问题']
        if (suggestedQuestions && Array.isArray(suggestedQuestions)) {
          current.recommendQuestion = suggestedQuestions.map((curItem: string) => ({
            question: curItem,
            category: '建议问题',
          }))
        }
      } catch (error) {
        console.error('transferSessionToView', error)
      }
    }

    return current
  })

  return {
    sessionId: res[0].sessionId,
    type: 'data',
    messages: list,
    status: 'EDITING',
  }
}
