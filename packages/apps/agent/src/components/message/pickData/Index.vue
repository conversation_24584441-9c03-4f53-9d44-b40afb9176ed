<template>
  <div class="pick-data-container">
    <!-- {{ sessionStore.currentSession?.messages }} -->
    <!-- {{ showNodes }} -->
    <!-- {{ sessionStore.currentSession?.status }} -->
    <!-- 思考过程 -->
    <ThinkingProcess :nodes="showNodes">
      <template #understandingIntentionContext="{ node }">
        <Space
          v-show="node.content"
          direction="horizontal"
          align="start"
          size="4px"
          class="thinking-process-description"
        >
          <Text
            color="text-description"
            v-html="formatTextWithBrackets(node.content)"
          />
          <Text
            v-if="isShowReclaim"
            style="margin-left: 4px; color: #007AFF; cursor: pointer; white-space: nowrap; display: flex; align-items: center; gap: 2px;"
            bold
            @click="emit('reclaim', node.content as string, message.messageId as number)"
          >
            纠正
            <Arrow />
          </Text>
        </Space>
      </template>
      <template #selectOneDatasetContext="{ node }">
        <Space
          v-show="node.content"
          direction="vertical"
          align="start"
          size="2px"
          class="thinking-process-description"
        >
          <Tag
            v-if="node.content"
            size="small"
            :style="{
              padding: '4px 6px',
            }"
          >
            <Link
              v-if="node.link"
              :href="node.link"
              muted
              size="small"
              target="_blank"
              style="display: flex; align-items: center;"
            >
              <Icon
                size="small"
                style="margin: 0px 8px 4px 2px;"
              >
                <DatasetSvg />
              </Icon>
              <Text
                color="text-paragraph"
                style="padding: 0px 2px;"
              >
                {{ node.content }}
              </Text>
              <Icon
                size="small"
                color="text-paragraph"
                style="margin: 0px 4px 1px 4px;"
              >
                <ShareSvg />
              </Icon>
            </Link>
            <Text
              v-else
              color="text-paragraph"
            >
              {{ node.content }}
            </Text>
          </Tag>
        </Space>
      </template>
    </ThinkingProcess>
    <!-- {{ analysisUrl }} -->
    <!-- 判断当前模式 single aside -->
    <template v-if="currentMode === 'single'">
      <!-- 分析结果图表 -->
      <AnalysisPreview
        v-show="analysisUrl"
        :analysis-url="analysisUrl"
        :message-id="message.messageId"
        @chart-rendered="handleChartRendered"
      />
      <!-- 在自助分析打开 -->
      <Space
        v-show="analysisUrl && message.isChartRendered && singleChartRendered"
        class="open-analysis-button"
        @click="openAnalysis"
      >
        <Text
          color="text-paragraph"
          bold
        >
          在自助分析打开
        </Text>
        <Icon
          style="width: 14px; height: 14px;line-height: 14px;"
          color="text-paragraph"
        >
          <ShareSvg />
        </Icon>
      </Space>
    </template>
    <Space
      v-if="currentMode === 'aside' && analysisUrl"
      direction="vertical"
      align="center"
      justify="center"
      class="aside-mode-card"
      size="12px"
    >
      <Chart />
      <Text color="text-paragraph">
        侧边模式请在左侧查看图表
      </Text>
    </Space>
  </div>
</template>
<script  setup lang="ts">
  import {
    ref, computed, nextTick, onMounted, onUnmounted,
  } from 'vue'
  import { useSessionStore } from '@agent/store/sessionStore'
  import {
    AgentFetchResponseContent, AgentMessage, ThinkingProcessNode, ThinkingProcessNodeState,
  } from '@agent/types/message'
  import { get } from 'lodash'
  import { useTimeoutPoll } from '@vueuse/core'

  import {
    Tag, Link, Icon, Text, Space, toast2 as toast,
  } from '@xhs/delight'
  import { useRoute } from 'vue-router'
  import { useAgentStore } from '@agent/store/agentStore'
  import ShareSvg from '@agent/components/iconSvg/ShareSvg.vue'
  import DatasetSvg from '@agent/components/iconSvg/DatasetSvg.vue'
  import Chart from './components/icon/Chart.vue'
  import Arrow from './components/icon/Arrow.vue'
  import ThinkingProcess from './components/ThinkingProcess.vue'
  import AnalysisPreview from './components/AnalysisPreview.vue'

  const props = defineProps<{
    message: AgentMessage
  }>()
  const emit = defineEmits<{(e: 'generate-complete'): void
                            (e: 'switch-analysis', options: { analysisId: number; shortcutId?: string }): void
                            (e: 'reclaim', content: string, messageId: number): void
                            (e: 'child-mounted', messageId: number): void
                            (e: 'chart-rendered', payload: { messageId: number; analysisUrl: string; data: any }): void
  }>()
  const agentStore = useAgentStore()

  const route = useRoute()
  const sessionStore = useSessionStore()
  const isShowReclaim = computed(() => true)
  const analysisUrl = ref('')
  const showNodes = ref<ThinkingProcessNode[]>([
    {
      key: 'understandingIntentionContext', title: '问题理解', state: ThinkingProcessNodeState.RUNNING, content: '', link: '',
    },
    {
      key: 'selectOneDatasetContext', title: '匹配数据集', state: ThinkingProcessNodeState.INIT, content: '', link: '',
    },
    {
      key: 'fieldsContext', title: '涉及字段', state: ThinkingProcessNodeState.INIT, content: '', link: '',
    },
  ])
  const currentMode = computed(() => (agentStore.isOpenAgent === 'true' ? 'single' : agentStore.mode))

  const sessionId = computed(() => sessionStore.currentSession?.sessionId)
  const lastMessage = computed(() => (props.message?.messageId
    ? sessionStore.currentSession?.messages.find(msg => msg.messageId === props.message?.messageId)
      || sessionStore.currentSession?.messages[sessionStore.currentSession?.messages.length - 1]
    : sessionStore.currentSession?.messages[sessionStore.currentSession?.messages.length - 1]))
  const isOpenAgentSessionId = computed(() => route.query.openAgentSessionId)

  // 使用 ref 来管理状态
  const isActive = ref(false)
  const pause = ref<() => void>(() => { /* empty */ })
  const isRequesting = ref(false)

  const singleChartRendered = ref(false)

  function transferStatus(context: AgentFetchResponseContent, key: string): ThinkingProcessNodeState {
    if (key !== 'fieldsContext') {
      return (get(context, `${key}.status`) || ThinkingProcessNodeState.INIT) as ThinkingProcessNodeState
    }
    // 涉及字段阶段时 有一个running总体算running 有一个fail算fail 全部success才算success
    const measureStatus = get(context, 'measureResolveContext.status')
    const dimensionStatus = get(context, 'dimensionResolveContext.status')
    const filterStatus = get(context, 'filterResolveContext.status')
    if (measureStatus === ThinkingProcessNodeState.RUNNING || dimensionStatus === ThinkingProcessNodeState.RUNNING || filterStatus === ThinkingProcessNodeState.RUNNING) {
      return ThinkingProcessNodeState.RUNNING
    }
    if (measureStatus === ThinkingProcessNodeState.FAIL || dimensionStatus === ThinkingProcessNodeState.FAIL || filterStatus === ThinkingProcessNodeState.FAIL) {
      return ThinkingProcessNodeState.FAIL
    }
    if (measureStatus === ThinkingProcessNodeState.SUCCESS && dimensionStatus === ThinkingProcessNodeState.SUCCESS && filterStatus === ThinkingProcessNodeState.SUCCESS) {
      return ThinkingProcessNodeState.SUCCESS
    }
    return ThinkingProcessNodeState.INIT
  }

  function transferContent(context: AgentFetchResponseContent, key: string) {
    try {
      if (key === 'understandingIntentionContext') {
        // 展示改写后的问题
        return get(context, 'understandingAnalysisContext.assistantCompletion.rewriteQuestion') || ''
      }
      if (key === 'selectOneDatasetContext') {
        return get(context, 'selectOneDatasetContext.assistantCompletion.datasetName') || ''
      }
      if (key === 'fieldsContext') {
        // 涉及字段阶段时 有一个running总体算running 有一个fail算fail 全部success才算success
        const measureContent = get(context, 'measureResolveContext.assistantCompletion.measures') || []
        const dimensionContent = get(context, 'dimensionResolveContext.assistantCompletion.dimensions') || []
        const filterContent = get(context, 'filterResolveContext.assistantCompletion.filters') || []
        if (measureContent.length > 0 || dimensionContent.length > 0 || filterContent.length > 0) {
          return {
            measures: measureContent,
            dimensions: dimensionContent,
            filters: filterContent,
          }
        }
        return ''
      }
      return ''
    } catch (error) {
      return ''
    }
  }

  function transferLink(context: AgentFetchResponseContent, key: string) {
    if (key === 'selectOneDatasetContext') {
      const datasetId = get(context, 'selectOneDatasetContext.assistantCompletion.newDatasetId') || ''
      if (datasetId) {
        return `/dataset/list?id=${datasetId}&projectId=${route.query.projectId}`
      }
      return ''
    }
    return ''
  }

  async function running() {
    if (isOpenAgentSessionId.value) {
      nextTick(() => {
        if (isActive.value) {
          pause.value()
        }
      })
      return
    }
    if (!sessionId.value) return
    if (!lastMessage.value) return

    if (['STOPPED', 'FAIL'].includes(lastMessage.value.status)) {
      // 更新节点状态
      showNodes.value = showNodes.value.map((item: ThinkingProcessNode) => ({
        ...item,
        state: item.state === ThinkingProcessNodeState.RUNNING || item.state === ThinkingProcessNodeState.INIT ? lastMessage.value!.status as any : item.state,
      }))
      pause.value()
      return
    }

    // 并发保护：上一次轮询未结束则不再发起新的请求
    if (isRequesting.value) return
    isRequesting.value = true
    try {
      const res = await sessionStore.getProgressInfo()
      if (!res) return
      const context = lastMessage.value.content as AgentFetchResponseContent
      if (!context) return

      const statusList = [
        get(context, 'understandingIntentionContext.status'),
        get(context, 'selectOneDatasetContext.status'),
        get(context, 'measureResolveContext.status'),
        get(context, 'dimensionResolveContext.status'),
        get(context, 'filterResolveContext.status'),
      ]
      // 更新分析结果图表url
      analysisUrl.value = get(context, 'genAnalysisContext.assistantCompletion.analysisUrl') || ''

      // 停止轮询：最后一条消息状态不是RUNNING
      const shouldPause = isActive.value && ((lastMessage.value.status !== 'RUNNING') && (statusList.every(status => status && status !== ThinkingProcessNodeState.RUNNING)))
      if (shouldPause) {
        if (!(statusList.every(status => status === ThinkingProcessNodeState.SUCCESS) && (lastMessage.value.status === 'SUCCESS') && !get(context, 'genAnalysisContext.assistantCompletion.analysisUrl'))) {
          pause.value()
        }
        if (['STOPPED', 'SUCCESS', 'FAIL'].includes(lastMessage.value.status)) {
          // 更新节点状态
          showNodes.value = showNodes.value.map((item: ThinkingProcessNode) => ({
            ...item,
            state: item.state === ThinkingProcessNodeState.RUNNING || item.state === ThinkingProcessNodeState.INIT ? lastMessage.value!.status as any : item.state,
          }))
          if (analysisUrl.value) {
            // 通知父组件更新滚动
            nextTick(() => {
              emit('generate-complete')
            })
          }
        }
      }
      // 中断处理
      if (statusList.some(status => status === ThinkingProcessNodeState.STOPPED)) {
        showNodes.value = showNodes.value.map((item: ThinkingProcessNode) => {
          if (item.state === ThinkingProcessNodeState.RUNNING) {
            return {
              ...item,
              state: ThinkingProcessNodeState.STOPPED,
            }
          }
          return item
        })
        return
      }
      // 更新节点状态
      showNodes.value = showNodes.value.map((item: ThinkingProcessNode) => ({
        ...item,
        state: transferStatus(context, item.key),
        content: transferContent(context, item.key),
        link: transferLink(context, item.key),
      }))
      if (showNodes.value[0].state === ThinkingProcessNodeState.INIT) {
        showNodes.value[0].state = ThinkingProcessNodeState.RUNNING
      }
      showNodes.value.forEach((item: ThinkingProcessNode, index: number) => {
        // 如果上一个步骤是RUNNING或INIT，当前步骤是RUNNING，则当前步骤变为INIT
        if (index > 0 && [ThinkingProcessNodeState.RUNNING, ThinkingProcessNodeState.INIT].includes(showNodes.value[index - 1].state) && item.state === ThinkingProcessNodeState.RUNNING) {
          item.state = ThinkingProcessNodeState.INIT
        }
        // 如果上一个步骤成功，下一个步骤从INIT变为RUNNING
        if (index > 0 && showNodes.value[index - 1].state === ThinkingProcessNodeState.SUCCESS && item.state === ThinkingProcessNodeState.INIT) {
          item.state = ThinkingProcessNodeState.RUNNING
        }
      })

      // 侧边模式 则在左侧自助分析 显示图表
      if (analysisUrl.value && (currentMode.value === 'aside' || agentStore.isOpenAgent === 'true' || agentStore.isFullscreen)) {
        const url = new URL(analysisUrl.value)
        const analysisId = Number(url.searchParams.get('analysisId') || url.searchParams.get('resourceId'))
        const shortcutId = url.searchParams.get('shortcutId') || undefined
        if (analysisId) {
          emit('switch-analysis', {
            analysisId,
            shortcutId,
          })
        } else {
          toast.danger('分析结果图表url错误')
        }
      }
    } finally {
      isRequesting.value = false
    }
  }

  // 然后初始化
  const timeoutPoll = useTimeoutPoll(running, 2500, {
    immediate: true,
  })
  isActive.value = timeoutPoll.isActive.value
  pause.value = timeoutPoll.pause

  function initOpenAgentSession() {
    if (!lastMessage.value) return
    const context = lastMessage.value.content as AgentFetchResponseContent
    if (!context) return
    // 更新节点状态
    showNodes.value = showNodes.value.map((item: ThinkingProcessNode) => ({
      ...item,
      state: transferStatus(context, item.key),
      content: transferContent(context, item.key),
      link: transferLink(context, item.key),
    }))
    // 更新分析结果图表url
    analysisUrl.value = get(context, 'genAnalysisContext.assistantCompletion.analysisUrl') || ''
  }

  if (isOpenAgentSessionId.value) {
    initOpenAgentSession()
  }

  const openAnalysis = () => {
    if (analysisUrl.value) {
      agentStore.setIsAsideOpen(true)
      const url = new URL(analysisUrl.value)
      const analysisId = Number(url.searchParams.get('analysisId') || url.searchParams.get('resourceId'))
      const shortcutId = url.searchParams.get('shortcutId')
      let targetUrl = `/analysis/edit?projectId=${route.query.projectId || 4}&analysisId=${analysisId}&openAgentSessionId=${sessionId.value}`
      if (shortcutId) {
        targetUrl += `&shortcutId=${shortcutId}`
      }
      window.open(targetUrl)
    }
  }

  // 处理方括号内容加粗显示
  const formatTextWithBrackets = (content: any) => {
    if (!content) return ''

    // 如果是字符串，处理方括号加粗
    if (typeof content === 'string') {
      return content.replace(/\「([^\」]+)\」/g, '<span class="understanding-strong">「$1」</span>').replace(/\[([^\]]+)\]/g, '<span class="understanding-strong">[$1]</span>')
    }

    // 如果是对象（fieldsContext的情况），返回空字符串，因为这种情况应该由专门的模板处理
    return ''
  }
  // 图表渲染完再出下面的内容
  const onPageLoaded = (event: any) => {
    const data = event.detail
    singleChartRendered.value = true
    emit('chart-rendered', {
      messageId: Number(props.message?.messageId),
      analysisUrl: analysisUrl.value,
      data,
    })
  }
  onMounted(() => {
    emit('child-mounted', Number(props.message?.messageId))
    window.addEventListener('singleChartLoaded', onPageLoaded)
  })

  onUnmounted(() => {
    window.removeEventListener('singleChartLoaded', onPageLoaded)
  })

  const handleChartRendered = () => {
    // 直接更新 sessionStore 中的消息对象
    if (sessionStore.currentSession?.messages && props.message?.messageId) {
      const messageIndex = sessionStore.currentSession.messages.findIndex(msg => msg.messageId === props.message.messageId)
      if (messageIndex !== -1) {
        sessionStore.currentSession.messages[messageIndex].isChartRendered = true
      }
    }
    emit('generate-complete')
  }
</script>

<style lang="stylus" scoped>

.pick-data-container
  display flex
  flex-direction: column
  align-items: center
  justify-content: center
  width: 100%
  height: 100%
  :deep(.understanding-strong)
    color: rgba(0, 0, 0, 0.7)
    font-weight: 500
.open-analysis-button
  width: 100%
  height: 32px
  background-color: rgba(0, 0, 0, 0.03)
  justify-content: space-between
  border-radius: 4px
  cursor pointer
  margin: 12px 0 0;
  padding: 0 10px;
  &:hover
    background-color: rgba(0, 0, 0, 0.05)
.aside-mode-card
  width: 100%
  height: 300px
  border: 1px solid rgba(0, 0, 0, 0.1)
  border-radius: 4px
</style>
