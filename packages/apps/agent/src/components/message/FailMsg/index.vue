<template>
  <div class="fail-msg">
    <Text>
      {{ errMsg }}
      <!-- <template v-if="message.errorType === 'INTENTION_ERROR'">
        <Text>如需人工支持可</Text>
        <Text
          class="message-feedback-btn"
          @click="onContactOfficial"
        >转人工客服
        </Text>
      </template> -->
    </Text>
  </div>
</template>
<script setup lang="ts">
  import { AgentMessageType } from '@agent/types/message'
  import { Text } from '@xhs/delight'
  import { computed, onMounted } from 'vue'
  import { useDataAgentTrackerStore } from '@analysis/stores/dataAgentTrackerStore'
  import { useSessionStore } from '@agent/store/sessionStore'

  const props = defineProps<{
    message: any
  }>()

  const { handleTracker } = useDataAgentTrackerStore()
  const sessionStore = useSessionStore()

  // const agentStore = useAgentStore()

  const errMsg = computed(() => {
    const msgType = props.message.content.type
    // 单独处理AI找数错误展示文案
    if (msgType === AgentMessageType[2]) {
      return '抱歉，我暂时不能理解你的问题。你可以告诉我需要检索的数据资产和知识，比如热搜词在哪看'
    }
    if ([AgentMessageType[3]].indexOf(msgType) > -1) {
      return props.message.errorMsg || '未知的错误'
    }
    if (props.message.errorType === 'INTENTION_ERROR') {
      return '抱歉，我暂时只能回答取数相关问题。您可以尝试这样提问：时间+分析对象+指标，如 近7天，天气相关搜索词的搜索量'
    }
    return props.message.errorMsg || '我理解不了你的需求呢，请换一种说法试试。'
  })

  // 获取用户提问内容
  const getUserQuestion = () => {
    const session = sessionStore.currentSession
    if (session && props.message.messageId) {
      // 找到当前失败消息的位置
      const currentIndex = session.messages.findIndex(
        msg => msg.messageId === props.message.messageId,
      )
      if (currentIndex > 0) {
        // 找到上一条用户消息
        const userMessage = session.messages[currentIndex - 1]
        if (userMessage?.role === 'user') {
          if (userMessage.content?.type === 'markdown') {
            return userMessage.content.text
          }
          if (userMessage.content?.type === 'pickData') {
            return userMessage.content.query || ''
          }
        }
      }
    }
    return ''
  }

  // 埋点：AI搜索失败曝光
  const trackSearchFailureExposure = () => {
    // 只针对AI搜索（searchData）的失败情况
    if (props.message.content?.type === 'searchData' && sessionStore.currentSession?.sessionId) {
      handleTracker('data_agent_search_fail', {
        data_agent_session_id: String(sessionStore.currentSession.sessionId),
        data_agent_message_id: String(props.message.messageId || ''),
        data_agent_error_type: props.message.errorType || 'UNKNOWN_ERROR',
        data_agent_query: getUserQuestion(),
      }, 'IMPRESSION')
    }
  }

  // 组件挂载时触发曝光埋点
  onMounted(() => {
    trackSearchFailureExposure()
  })

  // const serviceCode = computed(() => {
  //   const departmentName = agentStore.userInfo?.departmentNames?.[0] as keyof typeof ServiceCodeMap
  //   return ServiceCodeMap[departmentName] ?? '8444253345321903'
  // })

  // // 联系客服
  // const onContactOfficial = () => {
  //   openNewPage(`wxwork://message?uin=${serviceCode.value}`)
  // }

</script>
<style lang="stylus" scoped>
.fail-msg
  display flex
  align-items center
  .message-feedback-btn
    cursor pointer
    color var(--color-primary)
</style>
