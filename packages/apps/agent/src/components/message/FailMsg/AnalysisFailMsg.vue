<!--
 * @Date: 2025-06-10 15:32:30
 * @LastEditors: 施利利 <EMAIL>
 * @LastEditTime: 2025-06-10 15:32:30
 * @Description: 分析组件澄清
-->
<template>
  <Text
    v-if="showErrorTip"
    :style="{ color: 'grey'}"
  >由于你提供的信息较少，暂无法生成结果</Text>
  <FailMsg :message="message" />
</template>

<script setup lang="ts">
  import { Text } from '@xhs/delight'
  import FailMsg from './index.vue'

  defineProps<{
    message: any
    showErrorTip?: boolean
  }>()
</script>

<style scoped lang="scss"></style>
