<template>
  <Space
    class="data-ask-suggestions"
    :class="{ 'aside-mode': mode === 'aside' }"
    size="8px"
  >
    <Tag
      v-for="item in dataAskSuggestions"
      :key="item"
      color="white"
      class="data-ask-suggestion-item"
      @click="handleClick(item)"
    >
      {{ item }}
    </Tag>
  </Space>
</template>
<script setup lang="ts">
  import { Tag, Space } from '@xhs/delight'

  defineProps<{
    dataAskSuggestions: string[]
    mode: 'single' | 'aside'
  }>()
  const emit = defineEmits<{(e: 'click', item: string): void
  }>()
  const handleClick = (item: string) => {
    emit('click', item)
  }
</script>
<style lang="stylus" scoped>
.data-ask-suggestions
  margin-bottom 8px;
  overflow-x: auto;
  &::-webkit-scrollbar
    display: none;
  width 100%;
  .data-ask-suggestion-item
    cursor: pointer;
    &:hover
      background-color: #f0f0f0;
  &.aside-mode
    position: absolute;
    top: -34px;
    right: 24px;
    left: 0;
    width: auto;
</style>
