import {
  ref, computed, onUnmounted,
} from 'vue'
import type { Ref, ComputedRef } from 'vue'
import { AgentModeAiType } from '@agent/types'
import type { SessionContext } from '@agent/types'
import type { AgentMessage, DataAskContext, MessageStatus } from '@agent/types/message'
import { ThinkingProcessNodeState } from '@agent/types/message'
import { useSessionStore } from '@agent/store/sessionStore'
import { useSSE } from '@agent/services/useSSE'
import { cloneDeep, omit } from 'lodash'
import { genTraceId, addListener, tryCatch } from '@xhs/redbi-share-utils'
import { useSiteConfigStore } from '@xhs/redbi-share-stores'
import { getSuggestions } from '@agent/services/basicInfo'
import { useContextStore } from '@agent/store/contextStore'

export const useDataAsk = ({
  initEditor,
  isMsgRunning,
  focusToEnd,
  handleSend,
}: {
  initEditor: (value: string) => void
  isMsgRunning: Ref<boolean>
  focusToEnd: (element?: any) => void
  handleSend: (event?: any, tracker?: any) => void
}): {
    sessionContext: Ref<SessionContext[]>
    isDataAsk: ComputedRef<boolean>
    isRunningDataAsk: Ref<boolean>
    setIsRunningDataAsk: (value: boolean) => void
    dataAskPlaceholder: Ref<string>
    agentModel: Ref<string>
    handleRemoveContext: (item: SessionContext) => void
    getDataAskAnswerMessage: (agentMessageRes: any, sessionId: number, currentInput?: string) => AgentMessage
    abortDataAsk: () => void
    resetDataAsk: () => void
    addContext: (data: any) => void
    dataAskSuggestions: Ref<string[]>
    clickAskSuggestion: (suggestion: string) => void
  } => {
  const sessionStore = useSessionStore()
  const siteConfig = useSiteConfigStore()
  // 初始化contextStore
  useContextStore()
  const requestSource = siteConfig?.sdkInfo?.requestSource
  const biChannel = siteConfig?.sdkInfo?.biChannel

  // #region 数据提问建议
  const dataAskSuggestions = ref<string[]>([])
  async function getAndSetSuggestions(params: any) {
    if (!getSuggestions.hasCache(JSON.stringify(params.queryDsl))) {
      dataAskSuggestions.value = []
    }
    const [error, res] = await tryCatch(getSuggestions(params))
    if (error) {
      dataAskSuggestions.value = []
    }
    if (res) {
      try {
        dataAskSuggestions.value = Object.values(JSON.parse(res))
      } catch (e) {
        // e
        dataAskSuggestions.value = []
      }
    }
  }

  function clickAskSuggestion(suggestion: string) {
    initEditor(suggestion)
    // 发送提问
    handleSend()
  }
  // #endregion

  // #region 数据提问
  const sessionContext = ref<SessionContext[]>([])
  // 编辑器是否是数据提问模式（有上下文）
  const isDataAsk = computed(() => sessionContext.value.length > 0)
  // 是否正在运行数据提问
  const isRunningDataAsk = ref(false)
  function setIsRunningDataAsk(value: boolean) {
    isRunningDataAsk.value = value
  }
  const dataAskPlaceholder = ref('')
  // 推理模型
  const agentModel = ref<string>('deepseek-v3-0324')

  function isDeepThinking() {
    return agentModel.value.includes('deepseek-r1')
  }

  function resetDataAsk() {
    sessionContext.value = []
    dataAskSuggestions.value = []
    dataAskPlaceholder.value = ''
    isRunningDataAsk.value = false
  }

  // 移除数据提问上下文
  const handleRemoveContext = (item: SessionContext) => {
    sessionContext.value = sessionContext.value.filter(i => i !== item)
    if (sessionContext.value.length === 0) {
      dataAskPlaceholder.value = ''
      initEditor('')
      dataAskSuggestions.value = []
    }
  }
  // #endregion
  function addContext(data: any, tracker?: any) {
    if (isMsgRunning.value) {
      return
    }
    messageId = data.messageId
    // 添加上下文
    sessionContext.value = [data.context]
    // 添加getContext方法, 用于分析变更时, 获取上下文
    if (data.getContext) {
      Object.defineProperty(data.context, 'getContext', {
        value: data.getContext,
        enumerable: false,
      })
    }
    // 更新placeholder
    dataAskPlaceholder.value = data.placeholder || ''
    setIsRunningDataAsk(false)
    if (data.input) {
      initEditor(data.input)
    }
    // 聚焦输入框
    focusToEnd()

    // 发送提问
    handleSend(undefined, tracker)
  }
  // 监听侧栏时数据提问
  // !需保证 editor 唯一，否则会重复添加上下文
  const removeListener = addListener('ADD_DATA_ASK_CONTEXT', (data: any) => {
    addContext(data, {
      data_agent_skill_type: AgentModeAiType.dataQuestion,
      data_agent_data_question_obj: data?.context?.analysisUrl,
      data_agent_data_question_source: 'analysis',
      data_agent_llm_model: agentModel.value,
    })
  })
  onUnmounted(() => {
    removeListener()
  })

  // #region 提问请求数据

  let abortRequest: (() => void) | null = null
  let messageId: string
  function abortDataAsk() {
    if (abortRequest) {
      abortRequest()
      abortRequest = null
      stopDataAsk(true)
    }
  }

  /**
   * @description 停止数据提问
   * <AUTHOR> Team
   * @param {boolean} [isStop=false] 是否停止， 停止后状态为STOPPED， 否则为FAIL
   */
  function stopDataAsk(isStop = false) {
    const message = cloneDeep(sessionStore.getMessageById(messageId))
    if (!message) {
      return
    }
    if (message.status === 'SUCCESS' || message.status === 'FAIL') {
      return
    }

    if (message.status === 'RUNNING') {
      message.status = isStop ? 'STOPPED' : 'FAIL'
    }
    if ((message.content as DataAskContext).reasoningContent?.state === ThinkingProcessNodeState.RUNNING) {
      (message.content as DataAskContext).reasoningContent.state = isStop ? ThinkingProcessNodeState.STOPPED : ThinkingProcessNodeState.FAIL
    }
    message.title = isStop ? '查询取消' : '查询失败'
    sessionStore.updateMessage(message.messageId as string, message)
  }

  /**
   * @description 数据提问的回答消息, 返回一个正在请求的回答消息
   * <AUTHOR> Team
   * @param {any} answerMessageId
   * @param {number} sessionId
   * @param {string} currentInput
   * @return {*}  {AgentMessage}
   */
  function getDataAskAnswerMessage(answerMessageId: any, sessionId: number, currentInput?: string): AgentMessage {
    const traceId = genTraceId()
    const answerMessage: AgentMessage = {
      messageId: answerMessageId(),
      sessionId,
      title: '正在解读...',
      role: 'assistant' as const,
      content: {
        type: 'dataAsk',
        text: '',
        reasoningContent: null,
      },
      type: 'text' as const,
      status: 'RUNNING' as MessageStatus,
      traceId,
    }

    const sessionContextData = (() => {
      const _sessionContextData = sessionContext.value[0]
      if (_sessionContextData.getContext) {
        return _sessionContextData.getContext()
      }
      return _sessionContextData
    })()
    const queryDsl = sessionContextData.queryDsl
    // delete queryDsl.datasetId
    // 修改查询参数，限制查询数据量
    queryDsl.limit = 20000
    const params = {
      ...omit(sessionContextData, 'type', 'queryDsl', 'title'),
      analysisName: sessionContextData.title,
      queryDsl,
      modelName: agentModel.value,
      userQuestion: currentInput,
      answerMessageId: messageId,
      sessionId,
      requestSource,
      biChannel,
    }

    // 问题推荐
    tryCatch(getAndSetSuggestions(params))

    // 移除数据提问上下文
    // handleRemoveContext(sessionContext.value[0])
    setIsRunningDataAsk(false)

    let text = ''
    // 深度思考内容
    let reasoningContent = {
      enable: false,
      key: 'reasoningContent',
      content: '',
      title: '深度思考',
      state: ThinkingProcessNodeState.INIT,
    }
    // 开始请求
    abortRequest = queryDataAsk({
      headers: {
        'Trace-Id': traceId,
        scene: 'analysis_query_data',
        ...(requestSource ? { 'request-source': requestSource } : {}),
        ...(biChannel ? { 'bi-channel': biChannel } : {}),
      },
      body: params,
      onmessage: (msg: any) => {
        const { data, event } = msg
        // #region 异常处理
        const errorStr = '[AIMI-SSE-ERROR]'
        if (data.includes(errorStr)) {
          sessionStore.updateMessage(answerMessage.messageId as string, {
            errorMsg: data.replace(errorStr, '').replace(/^"|"$/g, ''),
            status: 'FAIL',
            title: '查询失败',
            errorType: 'DATA_ASK_ERROR',
          })
          return
        }
        // 报错
        if (event === 'ERROR') {
          sessionStore.updateMessage(answerMessage.messageId as string, {
            errorMsg: data,
            status: 'FAIL',
            title: '查询失败',
            errorType: 'DATA_ASK_ERROR',
          })
          return
        }
        // 警告
        const warnStr = '[AIMI-SSE-WARN]'
        if (data.includes(warnStr)) {
          sessionStore.updateMessage(answerMessage.messageId as string, {
            warnMsg: data.replace(warnStr, '').replace(/^"|"$/g, ''),
          })
        }
        // 获取 messageId
        if (event === 'MESSAGE_ID') {
          sessionStore.updateMessage(answerMessage.messageId as string, {
            messageId: +data,
          })

          answerMessage.messageId = +data
        }
        // #endregion

        if (data === '[DONE]' || data === '"[DONE]"') {
          sessionStore.updateMessage(answerMessage.messageId as string, {
            status: 'SUCCESS',
          })
          return
        }
        try {
          const _text = JSON.parse(data).choices[0].delta.content || ''
          const _reasoningContent = JSON.parse(data).choices[0].delta.reasoning_content || ''
          if (_text) {
            text += _text
          }
          if (_reasoningContent) {
            reasoningContent = {
              ...reasoningContent,
              enable: isDeepThinking(), // 是否开启深度思考
              content: reasoningContent.content + _reasoningContent,
              state: ThinkingProcessNodeState.RUNNING,
            }
          }
          // 判断理解是否完成
          const message = sessionStore.getMessageById(answerMessage.messageId as string)
          if (!_reasoningContent && _text && (message?.content as DataAskContext).reasoningContent?.state === ThinkingProcessNodeState.RUNNING) {
            // 设置深度思考完成
            reasoningContent.state = ThinkingProcessNodeState.SUCCESS
          }
        } catch (error) {
          // console.error('getDataAskAnswerMessage', error, data)
        }
        sessionStore.updateMessage(answerMessage.messageId as string, {
          title: '数据解读',
          content: {
            type: 'dataAsk',
            text,
            reasoningContent,
          } as any,
        })
      },
      onerror: () => {
        // console.error('getDataAskAnswerMessage onerror', e)
        stopDataAsk()
      },
      onclose: () => {
        // console.error('getDataAskAnswerMessage onclose', e)
        stopDataAsk()
      },
    })

    return answerMessage
  }

  // #endregion
  return {
    sessionContext,
    isDataAsk,
    isRunningDataAsk,
    setIsRunningDataAsk,
    dataAskPlaceholder,
    agentModel,
    handleRemoveContext,
    getDataAskAnswerMessage,
    abortDataAsk,
    resetDataAsk,
    addContext,
    dataAskSuggestions,
    clickAskSuggestion,
  }
}

function queryDataAsk(options: any) {
  const url = '/api/dataAgent/followup/query'
  const _options = {
    ...options,
  }
  return useSSE(url, _options)
}
