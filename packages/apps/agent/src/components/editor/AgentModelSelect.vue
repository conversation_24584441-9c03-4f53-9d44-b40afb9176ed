<template>
  <Space class="agent-model-select">
    <Dropdown
      v-model="model"
      trigger="click"
      auto-close
      :max-width="140"
      style="min-width: 140px;"
    >
      <template #options>
        <Option
          v-for="option in list"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :class="option.value === model ? 'agent-model-select-option-active' : ''"
        />

      </template>
      <template #optionPrefix="{ option }">
        <component :is="genIconUrl(option.value)" />
      </template>
      <template #optionSuffix="{ option }">
        <Icon
          v-if="option.value === model"
          :icon="CheckSmall"
        />
      </template>
      <Button
        size="small"
        type="light"
        class="agent-model-select-button"
      >
        <Space :size="0">
          <component
            :is="genIconUrl(model)"
            style="margin:0;"
          />
          <!-- 用于撑开布局 -->
          <span style="width: 0; opacity: 0;">1</span>
          <Icon
            :icon="Down"
          />
        </Space>
      </Button>
    </Dropdown>
    <Button
      v-if="model === 'deepseek'"
      :class="['agent-model-select-deepseek-button', isDeepSeekR1 ? 'agent-model-select-deepseek-button-active' : '']"
      size="small"
      :type="'light'"
      @click="isDeepSeekR1 = !isDeepSeekR1"
    >
      <Icon
        :size="12"
      >
        <DeepThink />
      </Icon>
      深度思考
    </Button>
  </Space>
</template>

<script setup lang="ts">
  import {
    ref, defineModel, watch, h, watchEffect,
  } from 'vue'
  import {
    Dropdown, Option, Button, Space, Icon,
  } from '@xhs/delight'
  import { Down, CheckSmall } from '@xhs/delight/icons'
  import DeepThink from './icons/deepThink.vue'

  const props = defineProps<{
    modelValue: string
  }>()

  const emit = defineEmits<{(e: 'update:modelValue', value: string): void
  }>()

  const model = defineModel<string>('model')
  // 是否是深度推理
  const isDeepSeekR1 = ref(false)

  watch(() => props.modelValue, value => {
    if (value?.includes('deepseek')) {
      isDeepSeekR1.value = value === 'deepseek-r1-0528'
      model.value = 'deepseek'
    } else {
      model.value = value
    }
  }, {
    immediate: true,
  })

  watchEffect(() => {
    const value = model.value
    if (value === 'deepseek') {
      emit('update:modelValue', isDeepSeekR1.value ? 'deepseek-r1-0528' : 'deepseek-v3-0324')
    } else {
      emit('update:modelValue', value || '')
    }
  })

  type AgentModelItem = {
    label: string
    value: string
    icon: any
  }
  const list: AgentModelItem[] = [{
    label: 'DeepSeek',
    value: 'deepseek',
    // 获取图片
    icon: new URL('./icons/deepseek.svg', import.meta.url).href,
  }, {
    label: 'Qwen3',
    value: 'qwen3-235b-a22b-instruct-2507',
    icon: new URL('./icons/qwen.png', import.meta.url).href,
  },
  // {
  //   label: 'Qwen2.5',
  //   value: 'qwen2_5_72b',
  //   icon: new URL('./icons/qwen.png', import.meta.url).href,
  // },
  // {
  //   label: 'Kimi-K2',
  //   value: 'kimi-k2',
  //   icon: new URL('./icons/kimi.png', import.meta.url).href,
  // }
  ]

  const genIconUrl = (value?: string) => {
    if (!value) {
      value = model.value
    }
    const icon = list.find(item => item.value === value)?.icon
    return h('img', {
      src: icon,
      class: `agent-model-select-option-icon agent-model-select-option-icon-${value}`,
    })
  }

</script>
<style lang="stylus" scoped>
.agent-model-select
  .agent-model-select-button,
  .agent-model-select-deepseek-button
    box-sizing border-box;
    border-radius: 44px;
    min-height 24px;
    border 1px solid #0000001A;
    padding 0 6px;

  .agent-model-select-deepseek-button
    &.agent-model-select-deepseek-button-active
      --color-text-paragraph #386bff;
      border-color var(--color-text-paragraph);

.agent-model-select-option-icon
  width: 16px;
  height: 16px;
  margin: 0 6px;

  &.agent-model-select-option-icon-deepseek
    transform scale(1.2);
</style>
