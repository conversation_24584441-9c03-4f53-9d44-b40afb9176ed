/*
 * @Date: 2025-06-11 21:48:21
 * @LastEditors: 尼禄(张子蓥) <EMAIL>
 * @LastEditTime: 2025-06-12 14:53:14
 * @Description: 编辑器输入框的工具函数
 */
export function transformInputText(inputText: string): any {
  const regex = /\[(.*?)\]/g
  let lastIndex = 0 // 记录上一次匹配结束的位置
  const children: any[] = [] // 存储所有节点
  let match = regex.exec(inputText)// 存储正则匹配结果

  // 若开头是插槽，补一个空文本（需要带空格，否则slate会自动将自定义元素换行）
  if (match && match.index === 0) {
    children.push({ text: ' ' })
  }

  // 循环匹配所有 [] 中的内容
  while (match !== null) {
    // 如果当前匹配位置和上一次结束位置之间有文本，就把这段文本作为普通文本节点
    if (match.index > lastIndex) {
      children.push({ text: inputText.slice(lastIndex, match.index) })
    }

    // 添加一个 input 类型的节点
    children.push({
      type: 'input',
      children: [{ text: '' }],
      placeholder: match[1],
    })
    // 更新 lastIndex 为当前匹配结束的位置
    lastIndex = match.index + match[0].length
    match = regex.exec(inputText)
  }

  if (lastIndex === inputText.length) {
    // 尾部是插槽，补一个空文本
    children.push({ text: '' })
  } else if (lastIndex < inputText.length) {
    // 如果最后一个匹配项后面还有文本，就把剩余文本作为普通文本节点（或者如果inputText没有[]，则把整个inputText作为普通文本节点）
    children.push({ text: inputText.slice(lastIndex) })
  }
  // 返回编辑器的内容
  return [
    {
      type: 'paragraph',
      children,
    },
  ]
}
