<!--
 * @Date: 2025-06-05 21:35:02
 * @LastEditors: 尼禄(张子蓥) <EMAIL>
 * @LastEditTime: 2025-07-15 12:06:29
 * @Description: 历史有效问题
-->
<template>
  <div
    v-loading="loading"
    class="chatbot-history-popover"
    :style="{ width: `${props.width}px` }"
  >
    <Space
      v-for="(item, index) in historyList"
      :key="index"
      class="chatbot-history-item"
      block
      :size="12"
      @click="handleClickQuestion(item)"
    >
      <Tooltip
        style="z-index: 10001"
        :content="item.query"
        :validate="() => agentMode === 'aside' ? item.query.length > 25 : item.query.length > 66"
      >
        <Text
          class="chatbot-history-item-content"
          color="text-title"
          ellipsis
          block
        >
          {{ item.query }}
        </Text>
      </Tooltip>
    </Space>
  </div>
</template>

<script setup lang="ts">
  import { Text, Space, Tooltip } from '@xhs/delight'
  import { computed } from 'vue'
  import { useAgentStore } from '@agent/store/agentStore'

  const agentStore = useAgentStore()

  const props = defineProps<{
    loading: boolean
    width: number
    historyList: any[]
  }>()

  const emit = defineEmits<{(e: 'update:visible', visible: boolean): void
                            (e: 'apply-history', query: string): void
  }>()

  const agentMode = computed(() => agentStore.mode)

  const loading = computed(() => props.loading)
  const historyList = computed(() => props.historyList)

  const handleClickQuestion = (item: any) => {
    emit('update:visible', false)
    emit('apply-history', item.query)
  }

</script>

<style lang="stylus" scoped>
.chatbot-history-popover
  box-sizing border-box
  --size-text-default 12px
  padding 6px 0
  display flex
  flex-direction column
  min-height 48px
  max-height 162px
  overflow overlay
  .chatbot-history-item
    padding 5px 12px
    margin 0 4px
    cursor pointer
    &:hover
      border-radius 4px
      background #f7f7f7
    .chatbot-history-item-content
      flex 1
</style>
