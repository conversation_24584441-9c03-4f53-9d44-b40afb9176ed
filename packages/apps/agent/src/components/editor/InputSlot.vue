<!--
 * @Date: 2025-06-10 14:54:26
 * @LastEditors: 尼禄(张子蓥) <EMAIL>
 * @LastEditTime: 2025-06-24 16:44:08
 * @Description: 输入框里的，输入框插槽
-->
<template>
  <span
    v-bind="$attrs"
    :contenteditable="false"
  >
    <Input
      ref="inputRef"
      class="input-slot"
      style="user-select: none"
      :style="{ width: inputWidth }"
      :contenteditable="false"
      :placeholder="placeholder"
      @change="handleChange"
      @blur="handleBlur"
      @paste.stop
    />
    <slot />
  </span>
</template>
<script setup lang="ts">
  import { Input } from '@xhs/delight'
  import { computed, ref } from 'vue'

  const props = defineProps<{
    element: any
    editor: any
  }>()

  const emit = defineEmits<{(e: 'change', value: string): void
                            (e: 'blur'): void
  }>()

  const inputRef = ref<any>()

  const inputText = ref('')

  const placeholder = computed(() => `${props.element?.placeholder}`)

  const getTextWidth = (text: string) => {
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    if (!context) return 0
    // 从实际元素上获取计算后的样式
    const computedStyle = window.getComputedStyle(document.body)
    const fontSize = computedStyle.getPropertyValue('--size-text-small')
    const fontFamily = computedStyle.getPropertyValue('font-family')
    // 设置完整的字体样式，计算宽度
    if (context) {
      context.font = `${fontSize} ${fontFamily}`
    }
    const metrics = context.measureText(text)
    return metrics.width
  }

  const inputWidth = computed(() => {
    let textWidth = 0
    if (inputText.value) {
      textWidth = getTextWidth(inputText.value)
    } else {
      textWidth = getTextWidth(placeholder.value)
    }
    const padding = 4 // 左右 padding
    return `${Math.max(textWidth + padding, 20)}px` // 设置最小宽度 60px
  })

  const handleChange = (value: string) => {
    inputText.value = value
    emit('change', value)
  }

  const handleBlur = () => {
    emit('blur')
  }

  defineExpose({
    focus: () => {
      inputRef.value.focus()
    },
  })

</script>
<style scoped lang="stylus">
  .input-slot
    display inline-block
    margin 0 4px
    :deep(.d-input.focus)
      border 1px solid transparent
    :deep(.d-input:not(.disabled):not(.readonly).pressing)
      border 1px solid transparent
    :deep(.--color-bg-fill:not(.disabled):not(.--color-bg-static):hover)
      background-color transparent
    :deep(.d-input)
      padding 0
      background-color transparent
      border 1px solid transparent
</style>
