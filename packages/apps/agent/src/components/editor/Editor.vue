<!--
 * @Date: 2025-05-28 17:34:08
 * @LastEditors: 尼禄(张子蓥) <EMAIL>
 * @LastEditTime: 2025-08-07 16:31:35
 * @Description: 输入框组件
-->
<template>
  <div
    ref="editorWrapRef"
    class="editor-wrap"
  >
    <AskSuggestions
      v-if="isDataAsk && dataAskSuggestions?.length > 0"
      :data-ask-suggestions="dataAskSuggestions"
      :mode="agentStore.mode"
      @click="clickAskSuggestion"
    />
    <div
      :class="['editor-content', { 'active': isFocus, 'data-ask': isDataAsk, 'simple-mode': isSimpleMode, 'only-one-line': inputIsEmpty && isSimpleMode }]"
    >
      <!-- 分析-业务线-agent -->
      <div
        v-if="isApolloAnalysis"
        style="margin-bottom: 12px; display: flex; justify-content: space-between;"
      >
        <div
          ref="buttonContainerRef"
          class="analysis-buttons-container"
        >
          <Button
            v-for="item in visibleAnalysisData"
            :key="item.agentInfo.agentCode"
            :type="activeAnalysisAgent === item.agentInfo.agentCode ? 'secondary' : 'light'"
            size="small"
            :icon="dilightIcon[item.icon as keyof typeof dilightIcon]"
            class="analysis-agent-btn"
            @click="handleAnalysisAgent(item)"
          >
            <!-- <img
              v-if="item.icon && !item.icon.includes('<svg')"
              :src="item.icon"
              alt="自定义图片"
              style="width: 16px; height: 16px;"
            /> -->
            <component
              :is="item.icon ? 'svg' : ''"
              v-if="item.icon && item.icon.includes('<svg')"
              :style="{ width: '16px', height: '16px' }"
              v-html="item.icon"
            />
            {{ item.displayName }}
            <!-- <Text
              ellipsis
              tooltip
              style="display: inline-block; max-width: 100px;"
            >{{ item.displayName }}</Text> -->
          </Button>
        </div>
        <Popover
          trigger="manual"
          :visible="manualVisible"
        >
          <Button
            v-if="hiddenAnalysisData.length > 0"
            ref="analysisAgentMoreBtnRef"
            style="margin-left: 10px;"
            size="small"
            type="light"
            :icon="dilightIcon.Down"
            icon-position="right"
            @click="handleAnalysisAgentMoreBtnClick"
          >更多</Button>
          <template #content>
            <div
              class="analysis-agent-popover-content"
            >
              <Button
                v-for="item in hiddenAnalysisData"
                :key="item.agentInfo.agentCode"
                :type="activeAnalysisAgent === item.agentInfo.agentCode ? 'secondary' : 'light'"
                size="small"
                :icon="dilightIcon[item.icon as keyof typeof dilightIcon]"
                class="analysis-agent-btn"
                @click="handleAnalysisAgent(item)"
              >
                <!-- <img
                  v-if="item.icon && !item.icon.includes('<svg')"
                  :src="item.icon"
                  alt="自定义图片"
                  style="width: 16px; height: 16px;"
                /> -->
                <component
                  :is="item.icon ? 'svg' : ''"
                  v-if="item.icon && item.icon.includes('<svg')"
                  :style="{ width: '16px', height: '16px' }"
                  v-html="item.icon"
                />
                {{ item.displayName }}
              </Button>
            </div>
          </template>
        </Popover>
      </div>
      <AskContext
        v-if="isDataAsk"
        :context="sessionContext"
        @remove="handleRemoveContext"
      />
      <Slate
        :editor="editor"
        :render-element="renderElement"
      >
        <Editable
          v-if="showEditor"
          ref="editorRef"
          :key="dataAskPlaceholder"
          :class="['input-area', { 'simple-mode': isSimpleMode, 'only-one-line': inputIsEmpty && isSimpleMode, 'apollo-analysis': isApolloAnalysis, 'input-area-single': agentStore.mode === 'single' && agentStore.isAgentHome }]"
          :placeholder="editorPlaceholder"
          @keydown="handleKeyDown"
          @focus="handleFocus"
          @blur="handleBlur"
          @compositionstart="isComposing = true"
          @compositionend="isComposing = false"
          @paste="handlePaste"
        />
      </Slate>
      <div
        v-if="!isSimpleMode"
        class="operation-area"
      >
        <Select
          v-if="(aiType === AgentModeAiType.data || (aiType === AgentModeAiType.analyze && isAgentHome)) && !isDataAsk"
          ref="datasetSelectRef"
          v-model="datesetType"
          class="dataset-select"
          :options="DATASET_OPTIONS"
          :disabled="!isAgentHome"
          :max-dropdown-width="200"
          dropdown-style="z-index: 10000"
          @change="handleDatasetTypeChange"
        />
        <AgentModelSelect
          v-if="isDataAsk"
          v-model="agentModel"
          class="dataset-select"
        />
        <!-- v-else-if="aiType === AgentModeAiType.search" -->
        <Popover
          v-else-if="aiType === AgentModeAiType.search"
          trigger="manual"
          :visible="searchConfigVisible"
          placement="bottom-start"
          @click:outside="searchConfigVisible = false"
        >
          <div
            ref="aiSearchConfigRef"
            class="search-config"
            @click="searchConfigVisible = !searchConfigVisible"
          >
            {{ displaySearchRange }}
            <Icon
              style="margin-left: 4px"
              :icon="dilightIcon.Down"
              :fill="isAgentHome ? '#787878' : '#d1d1d4'"
            />
          </div>
          <template #content>
            <div class="ai-search-config-form-wrapper">
              <Form
                label-width="40px"
                label-position="left"
                :hide-required-mark="true"
              >
                <FormItem
                  label="业务"
                  name="business"
                >
                  <RadioGroup
                    v-model="tempSearchRange.business"
                    :options="BUSINESS_OPTIONS"
                  />
                </FormItem>
                <FormItem
                  label="权限"
                  name="onlyPermission"
                >
                  <Checkbox
                    v-model:checked="tempSearchRange.onlyPermission"
                    label="只看我有权限"
                  />
                </FormItem>
              </Form>
              <div class="ai-search-popover-footer">
                <Button @click="handleCancel">取消</Button>
                <Button
                  type="primary"
                  @click="handleConfirm"
                >确定</Button>
              </div>
            </div>
          </template>
        </Popover>
        <Tooltip
          v-if="!isMsgRunning"
          content="请输入你的提问"
          :validate="() => !inputValue"
        >
          <SendSvg
            class="send-btn"
            :class="{ disabled: !inputValue }"
            @click="handleSend"
          />
        </Tooltip>
        <Tooltip
          v-else
          content="停止生成"
        >
          <StopSvg
            style="color: blue"
            class="stop-btn"
            @click="handleStop"
          />
        </Tooltip>
      </div>
      <template v-else>
        <AgentModelSelect
          v-if="isDataAsk"
          v-model="agentModel"
          class="dataset-select"
        />
        <div
          class="simple-mode-operation-area"
        >
          <Tooltip
            v-if="!isMsgRunning"
            content="请输入你的提问"
            :validate="() => !inputValue"
          >
            <SendSvg
              class="send-btn"
              :class="{ 'disabled': !inputValue }"
              @click="handleSend"
            />
          </Tooltip>
          <Tooltip
            v-else
            content="停止生成"
          >
            <StopSvg
              style="color: blue"
              class="stop-btn"
              @click="handleStop"
            />

          </Tooltip>
        </div>
      </template>
    </div>
  </div>
  <!-- 历史记录列表 -->
  <Popover
    :target="editorWrapRef"
    style="z-index: 10000"
    trigger="manual"
    :visible="historyList.length > 0 && historyVisible"
  >
    <template #content>
      <HistoryList
        ref="historyListRef"
        :loading="historyLoading"
        :width="agentInputWidth"
        :history-list="historyList"
        @update:visible="(visible: boolean) => historyVisible = visible"
        @apply-history="handleApplyHistory"
      />
    </template>
  </Popover>
</template>
<script setup lang="ts">
  // @ts-ignore
  import { Slate, Editable, useInheritRef } from 'slate-vue3'
  // @ts-ignore
  import { createEditor, Transforms, Editor } from 'slate-vue3/core'
  // @ts-ignore
  import { withDOM } from 'slate-vue3/dom'
  // @ts-ignore
  import { withHistory } from 'slate-vue3/history'
  import {
    ref, computed, watch, nextTick, h, onMounted, onUnmounted, inject, reactive,
  } from 'vue'
  import {
    Tooltip, Select, toast2 as toast, Popover, Icon, Form2 as Form, FormItem2 as FormItem, Button, RadioGroup, Checkbox,
  } from '@xhs/delight'
  import * as dilightIcon from '@xhs/delight/icons'
  import { useSessionStore } from '@agent/store/sessionStore'
  import { createNewSession, sendMessage, getHistoryMsgList } from '@agent/services/basicInfo'
  import { useAgentStore } from '@agent/store/agentStore'
  import { AgentModeAiType, DataAskType } from '@agent/types'
  import { AgentMessageType, MessageStatus, SessionStatus } from '@agent/types/message'
  import { publish, addListener, removeListener } from '@xhs/redbi-share-utils'
  import { getMessageTitle, handleMessageStop, getRandomMessageId } from '@agent/utils'
  import { useElementSize } from '@vueuse/core'
  import { useDataAgentTrackerStore } from '@analysis/stores/dataAgentTrackerStore'
  import { useRoute } from 'vue-router'
  import { getUnderstandingContentFromMessage } from '@agent/utils/messageHelper'
  import { getReportMessageType } from '@agent/components/message/analysisData/useAnalysisData'
  import StopSvg from './StopSvg.vue'
  import SendSvg from './SendSvg.vue'
  import HistoryList from './HistoryList.vue'
  import InputSlot from './InputSlot.vue'
  import { transformInputText } from './utils'
  import { getSearchAssistantMessage } from '../message/searchData/searchHelper'
  import { DATASET_OPTIONS, BUSINESS_OPTIONS } from './constant'
  import AskContext from './AskContext.vue'
  import AgentModelSelect from './AgentModelSelect.vue'
  import AskSuggestions from './AskSuggestions.vue'
  import { useDataAsk } from './useDataAsk'

  const route = useRoute()

  const props = withDefaults(defineProps<{
    initialValue?: string
    analysisData?: any[]
    analysisAgentCode?: string
    isSimpleMode?: boolean
    shouldAutoSubmit?: boolean
  }>(), {
    initialValue: '',
    analysisData: () => [],
    analysisAgentCode: '',
    isSimpleMode: false,
    shouldAutoSubmit: false,
  })

  // eslint-disable-next-line no-spaced-func, func-call-spacing
  const emit = defineEmits<{
    (e: 'stop'): void
    (e: 'start'): void
    (e: 'reset-editor-input'): void
    (e: 'dataset-type-change', value: number): void
    (e: 'analysis-agent-change', value: string): void
    (e: 'auto-submit-done'): void
  }>()

  const withCustomElements = (editor: any) => {
    const { isInline, isVoid, markableVoid } = editor
    editor.isInline = (element: any) => (element.type === 'input' ? true : isInline(element))
    editor.isVoid = (element: any) => (element.type === 'input' ? true : isVoid(element))
    editor.markableVoid = (element: any) => element.type === 'input' || markableVoid(element)
    return editor
  }

  const { analysisProjectId } = inject<any>('AnalysisAttrs')

  const sessionStore = useSessionStore()
  const agentStore = useAgentStore()

  const { handleTracker } = useDataAgentTrackerStore()

  const currentSession = computed(() => sessionStore.currentSession)
  const isAgentHome = computed(() => agentStore.isAgentHome) // 是否是新会话（是否是首页）
  const aiType = computed(() => agentStore.aiType) // 当前的ai类型。1取数/2找数/3分析
  const reclaimMessageId = computed(() => agentStore.reclaimMessageId) // 是否为纠正问题

  const activeAnalysisAgent = computed(() => props.analysisAgentCode)

  const handleAnalysisAgent = (item: any) => {
    // if (sessionStore.currentSession && props.analysisData.length) {
    //   sessionStore.currentSession.scene = item.agentInfo.agentCode
    // }
    manualVisible.value = false
    sessionStore.setAnalysisAgentCode(item.agentInfo.agentCode)
    sessionStore.setAnalysisAgentId(item.agentInfo?.agentId || 0)
    inputSlotValues.value?.clear()
    resetEditor()
    emit('reset-editor-input')
    emit('analysis-agent-change', item.agentInfo.agentCode)
  }

  const historyVisible = ref(false)
  const historyList = ref<any[]>([])
  const historyLoading = ref(false)
  // 是否来自推荐问题
  const isFromRecommend = ref(false)
  const recommendQuestion = ref('')

  const isFocus = ref(false)
  const editor = ref<any>(withHistory(withCustomElements(withDOM(createEditor()))))
  const editorRef = ref<any>()
  const editorWrapRef = ref<any>()
  const isComposing = ref(false) // 输入法是否正在输入
  const historyListRef = ref<any>()
  const datasetSelectRef = ref<any>()
  const aiSearchConfigRef = ref<any>()
  const buttonContainerRef = ref<any>()
  const { width: agentInputWidth } = useElementSize(editorWrapRef)
  const isApolloAnalysis = computed(() => aiType.value === AgentModeAiType.analyze && isAgentHome.value && props.analysisData.length > 0)
  const manualVisible = ref(false) // 是否手动触发聚焦
  const analysisAgentMoreBtnRef = ref<any>()

  // 按钮自适应相关
  const visibleAnalysisData = ref<any[]>([])
  const hiddenAnalysisData = ref<any[]>([])

  // 计算可见和隐藏的按钮
  const calculateVisibleButtons = () => {
    if (!props.analysisData.length) {
      visibleAnalysisData.value = []
      hiddenAnalysisData.value = []
      return
    }

    // 初始显示所有按钮
    visibleAnalysisData.value = [...props.analysisData]
    hiddenAnalysisData.value = []

    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
      if (!buttonContainerRef.value) return
      const container = buttonContainerRef.value
      const containerWidth = editorRef.value.$el.offsetWidth
      const buttons = container.querySelectorAll('.analysis-agent-btn')
      if (buttons.length === 0) return
      let totalWidth = 0
      const visibleButtons: any[] = []
      const hiddenButtons: any[] = []

      // 计算每个按钮的宽度
      buttons.forEach((button: any, index: number) => {
        const buttonWidth = button.offsetWidth + 4 // 4px 为间距
        totalWidth += buttonWidth
        if (totalWidth <= containerWidth - 80) { // 84px 为 Popover 按钮预留空间
          visibleButtons.push(props.analysisData[index])
        } else {
          hiddenButtons.push(props.analysisData[index])
        }
      })

      visibleAnalysisData.value = visibleButtons
      hiddenAnalysisData.value = hiddenButtons
      manualVisible.value = false
    })
  }
  const handleAnalysisAgentMoreBtnClick = () => {
    manualVisible.value = !manualVisible.value
    historyVisible.value = false
  }
  // 输入框内容组合（输入框内容+插槽内容）
  const inputValue = computed(() => {
    const children = editor.value?.children
    if (!children) return ''
    // 执行shift+enter之后，children变为一个多值的数组，所以将所有段落合并成一个字符串
    let textParams = ''
    if (children.length > 1) {
      textParams = children.map((item: any) => {
        if (item.type === 'paragraph') {
          return item.children.map((child: any, index: number) => {
            if (child.type === 'input') {
              return inputSlotValues.value.get(index) || child.placeholder || ''
            }
            return child.text
          }).join('')
        }
        return item.text
      }).join('')
    }
    const firstParagraph = children.length > 1 ? {
      ...children[0],
      children: [{ text: textParams }],
    } : children[0]
    if (!firstParagraph) return ''
    const inputNodeList = firstParagraph.children
    if (!inputNodeList) return ''

    let result = ''
    inputNodeList.forEach((node: any, index: number) => {
      if (node.type === 'input') {
        result += inputSlotValues.value.get(index) || node.placeholder || ''
      } else if (node.text) {
        result += node.text
      }
    })
    return result
  })

  // 判断输入框是否为空
  const inputIsEmpty = computed(() => inputValue.value.length === 0)

  const showEditor = ref(true)
  const editorPlaceholder = computed(() => {
    if (isDataAsk.value && dataAskPlaceholder.value) {
      return dataAskPlaceholder.value
    }
    switch (aiType.value) {
      case AgentModeAiType.data:
        return '输入你的取数问题，AI帮你便捷取数，Enter发送'
      case AgentModeAiType.search:
        return '输入你的找数问题，AI帮你搜索数据知识/资产，Enter发送'
      case AgentModeAiType.analyze:
        if (currentSession.value?.scene === 'GOOD_STRATEGY') {
          return '可以查询自己的好策略报告、下钻账户/SPU/SPU下策略组合表现，其他暂不支持哦~'
        }
        if (currentSession.value?.scene === 'CUSTOM_ANALYSIS') {
          return '可以分析指定brandtag的线索广告投放情况，支持指定竞对、赛道客户地区，其他暂不支持哦～'
        }
        if (props.analysisData.length) {
          return props.analysisData.find((item: any) => item.agentInfo.agentCode === activeAnalysisAgent.value)?.agentInfo.openingRemarks || '请输入问题，Enter发送'
        }
        return '输入需要分析的客户，并提问，让AI帮你生成分析报告，Enter发送'
      default:
        return '输入你的取数问题，AI帮你便捷取数，Enter发送'
    }
  })

  const agentMessageInfo = ref<any>()
  const isMsgRunning = computed(() => {
    const messages = currentSession.value?.messages || []
    const lastMessage = messages[messages.length - 1]
    // 如果最近一条消息正在RUNNINg或者正在发送
    return agentStore.aiType === AgentModeAiType.search
      ? ['SENDING', 'RUNNING'].includes(lastMessage?.status)
      : ['RUNNING'].includes(lastMessage?.status)
  })

  // 业务域切换相关逻辑，agent业务域：1商业化，2社区，3交易
  const datesetType = computed(() => {
    if (agentStore.aiType === AgentModeAiType.analyze) {
      return sessionStore.analysisDatesetType
    }
    return sessionStore.datesetType
  })
  const agentProjectId = computed(() => {
    if (agentStore.aiType === AgentModeAiType.analyze) {
      const agentInfo = props.analysisData.find((item: any) => item.agentInfo.agentCode === activeAnalysisAgent.value)
      // ai分析以tab子选项为准
      return agentInfo?.agentInfo?.projectId || analysisProjectId.value
    }
    if (agentStore.aiType === AgentModeAiType.data) {
      // 取数以用户选择的数据集类型为准
      return datesetType.value
    }
    if (agentStore.aiType === AgentModeAiType.search) {
      return AISearchRange.business === 'all' ? undefined : AISearchRange.business
    }
    return 1
  })
  const handleDatasetTypeChange = (value: any) => {
    if (agentStore.aiType === AgentModeAiType.analyze) {
      sessionStore.setAnalysisDatasetType(value)
      localStorage.setItem('agent_analysis_dataset_type', String(value))
      resetEditor()
      emit('dataset-type-change', value)
      return
    }
    sessionStore.setDataSetType(value)
    localStorage.setItem('agent_dataset_type', String(value))
    emit('dataset-type-change', value)
  }

  // Ai搜索范围相关逻辑
  const searchConfigVisible = ref(false)
  const getInitialSearchRange = () => {
    const AISearchLocalConfig = localStorage.getItem('ai_search_config')

    if (AISearchLocalConfig) {
      try {
        return JSON.parse(AISearchLocalConfig)
      } catch (e) {
        console.error('解析本地搜索范围失败:', e)
      }
    }
    return {
      business: 'all',
      onlyPermission: false,
    }
  }
  // 实际使用的搜索范围（从本地存储初始化）
  const AISearchRange = reactive(getInitialSearchRange())
  const tempSearchRange = reactive(getInitialSearchRange())
  const displaySearchRange = computed(() => {
    const businessText = BUSINESS_OPTIONS.find(opt => opt.value === AISearchRange.business)?.label || '全部'
    const permissionText = AISearchRange.onlyPermission ? ' (只看有权限的)' : ''
    return `搜索范围：${businessText}${permissionText}`
  })
  const handleCancel = () => {
    // 重置临时状态为当前实际状态
    tempSearchRange.business = AISearchRange.business
    tempSearchRange.onlyPermission = AISearchRange.onlyPermission
    searchConfigVisible.value = false
  }
  const handleConfirm = () => {
    // 更新实际状态
    AISearchRange.business = tempSearchRange.business
    AISearchRange.onlyPermission = tempSearchRange.onlyPermission
    // 同时更新本地存储
    localStorage.setItem('ai_search_config', JSON.stringify(AISearchRange))

    searchConfigVisible.value = false
  }

  // 输入框相关回调函数
  const inputSlotRefs = ref(new Map<number, any>()) // 记录每个输入框插槽的ref
  const inputSlotValues = ref(new Map<number, string>()) // 记录每个输入框插槽对应的值
  // 初始化输入问题，整个输入框只有一个段落paragraph
  const initEditor = (inputText?: string, _isFocus: boolean = true) => {
    if (!inputText) {
      editor.value.children = [
        {
          type: 'paragraph',
          children: [{ text: '' }],
        },
      ]
      return
    }

    // 有值的情况下，转换编辑器的内容（识别插槽区域）
    editor.value.children = transformInputText(inputText)
    nextTick(() => {
      if (!isDataAsk.value) {
        // 先聚焦编辑器
        editorRef.value?.$el.focus()
        // 设置选区到末尾
        Transforms.select(editor.value, Editor.end(editor.value, []))
      }
      // 如果有 input 插槽，则聚焦第一个
      const firstInputIndex = editor.value?.children?.[0]?.children?.findIndex(
        (node: any) => node.type === 'input',
      )
      if (firstInputIndex !== -1) {
        inputSlotRefs.value.get(firstInputIndex)?.focus()
      } else if (_isFocus) {
        focusToEnd(editorRef.value?.$el)
      }
    })
  }

  // 重置编辑器为空
  const resetEditor = () => {
    // 删除所有节点
    while (editor.value.children.length > 0) {
      Transforms.removeNodes(editor.value, { at: [0] })
    }
    // 初始化空节点
    Transforms.insertNodes(editor.value, {
      type: 'paragraph',
      children: [{ text: '' }],
    })
  }

  // 输入框自定义元素
  const renderElement = (elProps: any) => {
    const { attributes, children, element } = elProps
    const curInputIndex = editor.value.children[0].children.findIndex(
      (node: any) => node === element,
    )
    switch (element.type) {
      // 嵌套输入插槽
      case 'input':
        return h(
          InputSlot,
          {
            ...useInheritRef(attributes),
            ref: (el: any) => {
              inputSlotRefs.value.set(curInputIndex, el)
            },
            element,
            onChange: (value: string) => {
              inputSlotValues.value.set(curInputIndex, value)
            },
          },
          () => children,
        )
      // TODO：数据集卡片
      default:
        return h('paragraph', attributes, children)
    }
  }

  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      handleSend(e)
    } else if ((e.code === 'Space' || e.key === ' ') && isComposing.value) {
      e.preventDefault()
    }
  }

  // 粘贴时需要解析无格式纯文本
  const handlePaste = (event: ClipboardEvent) => {
    event.preventDefault()
    const text = event.clipboardData?.getData('text/plain') || ''
    Transforms.insertText(editor.value, text)
  }

  // 记录初始来源信息
  const initialSource = ref('')

  // 在组件挂载时记录来源
  onMounted(() => {
    // 检查是否从外部入口进入
    if (route.query.isOpenAgent === 'true' && route.query.from) {
      const fromSource = route.query.from as string

      switch (fromSource) {
        case 'dashboard-search':
          initialSource.value = '看板搜索-AI搜索'
          break
        case 'market-search':
          initialSource.value = '数据集市-AI搜索'
          break
        case 'analysis-dataset-search':
          initialSource.value = '自助数据集下拉-AI搜索'
          break
        default:
          // 如果有其他未知的from值，记录原始值以便调试
          initialSource.value = `外部入口-${fromSource}`
          break
      }
    }
  })

  // 判断用户问题的来源
  const getQuestionSource = () => {
    const currentInput = inputValue.value.trim()

    // 优先检查问题内容模式（这些模式优先级最高）
    if (agentStore.aiType === AgentModeAiType.data) {
      if (currentInput.includes('返回AI搜索结果')) {
        return 'AI取数-返回AI搜索结果'
      }
      if (currentInput.includes('的口径')) {
        return 'AI取数-xxxx的口径'
      }
      if (currentInput.includes('的相关看板')) {
        return 'AI取数-xxxx的相关看板'
      }
      if (currentInput.includes('的相关数据集') || currentInput.includes('的相关分析模板')) {
        return 'AI取数-xxxx的相关数据集/分析模板'
      }
    }

    // 检查是否为首次从外部入口进入
    // 优先检查是否有外部来源标识和initialSource值
    if (initialSource.value && (route.query.from || route.query.isOpenAgent === 'true')) {
      return initialSource.value
    }

    // 检查AI搜索模式
    if (agentStore.aiType === AgentModeAiType.search) {
      return 'agent-AI搜索'
    }

    // session内的后续问题，默认归类为agent内操作
    return 'agent-AI搜索'
  }

  const sendEvent = ref<any>()
  const handleSend = async (event?: any, tracker?: any, extra?: Record<string, any>) => {
    // 发送消息,关闭历史记录
    publish('close-history', {})
    if (isComposing.value) return
    sendEvent.value = event
    // 只有当event是真正的事件对象时才调用preventDefault
    if (event && typeof event === 'object' && event.preventDefault) {
      event.preventDefault()
    }
    handlePureSend({
      sendContent: inputValue.value,
      extra,
      tracker,
    })
  }

  const handlePureSend = async ({
    sendContent,
    from = 'editor',
    extra = {},
    tracker,
  }: {
    sendContent: string
    from?: 'editor' | 'analysis'
    extra?: Record<string, any>
    tracker?: any
  }) => {
    const tempIsAgentHome = isAgentHome.value
    sessionStore.clickRegenerateMessage(false)

    if (!sendContent.trim()) {
      return
    }
    toast.destroy()
    // 判断最后一条消息的状态
    const messages = currentSession.value?.messages || []
    const lastMsg = messages[messages.length - 1]
    const lastMsgStatus = lastMsg?.status
    if (
      currentSession.value?.status
      && lastMsgStatus
      && (lastMsgStatus === 'SENDING' || lastMsgStatus === 'RUNNING')
    ) {
      toast.warning('AI思考中，重新提问前请先停止生成')
      return
    }

    // 保存输入内容后，立刻重置编辑器，避免卡顿
    const currentInput = sendContent.trim()
    resetEditor()
    if (agentStore.aiType !== AgentModeAiType.analyze) {
      nextTick(() => {
        editorRef.value?.$el.focus()
      })
    }
    let isWithinSession = false

    try {
      let sessionId
      // 1.检查是否没有会话或者是虚拟会话(从AI分析点击卡片创建的)
      if (!currentSession.value?.messages?.length || Number(currentSession.value?.sessionId) < 0) {
        // 创建新会话（设置虚拟id，用于页面快速响应）
        sessionStore.setCurrentSession({
          sessionId: getRandomMessageId(),
          type: AgentModeAiType[agentStore.aiType],
          messages: [...(currentSession.value?.messages || [])], // AI分析可能会有虚拟消息，需要保留
          status: 'EDITING' as SessionStatus,
          scene: sessionStore.analysisAgentCode,
        })
        sessionId = await createNewSession({ sessionType: agentStore.aiType })
        sessionStore.updateSession({ sessionId })
        isWithinSession = false
      } else {
        // 继续当前会话
        sessionId = currentSession.value.sessionId
        isWithinSession = true
      }

      // 2.发送消息
      if (isDataAsk.value) {
        setIsRunningDataAsk(true)
      }
      if (lastMsg && !extra?.planContent) {
        const messageType = getReportMessageType((lastMsg?.content as any)?.aiContent)
        if (messageType === 'PLANING') {
          extra.planContent = (lastMsg?.content as any)?.aiContent?.analysisPlanContent?.分析计划Md
        }
      }

      // 2.1设置虚拟id，用于页面快速响应
      const userMessage = {
        messageId: getRandomMessageId(),
        sessionId,
        title: '',
        role: 'user' as const,
        content: {
          type: 'markdown' as const,
          text: currentInput,
          ...(extra || {}),
          askContext: isRunningDataAsk.value ? sessionContext.value : null,
        },
        type: 'text' as const,
        status: 'SENDING' as MessageStatus,
      }
      sessionStore.addMessage(userMessage)

      // 2.2发送消息
      sessionStore.updateSession({ status: 'RUNNING' as SessionStatus }) // 发消息前会话状态为RUNNING，等待返回消息成功或失败时，再重置为EDITING
      const hasReviewerPerm = agentStore.aiType === AgentModeAiType.search ? AISearchRange.onlyPermission : undefined

      let answerMessage:any
      if (isDataAsk.value) {
        answerMessage = getDataAskAnswerMessage(getRandomMessageId, sessionId as number, currentInput)
        // 提交会话发送埋点
        if (currentInput) {
          const trackerParams: Record<string, any> = {
            data_agent_session_id: String(sessionId),
            data_agent_current_mode: agentStore.mode,
            data_agent_query: currentInput,
            // 是否会话内发送 判断根据currentSession是否存在
            data_agent_is_within_session: isWithinSession === true ? '是' : '否',
            // 取数时:问题来源 其他技能传空
            data_agent_pick_question_origin: dataAskSuggestions.value.includes(currentInput) ? '推荐问题' : '手动输入',
            // 分析时:问题来源 其他技能传空
            data_agent_analysis_question_origin: AgentModeAiType[agentStore.aiType] !== 'analyze' ? '' : currentSession.value?.scene || `scene不存在，请检查sessionId:${sessionId}`,
            ...(tracker || {
              data_agent_skill_type: AgentModeAiType.dataQuestion,
              data_agent_data_question_obj: sessionContext.value[0]?.analysisUrl,
              data_agent_data_question_source: sessionContext.value[0]?.type === DataAskType.analysis ? 'analysis' : 'agent',
              data_agent_llm_model: agentModel.value,
            }),
          }

          // 只有AI搜索模式下才传递问题来源统计
          if (agentStore.aiType === AgentModeAiType.search) {
            trackerParams.data_agent_question_source = getQuestionSource()
          }

          handleTracker('data_agent_session_send', trackerParams)
        }
      } else {
        const sendParams: Record<string, any> = {
          sessionId,
          messages: [
            {
              role: 'user',
              content: currentInput,
              askContext: isRunningDataAsk.value ? sessionContext.value : null,
              scene: currentSession.value?.scene,
              projectId: agentProjectId.value,
              corrected: !!reclaimMessageId.value,
              hasReviewerPerm,
              ...(extra || {}),
            },
          ],
        }

        // 只有取数会更新这个状态
        if (reclaimMessageId.value) {
          // 取数问题纠正埋点
          const query = getUnderstandingContentFromMessage(lastMsg.content)
          handleTracker('data_agent_question_rectify', {
            data_agent_query: query,
            data_agent_query_rectify: currentInput,
            traceid: lastMsg?.traceId as string,
            data_agent_session_id: String(sessionId),
            data_agent_business: String(agentStore.aiType),
          })

          // 纠正问题时，需要记录原始消息id，用于后续的溯源
          sendParams.messages[0].correctInfo = {
            originalQuestion: query,
            correctedQuestion: currentInput,
            originAnswerMessageId: reclaimMessageId.value,
          }

          // 纠正问题状态重置
          agentStore.setReclaimMessageId(0)
        }

        if (agentStore.aiType === AgentModeAiType.analyze) {
          const agentInfo = props.analysisData.find((item: any) => item.agentInfo.agentCode === activeAnalysisAgent.value)
          const agentCode = agentInfo?.agentInfo.agentCode || currentSession.value?.scene
          sendParams.analysisScene = agentCode || sessionStore.analysisAgentCode
          sendParams.agentId = agentInfo?.agentInfo.agentId || sessionStore.analysisAgentId || 0
        }
        const id = getRandomMessageId()
        if (agentStore.aiType === AgentModeAiType.search) {
          // 添加一条新的消息
          sessionStore.addMessage({
            messageId: id,
            sessionId,
            title: '为你找到如下数据内容:',
            role: 'assistant',
            content: {
              type: 'searchData',
              text: 'AI 正在思考...',
            },
            type: 'text',
            status: 'SENDING' as MessageStatus,
          })
        }

        const agentMessageRes = await sendMessage(sendParams)
        // 提交会话发送埋点
        if (currentInput) {
          const trackerParams: Record<string, any> = {
            data_agent_session_id: String(sessionId),
            data_agent_current_mode: agentStore.mode,
            data_agent_query: currentInput,
            data_agent_skill_type: AgentModeAiType[agentStore.aiType],
            // 是否会话内发送 判断根据currentSession是否存在
            data_agent_is_within_session: isWithinSession === true ? '是' : '否',
            // 取数时:问题来源 其他技能传空
            data_agent_pick_question_origin:
              AgentModeAiType[agentStore.aiType] !== 'data'
                ? ''
                : isFromRecommend.value && tempIsAgentHome
                  ? '推荐问题'
                  : '手动输入',
            // 分析时:问题来源 其他技能传空
            data_agent_analysis_question_origin:
              AgentModeAiType[agentStore.aiType] !== 'analyze'
                ? ''
                : currentSession.value?.scene || `scene不存在，请检查sessionId:${sessionId}`,
            ...(tracker || {}),
          }

          // 只有AI搜索模式下才传递问题来源统计
          if (agentStore.aiType === AgentModeAiType.search) {
            trackerParams.data_agent_question_source = getQuestionSource()
          }

          handleTracker('data_agent_session_send', trackerParams)
        }
        // 搜索消息直接返回
        if (agentStore.aiType === AgentModeAiType.search) {
          const searchAssistantMessage = getSearchAssistantMessage(agentMessageRes as any, {
            sessionId,
          })
          const tempMessage = sessionStore.getMessageById(id)
          if (tempMessage?.status !== 'STOPPED') {
            // 没有被停止的时候才执行
            sessionStore.updateMessage(id, {
              ...searchAssistantMessage,
              title: '为您找到如下数据内容: ',
            })
          }
          // sessionStore.addMessage(searchAssistantMessage as any)
          return
        }

        agentMessageInfo.value = agentMessageRes
        sessionStore.setAgentMessageInfo(agentMessageRes)
        // 2.3更新用户消息id
        sessionStore.updateMessage(userMessage.messageId, {
          messageId: agentMessageRes.sendMessageIds[0],
        })
        // 2.4更新用户消息状态为成功（发送成功）
        sessionStore.updateMessage(userMessage.messageId, { status: 'SUCCESS' })

        // 3.初始化返回的消息
        answerMessage = {
          messageId: agentMessageRes.answerMessageId,
          sessionId,
          // 根据version,决定分析的Title
          title: getMessageTitle(aiType.value, 'RUNNING', from, agentStore.aiType === AgentModeAiType.analyze),
          role: 'assistant' as const,
          content: {
            type: AgentMessageType[aiType.value as keyof typeof AgentMessageType],
          },
          type: 'text' as const,
          status: 'RUNNING' as MessageStatus,
          traceId: agentMessageRes.traceId,
        }
      }
      sessionStore.addMessage(answerMessage as any)

      emit('start')
    } catch (error: any) {
      console.error(error)
      toast.danger(error.data?.errorMsg ?? error ?? '发送消息失败')
    }
  }

  const handleStop = () => {
    if (isDataAsk.value) {
      setIsRunningDataAsk(false)
    }
    abortDataAsk()
    emit('stop')
    const sessionId = currentSession.value?.sessionId
    const lastMessageId = currentSession.value?.messages[currentSession.value?.messages.length - 1].messageId
    handleMessageStop(sessionId, lastMessageId, agentMessageInfo.value, agentStore, sessionStore)
  }

  const handleFocus = () => {
    isFocus.value = true
    if (isAgentHome.value && !inputValue.value) {
      historyVisible.value = true
    }
  }

  const handleBlur = () => {
    isFocus.value = false
  }

  const fetchHistoryList = async () => {
    historyLoading.value = true
    try {
      const params: any = {
        sessionType: agentStore.aiType,
        limit: 30,
      }
      if (agentStore.aiType === AgentModeAiType.analyze) {
        const agentCode = props.analysisData.find((item: any) => item.agentInfo.agentCode === activeAnalysisAgent.value)?.agentInfo.agentCode || 'CUSTOM_ANALYSIS'
        params.agentCode = agentCode
      }
      const res = await getHistoryMsgList(params)
      historyList.value = res
      historyLoading.value = false
    } catch (error: any) {
      toast.danger(error.data?.errorMsg ?? error ?? '获取历史记录失败')
    } finally {
      historyLoading.value = false
    }
  }

  // #region 数据提问
  const {
    sessionContext,
    isDataAsk,
    isRunningDataAsk,
    setIsRunningDataAsk,
    dataAskPlaceholder,
    agentModel,
    handleRemoveContext,
    getDataAskAnswerMessage,
    abortDataAsk,
    resetDataAsk,
    addContext,
    dataAskSuggestions,
    clickAskSuggestion,
  } = useDataAsk({
    initEditor,
    isMsgRunning,
    focusToEnd,
    handleSend,
  })

  defineExpose({
    addContext,
    agentModel,
    updatePlaceholder: (placeholder: string) => {
      dataAskPlaceholder.value = placeholder
    },
    setEditorValue: (value: string) => {
      if (isMsgRunning.value) {
        return
      }
      setIsRunningDataAsk(false)
      initEditor(value)
    },
    sendQuestion: () => {
      if (isMsgRunning.value) {
        return
      }
      handleSend()
    },
    focus: (end: boolean = true) => {
      nextTick(() => {
        // 设置选区到末尾
        if (end) {
          focusToEnd(editorRef.value?.$el)
        } else {
          editorRef.value?.$el.focus()
        }
      })
    },
    clearSlotValues: () => {
      inputSlotValues.value?.clear()
    },
  })

  // #endregion
  // 聚焦到末尾
  function focusToEnd(element: any) {
    if (!element) {
      element = editorRef.value?.$el
    }
    // 如果元素不是可编辑的，则不进行聚焦
    if (!element.getAttribute('contenteditable')) {
      return
    }
    const range = document.createRange()
    const sel = window.getSelection()
    range.selectNodeContents(element)
    range.collapse(false) // 设置为false将光标移动到末尾
    sel?.removeAllRanges()
    sel?.addRange(range)
    element.focus() // 确保元素获得焦点
  }

  const handleApplyHistory = (history: string) => {
    initEditor(history)
  }
  watch(() => [activeAnalysisAgent.value, props.analysisData], () => {
    if (agentStore.aiType === AgentModeAiType.analyze && agentStore.isAgentHome) {
      fetchHistoryList()
      showEditor.value = false
      nextTick(() => {
        showEditor.value = true
        calculateVisibleButtons()
      })
    }
  }, {
    immediate: true,
    deep: true,
  })
  // 切换ai类型时slate到placeholder没有变化，因此需要强制DOM更新
  watch(aiType, () => {
    if (agentStore.aiType !== AgentModeAiType.analyze) {
      fetchHistoryList()
    }
    showEditor.value = false
    nextTick(() => {
      showEditor.value = true
    })
  })

  watch(
    () => props.initialValue,
    newVal => {
      initEditor(newVal || '')
      if (newVal && isAgentHome.value) {
        // 有初始值 说明来自推荐问题
        isFromRecommend.value = true
        recommendQuestion.value = newVal
      }
    },
    {
      immediate: true,
    },
  )

  // 处理自动提交逻辑
  watch(() => props.shouldAutoSubmit, newVal => {
    if (newVal && inputValue.value.trim()) {
      // 延迟一下确保组件已经完全初始化
      nextTick(() => {
        handleSend('enter')
        emit('auto-submit-done')
      })
    }
  })
  // 监听用户输入
  watch(
    () => inputValue.value,
    newVal => {
      if (
        isAgentHome.value
        && !newVal
        && isFocus.value
      ) {
        historyVisible.value = true
      } else {
        historyVisible.value = false
      }
    },
  )

  // 点击新会话, 清空输入框
  watch(
    () => agentStore.isAgentHome,
    newVal => {
      if (newVal) {
        emit('reset-editor-input')
        resetEditor()
        resetDataAsk()
        fetchHistoryList()
        isFromRecommend.value = false
        recommendQuestion.value = ''
      }
    },
    {
      immediate: true,
    },
  )

  // 恢复历史记录的时候，清空输入框
  watch(
    () => route.query.historyQuerySessionId,
    nv => {
      if (nv) {
        emit('reset-editor-input')
        resetEditor()
      }
    },
  )

  // 点击重新生成, 重新发送
  watch(
    () => [sessionStore.regenerateMessage, props.initialValue],
    (newVal, oldVal) => {
      if (newVal[0] && newVal[1]) {
        initEditor(newVal[1] as string)
        // 如果是重新生成, 需要找上一条用户消息的plancontent一起发送
        let planContent = ''
        if (newVal[0] && !oldVal?.[0]) {
          const lastUserMessage = sessionStore.regenerateMessageInfo
          if (lastUserMessage) {
            planContent = (lastUserMessage.content as any)?.planContent
          }
        }
        nextTick(() => {
          handleSend(sendEvent.value, {}, planContent ? {
            planContent,
          } : { })
        })
      }
    },
    {
      immediate: true,
      deep: true,
    },
  )

  // 编辑器没有点击外部的事件，因此手动监听点击
  const handleClickScreen = (e: MouseEvent) => {
    const isClickInside = editorWrapRef.value?.contains(e.target as Node)
    const isClickInHistory = historyListRef.value?.$el?.contains(e.target as Node)
    const isClickInDatasetSelect = datasetSelectRef.value?.$el?.contains(e.target as Node)
    const isClickInSearchConfig = aiSearchConfigRef.value?.contains(e.target as Node)
    // 点击编辑器外部（历史列表除外，历史列表走点击事件关闭）,或点击选择数据集下拉或AI搜索范围下拉, 则隐藏历史记录列表
    if ((!isClickInside && !isClickInHistory) || isClickInDatasetSelect || isClickInSearchConfig) {
      historyVisible.value = false
    }
    // 点击外部，隐藏更多按钮下拉框
    if (manualVisible.value && !analysisAgentMoreBtnRef.value?.$el?.contains(e.target as Node)) {
      manualVisible.value = false
    }
  }
  onMounted(() => {
    document.addEventListener('click', handleClickScreen)
    if (!localStorage.getItem('agent_dataset_type')) {
      sessionStore.setDataSetType(sessionStore.defaultDatasetType)
    }
    if (agentStore.aiType === AgentModeAiType.analyze) {
      // ai分析只有会话页会首次挂在Editor，因此需要手动获取历史记录
      fetchHistoryList()
    }
    addListener('send-message', handlePureSend)
  })
  onUnmounted(() => {
    document.removeEventListener('click', handleClickScreen)
    removeListener('send-message', handlePureSend)
  })
</script>

<style lang="stylus" scoped>
.editor-wrap
  position relative
  width 100%
  box-sizing border-box
  .editor-content
    display flex
    flex-direction column
    gap 4px
    min-height 120px
    padding 12px
    box-sizing border-box
    border-radius 8px
    outline 1px solid #e5e5e5
    background-color #fff
    &.simple-mode
      height unset
      min-height unset
      position relative
      padding 12px 10px
    &.only-one-line
      height 45px
    &.data-ask
      height calc(120px + 32px)
    &.active
      outline 1px solid #4475e9
    .input-area
      flex 1
      height 68px
      outline none
      overflow-y auto
      color var(--color-text-title)
      font-size var(--size-text-small)
      line-height 20px
      :deep([data-slate-placeholder="true"])
        max-height 68px
      &.simple-mode
        height unset
        max-height 140px
        padding-right 28px
        :deep([data-slate-placeholder="true"])
          right 30px
          width: calc(100% - 30px)!important
          overflow hidden
          text-overflow ellipsis
          white-space nowrap
      &.only-one-line
        height 21px
        overflow hidden
      &.input-area-single
        height 75px
        flex auto
      &.apollo-analysis
        height 40px !important
        min-height auto !important
        flex auto
    .operation-area
      display flex
      align-items center
      justify-content flex-end
      flex 0 0 24px
      margin-top 12px
      .dataset-select
        width auto
        margin-right auto
        cursor pointer
        :deep(.d-select)
          border 1px solid #e5e5e5
          padding 0 4px
        :deep(.d-select .d-select-main)
          display flex !important
          justify-content space-between
          align-items center
          padding 2px
          column-gap 2px
      .search-config
        display flex
        justify-content center
        align-items center
        gap 2px
        padding 3px 4px 3px 6px
        margin-right auto
        cursor pointer
        border-radius 4px
        border 1px solid #e5e5e5
        background var(--background-bg, #FFF)
        color var(--color-text-paragraph)
        font-size 12px
      .send-btn
        color var(--color-primary)
        cursor pointer
        &:hover
          color var(--color-primary-hover)
        &:active
          color var(--color-primary-pressing)
        &.disabled
          color var(--color-primary-disabled)
          cursor not-allowed
      .stop-btn
        color var(--color-primary)
        cursor pointer
        &:hover
          color var(--color-primary-hover)
        &:active
          color var(--color-primary-pressing)
    .simple-mode-operation-area
      position absolute
      right 12px
      bottom 8px
      .send-btn
        color var(--color-primary)
        cursor pointer
        &:hover
          color var(--color-primary-hover)
        &:active
          color var(--color-primary-pressing)
        &.disabled
          color var(--color-primary-disabled)
          cursor not-allowed
      .stop-btn
        color var(--color-primary)
        cursor pointer
        &:hover
          color var(--color-primary-hover)
        &:active
          color var(--color-primary-pressing)
.analysis-agent-btn
  padding 0 8px !important
  :deep(.d-button-content)
    justify-content flex-start !important
    .d-text
      display flex
      align-items center
      gap 4px
.analysis-agent-btn.--color-bg-fill
  border-radius 4px
  padding 0 8px
  background-color var(--primary-light-default, rgba(235, 242, 255, 1))
  &:hover
    background-color var(--primary-light-hover, rgba(235, 242, 255, 1))
  &:active
    background-color var(--primary-light-pressing, rgba(235, 242, 255, 1))
.analysis-agent-btn.--color-bg-fill .d-text
  color var(--color-primary)
.analysis-buttons-container
  display flex
  flex-wrap nowrap
  gap 4px
  flex 1
</style>
<style lang="stylus">
  .ai-search-config-form-wrapper
    padding 12px 16px
    .d-new-form
      gap 8px
      .d-form-item__label .d-form-item__title
        color var(--color-text-description)
        font-weight var(--size-text-font-weight-default)
    .d-radio-group
      column-gap 16px
    .ai-search-popover-footer
      display flex
      justify-content flex-end
      align-items center
      gap 8px
      margin-top 16px
      .d-button
        min-height 28px
        padding 4px 12px
  .analysis-agent-popover-content
    display flex
    flex-direction column
    padding 4px
    gap 4px
    max-height 300px
    overflow-y auto
</style>
