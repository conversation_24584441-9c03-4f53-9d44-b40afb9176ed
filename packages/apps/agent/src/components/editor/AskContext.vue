<template>
  <div class="ask-context">
    <div
      v-for="item in context"
      :key="item.title"
      class="ask-context-item"
      :class="{
        'is-preview': mode === 'preview',
      }"
    >
      <Space
        size="small"
        block
      >
        <Icon
          :size="10"
          :icon="CornerDownRight"
        />
        <Text
          ellipsis
          :tooltip="mode === 'preview' ? `${item.title}` : '你可以基于这份数据向AI提问'"
        >{{ item.type === DataAskType.chart ? '图表: ' : item.type === DataAskType.analysis ? '自助分析: ' : '' }}{{ item.title }}</Text>
        <Icon
          v-if="closeable"
          :size="14"
          class="ask-context-item-close"
          :icon="Close"
          @click="handleClose(item)"
        />
        <Tooltip
          v-if="mode === 'preview'"
        >
          <template #content>
            点击前往自助分析
          </template>
          <Icon
            :size="14"
            class="ask-context-item-jump"
            :icon="ArrowRight"
            @click="handleClick(item)"
          />
        </Tooltip>

      </Space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    Text, Icon, Space, Tooltip,
  } from '@xhs/delight'
  import { SessionContext, DataAskType } from '@agent/types'
  import { Close, CornerDownRight, ArrowRight } from '@xhs/delight/icons'

  const props = withDefaults(defineProps<{
    context: SessionContext[]
    closeable?: boolean
    mode?: 'preview' | 'edit'
  }>(), {
    closeable: true,
    mode: 'edit',
  })

  const emit = defineEmits<{(e: 'remove', item: SessionContext): void
  }>()

  const handleClose = (item: SessionContext) => {
    emit('remove', item)
  }

  const handleClick = (item: SessionContext) => {
    if (props.mode === 'preview') {
      window.open(item.analysisUrl, '_blank')
    }
  }
</script>
<style lang="stylus" scoped>
  .ask-context {
    display: flex;
    gap: 10px;

    .ask-context-item {
      background: #00000008;
      border-radius: 6px;
      max-width: 400px;
      padding: 4px 8px;
      --color-text-paragraph: #000000D9;

      &.is-preview {
        padding 7px 8px 7px 12px;
      }
    }

    .ask-context-item-close,
    .ask-context-item-jump {
      cursor: pointer;
      padding 2px;
      margin-left: -2px;
    }

  }
</style>
<style lang="stylus">
  .data-agent-container-aside
    .ask-context
      .ask-context-item
        max-width 300px !important
</style>
