<template>
  <Space class="session-type-tag-list">
    <Tag
      v-for="item in sessionTypeTagList"
      :key="item[1]"
      :color="sessionType === +item[0] ? 'blue' : undefined"
      :class="{ default: sessionType !== +item[0] }"
      @click="() => handleSessionTypeChange(+item[0])"
    >
      {{ item[1] }}
    </Tag>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Tag } from '@xhs/delight'
  import { SessionTypeTagMap } from '@agent/types'
  import { useSessionStore } from '@agent/store/sessionStore'

  const sessionTypeTagList = Object.entries(SessionTypeTagMap)

  const emits = defineEmits(['change-session-type'])

  const sessionStore = useSessionStore()
  const sessionType = ref(sessionStore.historyTagType)

  const handleSessionTypeChange = (type: number) => {
    sessionType.value = type
    sessionStore.setHistoryTagType(type)
    emits('change-session-type', type)
  }
</script>

<style lang="stylus" scoped>
  .session-type-tag-list
    width 100%
    padding-left 12px
    :deep(.d-tag)
      cursor pointer
      &.default
        background-color rgba(0, 0, 0, 0.03)
</style>
