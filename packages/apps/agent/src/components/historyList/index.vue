<template>
  <Drawer
    v-if="modeType === 'single'"
    v-model:visible="visibleSingle"
    :size="320"
    title="历史会话"
    class="data-agent-history-list"
    @close="handleClose"
  >
    <SessionTypeTags @change-session-type="handleChangeSessionType" />
    <HistoryContent
      :list-data="listData"
      :mode-type="modeType"
      :is-loading="isLoading"
      @open-history-query="handleHistoryQuery"
    />
  </Drawer>
  <div
    v-else
    v-show="visibleAside"
    class="data-agent-history-list-aside"
    :class="{ show: visibleAside }"
  >
    <div class="history-list-aside-main">
      <div class="history-list-aside-header">
        <Text class="history-list-aside-header-title">历史会话</Text>
        <Icon
          class="pointer"
          :icon="Close"
          @click="handleClose"
        />
      </div>
      <div
        class="history-list-aside-content"
      >
        <HistoryContent
          :list-data="listData"
          :mode-type="modeType"
          :is-loading="isLoading"
          @open-history-query="handleHistoryQuery"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { groupBy } from 'lodash'
  import {
    Drawer, Text, Icon, toast2,
  } from '@xhs/delight'
  import {
    Close,
  } from '@xhs/delight/icons'
  import { useAgentStore } from '@agent/store/agentStore'
  import { getHistoryRecordList } from '@agent/services/basicInfo'
  import { useRoute } from 'vue-router'
  import dayjs from 'dayjs'
  import { addListener } from '@xhs/redbi-share-utils'
  import { AgentModeAiType } from '@agent/types'
  import { useDataAgentTrackerStore } from '@analysis/stores/dataAgentTrackerStore'
  import { useSessionStore } from '@agent/store/sessionStore'
  import HistoryContent from './HistoryContent.vue'
  import SessionTypeTags from './SessionTypeTags.vue'

  const agentStore = useAgentStore()
  const modeType = ref<string>(agentStore.mode)
  const visibleSingle = ref(false)
  const visibleAside = ref(false)
  const listData = ref<any>()
  const isLoading = ref(false)
  const route = useRoute()
  const sessionStore = useSessionStore()

  const { handleTracker } = useDataAgentTrackerStore()

  const emits = defineEmits(['openHistoryQuery'])
  const props = withDefaults(defineProps<{
    isScreen: boolean
  }>(), {
    isScreen: false,
  })
  // 根据日期获取日期范围，并进行分类
  const getDateRange = (date: string) => {
    const targetDate = dayjs(date)
    const today = dayjs()

    if (targetDate.isSame(today, 'day')) {
      return 'today'
    }
    // 计算7天前的日期
    const sevenDaysAgo = today.subtract(7, 'day')

    if (targetDate.isAfter(sevenDaysAgo)) {
      return 'last7Days'
    }

    return 'after7Days'
  }

  const handleClose = () => {
    if (modeType.value === 'single') {
      visibleSingle.value = false
    } else {
      visibleAside.value = false
    }
    if (props.isScreen) {
      visibleSingle.value = false
      visibleAside.value = false
    }
  }
  const handleHistoryQuery = (item: any, type: string) => {
    if (route.query.historyQuerySessionId === `${item?.sessionId}`) {
      handleClose()
      return
    }

    // 点击历史会话 添加埋点
    handleTracker('data_agent_historic_records', {
      data_agent_query: item?.title?.replace('_(解读)', ''),
      data_agent_skill_type: AgentModeAiType[item?.skillType || item?.sessionType],
      data_agent_session_id: String(item?.sessionId),
    })

    emits('openHistoryQuery', item, type)
    handleClose()
  }

  const filterHistoryList = (dataList: any, sessionType?: AgentModeAiType) => {
    const availableTimes = ['today', 'last7Days', 'after7Days']
    const groupedData = groupBy(dataList.filter((item: any) => {
      const hasTitle = item.title
      const matchesType = sessionType ? item.sessionType === sessionType : true
      return hasTitle && matchesType && availableTimes.includes(item.time)
    }), 'time')

    return Object.entries(groupedData)
      .sort(([key1]: any, [key2]:any) => availableTimes.findIndex(time => time === key1) - availableTimes.findIndex(time => time === key2))
      .reduce((acc: any, [key, value]) => {
        if (value.length > 0) {
          acc[key] = value
        }
        return acc
      }, {})
  }

  const fetchHistoryList = (sessionType?: number) => {
    isLoading.value = true
    let sessionTypeStatus = sessionType || sessionStore.historyTagType || undefined
    if (props.isScreen || modeType.value === 'aside') {
      sessionTypeStatus = sessionType || undefined
    }
    getHistoryRecordList({
      sessionType: sessionTypeStatus,
      pageNo: 1,
      pageSize: 1000,
    }).then((res: any) => {
      const dataList = res.dataList.map((item: any) => ({
        ...item,
        skillType: item.title?.endsWith('_(解读)') ? 4 : item.sessionType, // 会话类型,如果是以_(解读)结尾, 则作为数据解读处理
        time: getDateRange(item.changedOn || item.createdOn),
      }))

      if (props.isScreen || modeType.value === 'aside') {
        listData.value = filterHistoryList(dataList, AgentModeAiType.data)
      } else {
        listData.value = filterHistoryList(dataList)
      }
    }).catch((err: any) => {
      toast2.danger('获取历史记录失败')
      console.error('err', err)
    }).finally(() => {
      isLoading.value = false
    })
  }

  const handleOpenHsitory = (mode: string) => {
    modeType.value = mode
    if (modeType.value === 'single') {
      visibleSingle.value = true
    } else {
      visibleAside.value = true
    }
    if (props.isScreen) {
      visibleSingle.value = true
      visibleAside.value = true
    }

    fetchHistoryList()
  }

  const handleChangeSessionType = (sessionType: number) => {
    fetchHistoryList(sessionType)
  }

  addListener('close-history', () => {
    handleClose()
  })
  defineExpose({
    handleOpenHsitory,
  })
</script>
<style lang="stylus" scoped>
  .history-list-main
    .history-list-main-time
      font-size 12px
      color #999
      line-height 40px
    .history-list-main-card
      cursor pointer
      padding 3px 6px
      &:hover
        background var(--fill-default, #00000008)
      .history-list-main-text
        .history-list-main-type
          display inline-block
          border-radius 4px
          border 1px solid #ddd
          padding 2px 4px
          margin-right 8px
          line-height 14px
          width 52px
        .history-list-main-title
          font-size 12px
          color #333
          line-height 20px
          font-weight 500
          max-width 200px
          display inline-block
      .history-list-main-desc
        font-size 12px
        color #666
        line-height 20px
        max-width 200px
        display inline-block
        margin-left 60px

</style>
<style lang="stylus" scoped>
  .data-agent-history-list-aside
    width 360px
    height 100%
    background-color rgba(0, 0, 0, 0.5)
    border-radius 4px
    position absolute
    bottom 0
    right 0
    transform translateY(100%)
    transition transform 0.3s ease-in-out
    &.show
      transform translateY(0)
    .history-list-aside-main
      width 356px
      height 80%
      background-color #fff
      border-radius 12px 12px 0 0
      position absolute
      bottom 0
      right 2px
      padding 0
      box-sizing border-box
      .history-list-aside-header
        display flex
        justify-content space-between
        align-items center
        padding 20px
        .history-list-aside-header-title
          font-weight 500
          font-size 14px
          line-height 22px
        .pointer
          cursor pointer
      .history-list-aside-content
        height calc(100% - 64px)
        overflow-y auto
        padding 10px
        box-sizing border-box
</style>
<style lang="stylus">
  .data-agent-history-list
    z-index 10000!important
  .data-agent-history-list .d-meta-wrapper
    padding 0!important
  .data-agent-history-list .d-meta-wrapper .d-meta
    border none!important
  .data-agent-history-list .d-drawer-content
    padding 0 10px 10px!important
</style>
