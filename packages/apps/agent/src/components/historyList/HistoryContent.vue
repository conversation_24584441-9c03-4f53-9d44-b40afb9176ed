<template>
  <div
    v-for="(item, key) in listData"
    :key="key"
    :class="['history-list-main', modeType === 'single' ? 'history-list-main-single' : 'history-list-main-aside']"
  >
    <Text class="history-list-main-time">{{ HistoryDateMap[key] }}</Text>
    <!-- 消息列表 -->
    <div
      v-for="(child, keys) in item"
      :key="keys"
      class="history-list-main-card"
      @click="() => handleHistoryQuery(child)"
    >
      <div class="history-list-main-text">
        <Tag
          color="white"
          size="small"
          class="history-list-main-type"
        >{{ HistoryTypeMap[child.sessionType]?.slice(2) }}</Tag>
        <Tooltip
          class="history-list-main-title-tooltip"
          :content="child.title"
        >
          <Text
            class="history-list-main-title"
            ellipsis
          >{{ child.title }}</Text>
        </Tooltip>
      </div>
      <!-- 数据集 -->
      <!-- <Text class="history-list-main-desc">{{ item.datasetList.join(',') }}</Text> -->
    </div>
  </div>
  <div
    v-if="!listData"
    class="history-list-main-result"
  >
    <div
      v-if="isLoading"
      v-loading="isLoading"
    />
    <Result
      v-else
      status="resultless"
      title="暂无内容"
    />
  </div>
</template>

<script setup lang="ts">
  import {
    Text, Tag, Result, Tooltip,
  } from '@xhs/delight'
  import { HistoryTypeMap } from '@agent/types'

  const props = defineProps<{
    listData: any
    modeType: string
    isLoading: boolean
  }>()

  const HistoryDateMap: any = {
    today: '今天',
    last7Days: '近7天',
    after7Days: '7天前',
  }
  const emits = defineEmits(['openHistoryQuery'])

  const handleHistoryQuery = (item: any) => {
    emits('openHistoryQuery', item, props.modeType)
  }

</script>
<style lang="stylus" scoped>
  .history-list-main
    width 300px
    .history-list-main-time
      font-size 12px
      color #999
      line-height 40px
      padding-left 12px
    .history-list-main-card
      width 100%
      cursor pointer
      padding 5px 12px
      margin-bottom 12px
      border-radius 4px
      &:hover
        background var(--fill-default, #00000008)
      .history-list-main-text
        .history-list-main-type
          margin-right 8px
          :deep(.d-tag-content)
            line-height 16px
        .history-list-main-title
          font-size 12px
          color #333
          line-height 20px
          font-weight 500
          max-width 230px
          display inline-block
      // .history-list-main-desc
      //   font-size 12px
      //   color #666
      //   line-height 20px
      //   max-width 200px
      //   display inline-block
      //   margin-left 60px
  .history-list-main-aside
    width 335px
    .history-list-main-card
      margin-bottom 8px
      .history-list-main-text
        .history-list-main-title
          max-width 260px
  .history-list-main-result
    padding-top 200px
</style>
<style lang="stylus">
  .history-list-main-title-tooltip
    z-index 10001!important
</style>
