<template>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <foreignObject
      x="-3.38623"
      y="-4.52906"
      width="22.7725"
      height="25.0581"
    >
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        style="
          backdrop-filter: blur(2.26px);
          clip-path: url(#bgblur_0_3370_79376_clip_path);
          height: 100%;
          width: 100%;
        "
      />
    </foreignObject>
    <g
      filter="url(#filter0_i_3370_79376)"
      data-figma-bg-blur-radius="4.52906"
    >
      <path
        d="M1.14282 1.71429C1.14282 0.767512 1.91033 0 2.85711 0H11.3243C11.7585 0 12.1765 0.164775 12.4939 0.461048L14.3125 2.15838C14.6599 2.48259 14.8571 2.93645 14.8571 3.41161V14.2857C14.8571 15.2325 14.0896 16 13.1428 16H2.85711C1.91033 16 1.14282 15.2325 1.14282 14.2857V1.71429Z"
        fill="#D0DAFF"
      />
    </g>
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.99967 2.87964C6.65261 2.87964 5.4602 3.15385 4.62484 3.57153C3.75662 4.00564 3.42847 4.50386 3.42847 4.89107C3.42847 5.27827 3.75662 5.77649 4.62484 6.2106C5.4602 6.62828 6.65261 6.90249 7.99967 6.90249C9.34673 6.90249 10.5391 6.62828 11.3745 6.2106C12.2427 5.77649 12.5709 5.27827 12.5709 4.89107C12.5709 4.50386 12.2427 4.00564 11.3745 3.57153C10.5391 3.15385 9.34673 2.87964 7.99967 2.87964ZM11.8653 7.19112C12.1623 7.04266 12.3934 6.85636 12.5713 6.64387V8.1817H12.5711C12.5711 8.5689 12.2429 9.06712 11.3747 9.50127C10.5393 9.91891 9.34695 10.1931 7.9999 10.1931C6.65284 10.1931 5.46043 9.91891 4.62507 9.50127C3.75685 9.06712 3.42869 8.5689 3.42869 8.1817H3.42847V6.64387C3.60641 6.85636 3.83748 7.04266 4.13441 7.19112C5.15202 7.69993 6.51944 7.99881 7.9999 7.99884C9.48038 7.99881 10.8478 7.69993 11.8653 7.19112ZM3.42847 11.1622V9.93684C3.60641 10.1493 3.83748 10.3356 4.13441 10.4841C5.15202 10.9929 6.51944 11.2918 7.9999 11.2918C9.48038 11.2918 10.8478 10.9929 11.8653 10.4841C12.1623 10.3356 12.3934 10.1493 12.5713 9.93684V11.1622L12.5639 11.2152C12.5095 11.6 12.1244 12.0796 11.2649 12.4817C10.4354 12.8698 9.28756 13.1204 7.9999 13.1204C6.71223 13.1204 5.56438 12.8698 4.73484 12.4817C3.87538 12.0796 3.49027 11.6 3.43594 11.2152L3.42847 11.1622Z"
      fill="#3C66FF"
    />
    <defs>
      <filter
        id="filter0_i_3370_79376"
        x="-3.38623"
        y="-4.52906"
        width="22.7725"
        height="25.0581"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood
          flood-opacity="0"
          result="BackgroundImageFix"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="31.7034" />
        <feComposite
          in2="hardAlpha"
          operator="arithmetic"
          k2="-1"
          k3="1"
        />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_3370_79376"
        />
      </filter>
      <clipPath
        id="bgblur_0_3370_79376_clip_path"
        transform="translate(3.38623 4.52906)"
      >
        <path
          d="M1.14282 1.71429C1.14282 0.767512 1.91033 0 2.85711 0H11.3243C11.7585 0 12.1765 0.164775 12.4939 0.461048L14.3125 2.15838C14.6599 2.48259 14.8571 2.93645 14.8571 3.41161V14.2857C14.8571 15.2325 14.0896 16 13.1428 16H2.85711C1.91033 16 1.14282 15.2325 1.14282 14.2857V1.71429Z"
        />
      </clipPath>
    </defs>
  </svg>
</template>
<script setup lang="ts"></script>
