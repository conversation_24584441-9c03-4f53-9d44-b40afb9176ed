<template>
  <Icon
    size="large"
    color="primary"
  >
    <svg
      :width="width"
      :height="height"
      viewBox="0 0 14 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.15179 5.4605L10.0928 2.09856M8.15179 5.4605L12.1684 4.38424M8.15179 5.4605L12.6899 8.08055M8.15179 5.4605L9.90776 12.0139M8.15179 5.4605L4.29666 12.1378M8.15179 5.4605L1.19922 7.32343M8.15179 5.4605L3.20561 2.60482M8.15179 5.4605L7.01019 1.19995"
        :stroke="`url(#paint0_linear_1090_${id})`"
        stroke-width="1.6"
        stroke-linecap="round"
      />
      <defs>
        <linearGradient
          :id="`paint0_linear_1090_${id}`"
          x1="4.27199"
          y1="12.4544"
          x2="10.0686"
          y2="0.956419"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset="0.5"
            :stop-color="color || '#2372FB'"
          />
          <stop
            offset="1"
            :stop-color="color || '#51E8F6'"
          />
        </linearGradient>
      </defs>
    </svg>
  </Icon>
</template>
<script setup lang="ts">
  import { Icon } from '@xhs/delight'
  import { nanoid } from 'nanoid'

  const id = nanoid()
  defineProps<{
    width: string
    height: string
    color?: string
  }>()
</script>
