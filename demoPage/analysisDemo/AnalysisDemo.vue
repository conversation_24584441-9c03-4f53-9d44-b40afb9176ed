<template>
  <div class="analysis-demo">
    <!-- <Button @click="handleAdd">新建(点击数据集)</Button>
    <Button @click="handleJump">跳转(点击模板)</Button>
    <Button @click="handleSaveAs">另存为</Button> -->
    <AnalysisView
      ref="analRendererRef"
      :chart-id="(undefined as any)"
      @query-success="handleQueryFinished"
      @query-failed="handleQueryFailed"
    />
  </div>

</template>
<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  // import { Button } from '@xhs/delight'
  import AnalysisView from '@analysis/pages/AnalysisView/AnalysisView.vue'

  const analRendererRef = ref()

  // // 打开新的分析，并选中目录对应的数据集
  // const handleAdd = () => {
  //   analRendererRef.value.jumpToNewTab({
  //     options: {
  //       datasetId: 9193,
  //       datasetName: 'kkk',
  //       nodeId: 11122,
  //       useExist: true, // 使用当前用户已有的空分析id
  //     },
  //   })
  // }

  // 跳转到目录对应的模版 & 看板触发查询
  const handleJump = () => {
    analRendererRef.value.jumpToTemplate(29772) // 29132 12683 29318
  }

  // 查询成功回调
  const handleQueryFinished = ({ res }: any) => {
    // 返回查询res
    // eslint-disable-next-line no-console
    console.log('finished', res)
  }
  // 查询失败回调
  const handleQueryFailed = ({ err }: any) => {
    // 返回失败err
    console.error('finished', err)
  }
  onMounted(() => {
    handleJump()
  })
</script>
<style lang="stylus" scoped>
.analysis-demo
  width calc(100wh - 32px)
  height calc(100vh - 32px)
  margin 16px
</style>
