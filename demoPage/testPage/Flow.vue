<template>
  <div class="demo">
    <Button @click="handleClick">触发任务</Button>
    <Button @click="handleClickAsync">触发异步任务</Button>
  </div>
</template>
<script setup lang="ts">
  /**
   * @note 代码片段生成请替换为文件描述
   * <AUTHOR>
   * @date 代码片段生成
   * @Last Modified by: ch<PERSON><PERSON><PERSON>@xiaohongshu.com
   * @Last Modified time: 2024-01-12 15:04:49
   */
  import { Button } from '@xhs/delight'
  import { Flow, demo, FlowContext } from '@xhs/redbi-share-dsl'

  // 同步任务模块示例
  function demo1(val: any, ctx: FlowContext) {
    ctx.log.info('日志测试')
    val.push({ demo1: 333, ...(ctx.taskConfig || {}) })
    return val
  }

  // 异步任务模块示例
  function demo2(val: any, ctx: FlowContext) {
    return new Promise(reslove => {
      setTimeout(() => {
        val.push({ demo2: 333, ...(ctx.taskConfig || {}) })
        reslove(val)
      }, 1000)
    })
  }

  // 初始化任务流
  const flow = new Flow({ debug: true })

  // 同步任务任务管道处理
  function handleClick() {
    // 初始数据
    const initValue = [
      {
        test: 1,
      },
      {
        test: 2,
      },
    ]

    const result = flow.pipe(
      [
        demo,
        demo1,
        [demo1, { config: 3 }],
        val => {
          val.push({ ttt: 333 })
          return val
        },
        [demo1, { config: 4 }],
      ],
      initValue,
    )
    // 同步处理
    // eslint-disable-next-line no-console
    console.log('同步任务最终处理数据为', result)
  }

  // 异步管道示例
  function handleClickAsync() {
    // 初始数据
    const initValue = [
      {
        test: 1,
      },
      {
        test: 2,
      },
    ]
    // 异步处理
    flow
      .pipeWithAsync(
        [
          demo,
          [demo1, { config: 1 }],
          [demo2, { config: 'demo2-1' }],
          demo2,
          val => {
            val.push({ ttt: 333 })
            return val
          },
          [demo2, { config: 'demo2-2' }],
          [demo1, { config: 2 }],
        ],
        initValue,
      )
      .then(result => {
        // eslint-disable-next-line no-console
        console.log('异步任务最终处理数据为', result)
      })
  }
</script>

<style lang="stylus" scoped>

  .demo
    display grid
</style>
