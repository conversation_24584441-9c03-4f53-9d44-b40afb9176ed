<template>
  <div class="ctrl-a-test-container">
    <div class="controls">
      <Button
        type="primary"
        @click="simulateCtrlA"
      >
        模拟 Ctrl+A
      </Button>
      <Button @click="selectAll">
        全选
      </Button>
      <Button @click="copyAll">
        复制全部
      </Button>
    </div>

    <div class="content-area">
      <div
        ref="contentRef"
        class="test-content"
        tabindex="0"
        @keydown="handleKeydown"
      >
        <h1>测试标题</h1>
        <p>这是一个<strong>粗体文本</strong>和<em>斜体文本</em>的段落。</p>
        <ul>
          <li>列表项目 1</li>
          <li>列表项目 2</li>
          <li>列表项目 3</li>
        </ul>
        <blockquote>
          这是一个引用块，包含一些重要的信息。
        </blockquote>
        <p>最后一段包含<code>代码片段</code>和<a href="#">链接</a>。</p>
      </div>
    </div>

    <div class="status">
      <p>选中内容: {{ selectedText || '无' }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Button, toast2 } from '@xhs/delight'

  const contentRef = ref<HTMLElement>()
  const selectedText = ref('')

  onMounted(() => {
    // 监听选择变化
    document.addEventListener('selectionchange', updateSelectedText)
  })

  const updateSelectedText = () => {
    const selection = window.getSelection()
    if (selection?.toString().trim()) {
      selectedText.value = selection.toString()
    } else {
      selectedText.value = ''
    }
  }

  // 处理键盘事件
  const handleKeydown = (event: KeyboardEvent) => {
    // 检测 Ctrl+A
    if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
      event.preventDefault()
      selectAll()
    }
    // 检测 Ctrl+C
    if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
      event.preventDefault()
      copySelected()
    }
  }

  // 模拟 Ctrl+A 行为
  const simulateCtrlA = () => {
    selectAll()
    setTimeout(() => {
      copySelected()
    }, 100)
  }

  // 全选功能
  const selectAll = () => {
    if (!contentRef.value) return

    try {
      const selection = window.getSelection()
      const range = document.createRange()

      // 清除当前选择
      selection?.removeAllRanges()

      // 选择整个内容
      range.selectNodeContents(contentRef.value)
      selection?.addRange(range)

      toast2.success('已全选内容')
    } catch (error) {
      console.error('全选失败:', error)
      toast2.warning('全选失败')
    }
  }

  // 复制选中内容
  const copySelected = () => {
    try {
      const selection = window.getSelection()
      if (!selection || !selection.toString().trim()) {
        toast2.warning('没有选中内容')
        return
      }

      // 使用现代 Clipboard API
      if (navigator.clipboard && window.ClipboardItem) {
        // 获取选中的 HTML 内容
        const range = selection.getRangeAt(0)
        const container = document.createElement('div')
        container.appendChild(range.cloneContents())
        const htmlContent = container.innerHTML

        // 创建 ClipboardItem
        const htmlBlob = new Blob([htmlContent], { type: 'text/html' })
        const textBlob = new Blob([selection.toString()], { type: 'text/plain' })

        const clipboardItem = new ClipboardItem({
          'text/html': htmlBlob,
          'text/plain': textBlob,
        })

        navigator.clipboard.write([clipboardItem]).then(() => {
          toast2.success('选中内容已复制到剪贴板')
        }).catch(() => {
          copyToClipboard(selection.toString())
        })
      } else {
        copyToClipboard(selection.toString())
      }
    } catch (error) {
      console.error('复制失败:', error)
      toast2.warning('复制失败')
    }
  }

  // 复制全部内容
  const copyAll = () => {
    if (!contentRef.value) return

    try {
      const textContent = contentRef.value.textContent || ''
      const htmlContent = contentRef.value.innerHTML

      // 使用现代 Clipboard API
      if (navigator.clipboard && window.ClipboardItem) {
        const htmlBlob = new Blob([htmlContent], { type: 'text/html' })
        const textBlob = new Blob([textContent], { type: 'text/plain' })

        const clipboardItem = new ClipboardItem({
          'text/html': htmlBlob,
          'text/plain': textBlob,
        })

        navigator.clipboard.write([clipboardItem]).then(() => {
          toast2.success('全部内容已复制到剪贴板')
        }).catch(() => {
          copyToClipboard(textContent)
        })
      } else {
        copyToClipboard(textContent)
      }
    } catch (error) {
      console.error('复制失败:', error)
      toast2.warning('复制失败')
    }
  }

  // 传统的复制方法
  const copyToClipboard = (text: string) => {
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)

      textArea.focus()
      textArea.select()
      const success = document.execCommand('copy')

      document.body.removeChild(textArea)

      if (success) {
        toast2.success('内容已复制到剪贴板')
      } else {
        toast2.warning('复制失败')
      }
    } catch (error) {
      console.error('传统复制失败:', error)
      toast2.warning('复制失败')
    }
  }
</script>

<style lang="stylus" scoped>
.ctrl-a-test-container
  padding: 20px
  max-width: 800px
  margin: 0 auto

.controls
  margin-bottom: 20px
  display: flex
  gap: 10px

.content-area
  border: 1px solid #ddd
  border-radius: 4px
  padding: 20px
  margin-bottom: 20px
  background: #fff

.test-content
  outline: none
  user-select: text
  -webkit-user-select: text
  -moz-user-select: text
  -ms-user-select: text

  *
    user-select: text
    -webkit-user-select: text
    -moz-user-select: text
    -ms-user-select: text

.status
  background: #f5f5f5
  padding: 15px
  border-radius: 4px

  p
    margin: 5px 0
    font-family: monospace
</style>
