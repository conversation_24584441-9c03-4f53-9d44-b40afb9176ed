<template>
  <div class="redoc-container">
    <div class="copy-controls">
      <Button @click="copyHTML">复制HTML</Button>
    </div>

    <red-editor
      :value="docValue"
      :box-style="style"
      :is-image-click-preview-on="false"
      readonly
      @reditorinject="inject"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, toast2 } from '@xhs/delight'
  import copy from 'copy-to-clipboard'
  import { redocExample } from './md2redoc'

  const docValue = ref(JSON.stringify(redocExample))
  const style = JSON.stringify({
    width: '100%',
    // border: '1px solid red',
    borderRadius: '4px',
    backgroundColor: '#f7f7f7',
    padding: '0px',
  })

  // @ts-ignore
  const inject = () => {
    // 注入完成后启用选择

    setTimeout(() => {
      const redocElement = document.querySelector('red-editor')
      if (!redocElement) {
        toast2.warning('未找到渲染组件')
        return
      }
      const editorContent = redocElement.querySelector('.sdk-editor') || redocElement

      if (!editorContent) {
        toast2.warning('无法获取渲染内容')
        return
      }
      // 直接操作 DOM 节点，转换 iframe 为 a 标签
      convertIframeToAnchor(editorContent)
    }, 1000)
  }

  // 复制纯HTML内容
  const copyHTML = () => {
    try {
      const redocElement = document.querySelector('red-editor')
      if (!redocElement) {
        toast2.warning('未找到渲染组件')
        return
      }

      const editorContent = redocElement.querySelector('.sdk-editor') || redocElement

      if (!editorContent) {
        toast2.warning('无法获取渲染内容')
        return
      }

      // 复制转换后的 HTML 内容
      const htmlContent = editorContent.innerHTML

      if (copy(htmlContent, { format: 'text/html' })) {
        toast2.success('HTML内容已复制到剪贴板')
      } else {
        toast2.warning('复制失败')
      }
    } catch (error) {
      console.error('复制HTML失败:', error)
      toast2.warning('复制失败')
    }
  }

  // 将 iframe 转换为 a 标签的函数 - 直接操作 DOM 节点
  const convertIframeToAnchor = (editorContent: Element): void => {
    // 查找所有 iframe 元素
    const iframes = editorContent.querySelectorAll('iframe')

    iframes.forEach(iframe => {
      const src = iframe.getAttribute('src')
      if (!src) {
        return // 如果没有 src，跳过
      }

      // 解码 HTML 实体
      const decodedSrc = src.replace(/&amp;/g, '&')

      // 查找最近的 data-slate-node="element" 父级节点
      const slateElement = findClosestSlateElement(iframe)
      if (!slateElement) {
        return // 如果找不到 slate 元素，跳过
      }

      // 创建新的 a 标签
      const anchorElement = createAnchorElement(decodedSrc)

      // 替换整个 slate 元素
      slateElement.parentNode?.replaceChild(anchorElement, slateElement)
    })
  }

  // 查找最近的 data-slate-node="element" 父级节点
  const findClosestSlateElement = (element: Element): Element | null => {
    let current = element

    while (current && current !== document.body) {
      if (current.getAttribute('data-slate-node') === 'element') {
        return current
      }
      current = current.parentElement as Element
    }

    return null
  }

  // 创建 a 标签元素
  const createAnchorElement = (src: string): HTMLAnchorElement => {
    const anchor = document.createElement('a')

    // 设置基本属性
    anchor.href = src
    anchor.setAttribute('data-title', 'RedBI')
    anchor.setAttribute('data-view-type', 'preview')
    anchor.target = '_blank'

    // 创建 span 元素
    const span = document.createElement('span')
    span.textContent = src
    anchor.appendChild(span)

    return anchor
  }
</script>

<style lang="stylus" scoped>
  .redoc-example-container
    padding: 20px
    max-width: 1200px
    margin: 0 auto

  .copy-controls
    margin-bottom: 20px
    display: flex
    gap: 10px

  // RedocSDK 样式 - 参考项目中的实际用法
  :deep(.redoc-container)
    width: 100%
    padding: 6px
    position: relative

  :deep(.doc-title)
    display: none

  :deep(.sdk-editor)
    padding: 0
    // 启用文本选择
    user-select: text !important
    -webkit-user-select: text !important
    -moz-user-select: text !important
    -ms-user-select: text !important

  // 确保所有内容都可以被选择
  :deep(.sdk-editor *)
    user-select: text !important
    -webkit-user-select: text !important
    -moz-user-select: text !important
    -ms-user-select: text !important
</style>
