<template>
  <div class="tree-page">
    <Tree
      ref="treeRef"
      v-model:expanded-keys="expandedKeys"
      v-model:selected-keys="selectedKeys"
      :tree-data="treeData"
      :checkable="true"
      :default-expand-all="false"
      draggable
      auto-reslove-drag-data
      style="height: 500px; width: 200px"
      @check="handleCheck"
      @select="handleSelect"
      @dragstart="dragstart"
      @dragenter="dragenter"
    />
    <div>---</div>
    <Tree
      ref="treeRef1"
      v-model:expanded-keys="expandedKeys"
      v-model:selected-keys="selectedKeys"
      :tree-data="treeData"
      :checkable="true"
      :default-expand-all="false"
      draggable
      virtualize
      multiple-drag
      auto-reslove-drag-data
      style="height: 500px; width: 200px"
      @check="handleCheck"
      @select="handleSelect"
      @dragstart="dragstart"
      @dragenter="dragenter"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Tree } from '@xhs/delight-material-bi-ui'

  const expandedKeys = ref([])
  const selectedKeys = ref([])
  const treeRef = ref()
  const treeRef1 = ref()

  const handleCheck = (keys: any) => keys

  const handleSelect = (keys: any) => keys

  const dragstart = ({ node }: any) => {
    // 收缩当前拖拽的节点
    expandedKeys.value = expandedKeys.value.filter(key => key !== node.key)
  }

  const dragenter = () => {
    // 你可以在拖拽元素进入容器的时候，做一些你想做的事情，比如展开此节点
    // expandedKeys.value = [...new Set([...expandedKeys.value, key])];
  }
  const getKey = (prefix: string, id: string | number) => `${prefix}-${id}`

  const createData: any = (
    maxDeep: number,
    maxChildren: number,
    minNodesNumber: number,
    deep = 1,
    key = 'node',
  ) => {
    let id = 0
    return Array.from({ length: minNodesNumber })
      .fill(deep)
      .map(() => {
        const childrenNumber = deep === maxDeep ? 0 : maxChildren
        id += 1
        const nodeKey = getKey(key, id)
        return {
          key: nodeKey,
          title: nodeKey,
          canDrop: !!childrenNumber,
          // disabled: true,
          children: childrenNumber
            ? createData(maxDeep, maxChildren, childrenNumber, deep + 1, nodeKey)
            : undefined,
        }
      })
  }

  const treeData = createData(3, 10, 40)

  onMounted(() => {
    treeRef.value.scrollToItem('node-7-7-7')
    treeRef1.value.scrollToItem('node-7-7-7')
  })
</script>

<style lang="stylus" scoped>
  .tree-page
    width 100%
    height 100%
    padding 20px
    display flex
</style>
