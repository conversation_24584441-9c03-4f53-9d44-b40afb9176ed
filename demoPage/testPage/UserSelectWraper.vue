<template>
  <div class="test-page">
    <!-- <wc-demo
      :folder-type="55"
      msg="This is a message"
    /> -->
    <!-- <wc-measure-menu /> -->
    <div style="color: red">checkedKeys:</div>
    <div>{{ checkedKeys }}</div>
    <br>
    <div style="color: red">checkedUsers:</div>
    <div>{{ checkedUsers }}</div>

    <Space>
      <!-- v-model:checkedKeys="checkedKeys" -->
      <UserSelectTree
        v-model:checked-users="checkedUsers"
        @change="handleChange"
      />
      <div style="width: 600px">
        <UserSelectWraper v-model:checked-users="checkedUsers" />
      </div>
    </Space>
    <br>
    <br>
    <Button
      type="primary"
      @click="handleClick"
    >UserSelectModal</Button>
    <UserSelectModal
      v-model:visible="visible"
      :checked-users="checkedUsers"
      @confirm="handleConfirm"
    />
    <br>
    <br>
    <UserSelectInput
      v-model:checked-users="checkedUsers"
    />
    <br>
    <br>

    <div>checkedUserGroupIds: {{ checkedUserGroupIds }}</div>
    <div>checkedUserGroups: {{ checkedUserGroups }}</div>
    <Space>
      <UserGroup
        :checked-user-groups="checkedUserGroups"
        :node-id="1596"
        type="dataset"
        @change="handleUserGroupChange"
      />
      <div style="width: 600px">
        <UserGroupWraper
          v-model:checked-user-groups="checkedUserGroups"
          :node-id="1596"
          type="dataset"
        />
      </div>
    </Space>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  import { Button, Space } from '@xhs/delight'
  import { SystemUserInfo, UserGroupInfo } from '@xhs/redbi-share-types'
  import {
    UserGroup, UserGroupWraper,
  } from '@xhs/redbi-share-components'
  import {
    UserSelectModal, UserSelectWraper, UserSelectTree, UserSelectInput,
  } from '@xhs/delight-material-bi-ui'

  const checkedKeys = ref<string[]>(['<EMAIL>'])

  const checkedUsers = ref<SystemUserInfo[]>([
    {
      departmentName: '小红书',
      departmentNames: ['小红书'],
      displayName: '北森接口专用 勿动',
      redEmail: '',
      userAvatar:
        'https://fe-static.xhscdn.com/formula-static/ones/public/img/user.2a4c4ae.jpg?from_wecom=1',
      userEmail: '<EMAIL>',
    },
    {
      userEmail: '<EMAIL>',
      redEmail: '<EMAIL>',
      displayName: '东初(刘赛)',
      userAvatar:
        'https://fe-static.xhscdn.com/formula-static/ones/public/img/user.2a4c4ae.jpg?from_wecom=1',
      internalUser: null,
      departmentName: '数据应用研发组',
      departmentNames: ['技术部', '数据平台部', '数据应用研发组'],
    },
  ])

  const handleChange = (users: SystemUserInfo[], keys: string[]) => {
    checkedUsers.value = users
    checkedKeys.value = keys
  }

  const visible = ref(false)
  const handleClick = () => {
    visible.value = true
  }

  const handleConfirm = (users: SystemUserInfo[], keys: string[]) => {
    checkedUsers.value = users
    checkedKeys.value = keys
  }

  const checkedUserGroupIds = ref<number[]>([])
  const checkedUserGroups = ref<UserGroupInfo[]>([
    {
      projectId: 4,
      userGroupId: 31,
      subProjectId: 0,
      subProjectName: '',
      userGroupName: 'R社区内容数据分析组',
      remarks: '观远迁移过来的用户组',
      canApply: true,
    },
  ])

  const handleUserGroupChange = (userGroups: UserGroupInfo[], userGroupIds: number[]) => {
    checkedUserGroupIds.value = userGroupIds
    checkedUserGroups.value = userGroups
  }
</script>

<style lang="stylus" scoped>
  .test-page
    width 100%
    height 100%
    padding 20px
</style>
