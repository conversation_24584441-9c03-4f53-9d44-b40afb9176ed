<template>
  <div class="test-wrap">
    <Menu
      :use-collapse="false"
      @click="handleClick"
    >
      <MenuItem
        key="MapBase"
        title="MapBase"
      />
      <MenuItem
        key="Tree"
        title="Tree"
      />
      <MenuItem
        key="UserSelectWraper"
        title="UserSelectWraper"
      />
      <MenuItem
        key="Flow"
        title="Flow"
      />
      <MenuItem
        key="QueryProvider"
        title="QueryProvider"
      />
      <MenuItem
        key="Md2Redoc"
        title="Md2Redoc"
      />
      <MenuItem
        key="RedocExample"
        title="RedocExample"
      />
      <MenuItem
        key="CtrlATest"
        title="CtrlATest"
      />
    </Menu>
    <div class="test-content">
      <component :is="targetComponent[currentKey]" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Menu2 as Menu, MenuItem2 as MenuItem } from '@xhs/delight'
  import { ref } from 'vue'
  import Tree from './Tree.vue'
  import UserSelectWraper from './UserSelectWraper.vue'
  import Flow from './Flow.vue'
  import MapBase from './MapBase.vue'
  import QueryProvider from './QueryProvider.vue'
  import Md2Redoc from './Md2Redoc.vue'
  import RedocExample from './RedocExample.vue'
  import CtrlATest from './CtrlATest.vue'

  const targetComponent: any = {
    Tree,
    UserSelectWraper,
    Flow,
    MapBase,
    QueryProvider,
    Md2Redoc,
    RedocExample,
    CtrlATest,
  }
  const currentKey = ref('MapBase')
  const handleClick = ({ key }: { key: string }) => {
    currentKey.value = key
  }
</script>

<style lang="stylus" scoped>
.test-wrap
  display flex
.test-content
  width calc(100vw - 256px)
  height 100%
  padding 20px
</style>
