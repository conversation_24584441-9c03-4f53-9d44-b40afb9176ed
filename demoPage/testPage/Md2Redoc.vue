<template>
  <div class="redoc-example-container">
    <div class="input-section">
      <Text class="section-title">Markdown 输入</Text>
      <TextArea
        v-model="markdownInput"
        placeholder="请输入 Markdown 内容..."
        :rows="8"
        class="markdown-input"
      />
      <Space>
        <Button @click="loadSampleMarkdown">加载示例</Button>
        <Button @click="copyHTML">复制HTML</Button>
      </Space>
    </div>

    <div
      :id="mdId"
      class="md-temp"
    >
      <Text class="section-title">Markdown 渲染结果</Text>
      <Markdown :content="{ text: markdownInput }" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    TextArea, Button, Space, Text, toast2,
  } from '@xhs/delight'
  import copy from 'copy-to-clipboard'
  import { Markdown } from '@xhs/techfe-bi-components'
  import { md as sampleMarkdown } from './md2redoc'

  // 响应式数据
  const markdownInput = ref<string>('')
  // 生成随机的 block-id
  const generateBlockId = (): string => Array.from({ length: 32 }, () => Math.floor(Math.random() * 16).toString(16)).join('')
  const mdId = ref<string>(generateBlockId())
  // 增强版的 wc-analysis-preview 转换函数
  const convertWcAnalysisPreviewToAnchorAdvanced = (htmlContent: string): string => {
    // 匹配 wc-analysis-preview 标签的正则表达式
    const wcAnalysisPreviewRegex = /<wc-analysis-preview[^>]*analysis-url="([^"]*)"[^>]*><\/wc-analysis-preview>/gi

    return htmlContent.replace(wcAnalysisPreviewRegex, (match, analysisUrl) => {
      if (!analysisUrl) {
        return match // 如果没有 analysis-url，保持原样
      }
      // 解码 HTML 实体
      const decodedUrl = analysisUrl
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')

      // 构建 a 标签
      return `<a href="${decodedUrl}" data-title="RedBI" data-view-type="preview" target="_blank"><span>${decodedUrl}</span></a>`
    })
  }

  // 清理 HTML 内容的函数
  const cleanHtmlContent = (html: string): string => (
    html
      // 移除多余的空白字符
      .replace(/\s+/g, ' ')
      // 移除标签之间的空白
      .replace(/>\s*</g, '><')
      // 移除开头和结尾的空白
      .trim()
      // 移除连续的换行符
      .replace(/\n\s*\n/g, '\n')
      // 移除 HTML 注释
      .replace(/<!--[\s\S]*?-->/g, '')
      // 移除空的 div 和 span
      .replace(/<(div|span)[^>]*>\s*<\/\1>/g, '')
      // 移除只包含空白字符的元素
      .replace(/<(div|span|p)[^>]*>\s*<\/\1>/g, '')
      // 移除多余的空格
      .replace(/\s{2,}/g, ' ')
  )

  // 复制纯HTML内容
  const copyHTML = () => {
    try {
      const redocElement = document.getElementById(mdId.value)
      if (!redocElement) {
        toast2.warning('未找到渲染组件')
        return
      }

      const editorContent = redocElement.querySelector('.markdown-body') || redocElement

      if (!editorContent) {
        toast2.warning('无法获取渲染内容')
        return
      }

      // 复制转换后的 HTML 内容
      let htmlContent = editorContent.innerHTML

      // 将 wc-analysis-preview 标签替换为 a 标签
      htmlContent = convertWcAnalysisPreviewToAnchorAdvanced(htmlContent)

      // 清理 HTML 内容，去除多余的空行和空白
      htmlContent = cleanHtmlContent(htmlContent)

      if (copy(htmlContent, { format: 'text/html' })) {
        toast2.success('HTML内容已复制到剪贴板')
      } else {
        toast2.warning('复制失败')
      }
    } catch (error) {
      console.error('复制HTML失败:', error)
      toast2.warning('复制失败')
    }
  }

  // 加载示例 Markdown
  const loadSampleMarkdown = () => {
    markdownInput.value = sampleMarkdown
    toast2.info('已加载示例内容')
  }
</script>

<style lang="stylus" scoped>
  .redoc-example-container
    padding: 20px
    max-width: 1200px
    margin: 0 auto

  .input-section,
  .redoc-section
    margin-bottom: 30px

  .section-title
    font-size: 18px
    font-weight: 600
    margin-bottom: 10px
    color: #333

  .markdown-input
    width: 100%
    margin-bottom: 10px

  .redoc-container
    border: 1px solid #ddd
    border-radius: 4px
    padding: 20px
    background: #fff
    min-height: 300px
    margin-bottom: 10px

  .placeholder
    display: flex
    align-items: center
    justify-content: center
    height: 200px
    color: #999
    font-size: 16px

  .slate-data-modal
    max-height: 500px
    overflow-y: auto

    pre
      margin: 0
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace
      font-size: 12px
      line-height: 1.4
      white-space: pre-wrap
      word-break: break-all

  // RedocSDK 样式 - 参考项目中的实际用法
  :deep(.redoc-container)
    width: 100%
    padding: 6px
    position: relative

  :deep(.doc-title)
    display: none

  :deep(.sdk-editor)
    padding: 0
</style>
