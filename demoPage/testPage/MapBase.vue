<script lang="ts" setup>
  import { ref, onMounted } from 'vue'
  import { cloneDeep } from 'lodash'
  import MapBase, { Layer, Tooltip, fetchAreaGeoJson } from '@dashboard/components/charts/basic/MapBase'

  // 地图数据
  const geojson = ref()

  onMounted(() => {
    // 加载地图数据
    fetchAreaGeoJson({ level: 'country', code: '100000', granularity: 'province' }).then(res => {
      geojson.value = res
    })
  })

  // 业务数据
  const topList = [
    {
      adcode: '410000',
      value: 999,
    },
  ]
  const otherList = [
    {
      adcode: '370000',
      value: 2000,
    },
    {
      adcode: '150000',
      value: 3000,
    },
    {
      adcode: '460000',
      value: 3800,
    },
    {
      adcode: '530000',
      value: 4400,
    },
  ]

  // 合并地图数据和业务数据
  const combineData = (data: ({ adcode: string; value: number})[]) => {
    const result = cloneDeep({
      type: 'FeatureCollection',
      features: [] as any[],
    })

    if (!geojson.value) return result

    const codeMap = new Map<string, any>(data.map((x: any) => [x.adcode, x]))

    geojson.value.features.forEach((feature: any) => {
      const dataItem = codeMap.get(String(feature.properties.adcode))

      if (dataItem) {
        result.features.push({
          ...feature,
          properties: {
            ...feature.properties,
            ...dataItem,
          },
        })
      }
    })

    return result
  }

  const selectedGeojson = ref({
    type: 'FeatureCollection',
    features: [] as any[],
  })

  // 鼠标选中省份
  const handleDataLayerClick = (target: any) => {
    selectedGeojson.value = {
      type: 'FeatureCollection',
      features: [target.feature],
    }
  }

  // 取消选中省份
  const handleDataLayerUnclick = () => {
    selectedGeojson.value = {
      type: 'FeatureCollection',
      features: [] as any[],
    }
  }
</script>

<template>
  <div :style="{ width: '1080px', height: '720px', 'box-shadow': '0 0 1px 1px #eee' }">
    <MapBase v-if="geojson">
      <!-- 地图底图 -->
      <Layer
        type="polygon"
        :options="{
          name: 'area',
          autoFit: true,
        }"
        :source="geojson"
        :coding="{
          shape: 'fill',
          color: 'rgb(239,243,255)',
          style: {
            opacity: 1,
          },
        }"
      />

      <!-- 省份边界 -->
      <Layer
        type="polygon"
        :options="{
          name: 'border',
        }"
        :source="geojson"
        :coding="{
          shape: 'line',
          color: 'rgb(93,112,146)',
          size: 0.2,
          style: {
            opacity: 1
          },
        }"
      />

      <!-- 数据着色 -->
      <Layer
        type="polygon"
        :options="{
          name: 'data',
        }"
        :source="combineData(otherList)"
        :coding="{
          color: ['value', ['rgb(8,81,156)', 'rgb(189,215,231)']],
          shape: 'fill',
          style: {
            opacity: 1,
          },
          active: {
            color: 'white',
            mix: 0.8,
          },
        }"
        @click="handleDataLayerClick"
        @unclick="handleDataLayerUnclick"
      />

      <!-- 高亮着色 -->
      <Layer
        type="polygon"
        :options="{
          name: 'topn',
        }"
        :source="combineData(topList)"
        :coding="{
          color: ['value', ['red']],
          shape: 'fill',
          style: {
            opacity: 1,
          },
          scale: ['value', {
            type: 'quantize',
          }],
          active: {
            color: 'white',
            mix: 0.6,
          },
        }"
        @click="handleDataLayerClick"
        @unclick="handleDataLayerUnclick"
      />

      <!-- 省份边界:选中态 -->
      <Layer
        type="polygon"
        :options="{
          name: 'border-active',
        }"
        :source="selectedGeojson"
        :coding="{
          shape: 'line',
          color: '#000',
          size: 0.6,
          style: {
            opacity: 1
          },
        }"
      />

      <!-- 文本标签 -->
      <Layer
        type="point"
        :options="{
          name: 'label',
        }"
        :source="combineData([...topList, ...otherList])"
        :coding="{
          color: '#000',
          shape: [['name', 'value'], (name: string, value: number) => `${name} ${value}`],
          size: 10,
          style: {
            opacity: 1,
            stroke: 'rgba(255,255,255,0.5)',
            strokeWidth: 0.5,
            padding: [5, 5],
            textAllowOverlap: false,
          },
        }"
        @click="handleDataLayerClick"
        @unclick="handleDataLayerUnclick"
      />

      <!-- tooltip -->
      <Tooltip
        class="custom-tooltip"
        :options="{
          offsets: [0, 8],
        }"
        :layers="['data', 'topn']"
        :renderer="(target: any) => {
          const {name,value} = target?.feature?.properties
          return (name && value) ? `${name} ${value}` : ''
        }"
      />
    </MapBase>
  </div>
</template>

<style lang="stylus">
.l7-popup.custom-tooltip
  .l7-popup-tip
    border-top-color rgba(50,50,50,0.9)
    border-top-width 6px
    border-left-width 6px
    border-right-width 6px
  .l7-popup-content
    padding 4px 6px
    background-color rgba(50,50,50,0.9)
    color #fff
    font-size: 12px
    line-height: 1.5
</style>
