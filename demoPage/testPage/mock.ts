export const ASYNC_QUERY_PARAMS = {
  type: 'table',
  headers: {
    'first-visit': false,
    'page-load-at': 1706532365157,
    scene: 'analysis',
    'analysis-id': 'template-4886-158683',
  },
  query: { disableCache: true },
  body: {
    datasetId: 1922,
    chartId: '',
    aggregate: true,
    pageOption: {
      needTotalCount: true,
      pagedByAllRowDim: true,
      pagedByFirstRowDim: false,
    },
    queryOption: {
      formatChartDate: true,
      disableCache: false,
      needPivotGroup: false,
    },
    dimensions: [
      {
        pid: 'D70RZ-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_id',
        dataType: 'String',
        alias: '商家ID',
        expr: null,
        fieldIdForStats: 94494,
      },
      {
        pid: 'DEBnt-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_name',
        dataType: 'String',
        alias: '商家名称',
        expr: null,
        fieldIdForStats: 94495,
      },
      {
        pid: 'DcvDg-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_user_id',
        dataType: 'String',
        alias: '商家对应的主账号ID',
        expr: null,
        fieldIdForStats: 94496,
      },
      {
        pid: 'DqA2H-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_industry',
        dataType: 'String',
        alias: '商家主营行业',
        expr: null,
        fieldIdForStats: 94497,
      },
      {
        pid: 'DVA5J-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_first_category_name',
        dataType: 'String',
        alias: '商家主营一级',
        expr: null,
        fieldIdForStats: 94498,
      },
      {
        pid: 'DKIDZ-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_second_category_name',
        dataType: 'String',
        alias: '商家主营二级',
        expr: null,
        fieldIdForStats: 94499,
      },
      {
        pid: 'D8hzN-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_third_category_name',
        dataType: 'String',
        alias: '商家主营三级',
        expr: null,
        fieldIdForStats: 94500,
      },
      {
        pid: 'DlIqV-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'seller_level',
        dataType: 'String',
        alias: '商家月销分层',
        expr: null,
        fieldIdForStats: 94502,
      },
      {
        pid: 'DnnCa-CNT',
        produced: 'Calculated',
        tableId: 2769,
        fieldName: '',
        dataType: 'String',
        alias: '店铺类型',
        expr: "case $$94503 when 0 then '旗舰店' \n                    when 1 then '品牌店' \n                    when 2 then '专卖店' \n                    when 3 then '集合店' \n                    when 4 then '品牌号店' \n                    when 5 then 'MCN' \n                    when 6 then 'KOL' \n                    when 7 then '个人C店' \n                    when 8 then '快闪店' \n                    when 9 then '个人店'\n                    when 10 then '普通企业店'\n                    when 90 then '外部接入虚拟商家'                         \n                    when 101 then '官方旗舰店' \n                    when 102 then '卖场型官方旗舰店' \n                    when 103 then '卖场型旗舰店' \n                    when 401 then '高端集合店'\n                    when 601 then '广告代理商'\n                    when 602 then '影子账号'\n                    when 700 then '福利社供应商'\t  \n                    when 1001 then '个人工商店'  else ''",
        fieldIdForStats: 317796,
      },
      {
        pid: 'D4GK9-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'channel_group',
        dataType: 'String',
        alias: '渠道大类',
        expr: null,
        fieldIdForStats: 94505,
      },
      {
        pid: 'DZEI0-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'channel',
        dataType: 'String',
        alias: '渠道小类',
        expr: null,
        fieldIdForStats: 94506,
      },
      {
        pid: 'DfU6A-CNT',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'traffic_type',
        dataType: 'String',
        alias: '流量类型小类',
        expr: null,
        fieldIdForStats: 94518,
      },
    ],
    measures: [
      {
        pid: 'MlAx5-SUM',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'goods_view_num',
        dataType: 'Whole',
        aggregator: 'SUM',
        alias: '商详次数',
        expr: null,
        fieldIdForStats: 94520,
      },
      {
        pid: 'MyiqQ-SUM',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'add_cart_num',
        dataType: 'Whole',
        aggregator: 'SUM',
        alias: '加购次数',
        expr: null,
        fieldIdForStats: 94521,
      },
      {
        pid: 'MrOtA-SUM',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'instant_buy_num',
        dataType: 'Whole',
        aggregator: 'SUM',
        alias: '立购次数',
        expr: null,
        fieldIdForStats: 94522,
      },
      {
        pid: 'Mm09r-SUM',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'goods_total',
        dataType: 'Whole',
        aggregator: 'SUM',
        alias: '购买件数',
        expr: null,
        fieldIdForStats: 94507,
      },
      {
        pid: 'M8t7p-SUM',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'buy_num',
        dataType: 'Whole',
        aggregator: 'SUM',
        alias: '购买次数',
        expr: null,
        fieldIdForStats: 94509,
      },
      {
        pid: 'MOKQ1-SUM',
        produced: 'Existed',
        tableId: 2769,
        fieldName: 'deal_gmv',
        dataType: 'Decimal',
        aggregator: 'SUM',
        alias: 'DGMV',
        expr: null,
        fieldIdForStats: 94515,
      },
      {
        pid: 'M6aJ3-SUM',
        produced: 'Calculated',
        tableId: 2769,
        fieldName: '',
        dataType: 'Whole',
        alias: '购买用户数',
        expr: 'countd(if$$94509 >0 then $$94455)',
        fieldIdForStats: 136353,
      },
      {
        pid: 'Md5O7-SUM',
        produced: 'Calculated',
        tableId: 2769,
        fieldName: '',
        dataType: 'Decimal',
        alias: '笔单价',
        expr: 'sum($$94515)/sum($$94509)',
        fieldIdForStats: 178345,
      },
      {
        pid: 'MRMSS-SUM',
        produced: 'Calculated',
        tableId: 2769,
        fieldName: '',
        dataType: 'Decimal',
        alias: '客单价',
        expr: 'sum($$94515)/$$136353',
        fieldIdForStats: 178346,
      },
      {
        pid: 'Mst07-SUM',
        produced: 'Calculated',
        tableId: 2769,
        fieldName: '',
        dataType: 'Decimal',
        alias: '购买转化率',
        expr: 'SUM($$94509)/sum($$94520)',
        fieldIdForStats: 312200,
      },
    ],
    orderBy: [
      {
        field: {
          pid: 'MlAx5-SUM',
          produced: 'Existed',
          tableId: 2769,
          fieldName: 'goods_view_num',
          dataType: 'Whole',
          aggregator: 'SUM',
          alias: '商详次数',
          expr: null,
          fieldIdForStats: 94520,
        },
        asc: false,
      },
    ],
    chartConfig: {
      chartId: '',
      chartType: 'PivotDataTable',
      dimensionGroup: {
        rowDimensions: [
          0,
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9,
          10,
          11,
        ],
        columnDimensions: [],
      },
      valueInCols: true,
      customValueOrder: 0,
    },
    advancedOrderBy: [],
    byMoreMeasures: [],
    filter: {
      dimensions: [
        {
          dimension: {
            pid: 'DYSKT-CNT',
            produced: 'Existed',
            tableId: 2769,
            fieldName: 'dtm',
            convert: 'DateParse',
            convertedType: 'Date',
            convertArg: {
              date: 'YYYYMMDD',
              notUseInFilter: true,
            },
            dataType: 'Whole',
            alias: '日期',
            expr: null,
            fieldIdForStats: 94533,
          },
          filter: {
            range: {
              rangeBoundary: {
                minBound: '2023-07-01',
                maxBound: '2023-12-17',
              },
            },
          },
        },
      ],
      measures: [],
    },
    limit: 500,
    offset: 0,
  },
  traceId: '2EOoAFbfMQ-JLtuRYyidu',
  queryId: 'a522a5bbed12e34d1d8ebead1ce40eec7dfce887',
}
