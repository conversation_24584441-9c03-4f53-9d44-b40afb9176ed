<script lang="ts" setup>
  import { ref, onMounted } from 'vue'
  import QueryProvider, { AsyncQueryProvider } from '@attribution/components/QueryProvider'
  import { ASYNC_QUERY_PARAMS } from './mock'

  const params = ref<any>({ id: 1 })

  const fetchData: any = ({ id }: { id: number }) => new Promise((resolve, reject) => {
    setTimeout(() => {
      if (id === 2) reject(new Error('my error'))

      resolve({
        name: Math.random().toString(36).slice(2),
        age: Math.round(Math.random() * 100),
      })
    }, 1000)
  })

  onMounted(() => {
    setTimeout(() => {
      params.value = { id: 2 }
    }, 3000)
  })
</script>

<template>
  <h2>同步查询</h2>

  <div style="width:600px;height:400px;border:1px solid #666">
    <QueryProvider
      :params="params"
      :fetch-data="fetchData"
    >
      <template #default="{ data }">
        <div>
          <div>name: {{ data.name }}</div>
          <div>age: {{ data.age }}</div>
        </div>
      </template>
    </QueryProvider>
  </div>

  <h2>异步查询</h2>

  <div style="width:600px;height:400px;border:1px solid #666">
    <AsyncQueryProvider :params="ASYNC_QUERY_PARAMS as any">
      <template #default="{ data }">
        <pre style="max-height:100%;overflow:auto;margin:0">{{ JSON.stringify(data?.data?.dataList, null, 2) }}</pre>
      </template>
    </AsyncQueryProvider>
  </div>
</template>
