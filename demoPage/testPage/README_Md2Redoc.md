# Markdown 转 Slate 组件使用说明

## 功能概述

这个组件实现了将 Markdown 文本转换为 Slate 数据结构，并使用 `@xhs/docs-editor-api-react-wc` 包进行渲染，同时提供富文本复制功能。

## 主要功能

1. **Markdown 转 Slate**: 使用 `unified` + `remark-parse` + `remark-slate` 将 Markdown 文本转换为 Slate 数据结构
2. **RedocSDK 渲染**: 使用 `@xhs/docs-editor-api-react-wc` 包将转换后的 Slate 数据进行渲染
3. **富文本复制**: 支持复制 Slate 数据和渲染结果的 HTML 内容
4. **示例数据**: 提供丰富的 Markdown 示例内容

## 技术栈

- **Vue 3**: 使用组合式 API
- **Delight 组件库**: UI 组件
- **unified**: 文本处理框架
- **remark-parse**: Markdown 解析器
- **remark-slate**: Markdown 转 Slate 转换器
- **@xhs/docs-editor-api-react-wc**: RedocSDK Web Component
- **copy-to-clipboard**: 复制功能

## 使用方法

### 1. 基本使用

```vue
<template>
  <Md2Redoc />
</template>

<script setup>
import Md2Redoc from './Md2Redoc.vue'
</script>
```

### 2. 转换流程

1. 在 Markdown 输入框中输入或粘贴 Markdown 内容
2. 点击"转换为 Slate"按钮
3. 查看生成的 Slate 数据结构
4. 点击"使用 RedocSDK 渲染"查看渲染结果
5. 使用复制功能获取数据或渲染结果的 HTML 内容

### 3. 支持的 Markdown 语法

- **标题**: `# ## ### #### ##### ######`
- **段落**: 普通文本段落
- **列表**: 有序列表和无序列表
- **代码块**: ``` ``` 包裹的代码
- **内联代码**: `code`
- **链接**: `[text](url)`
- **图片**: `![alt](url)`
- **引用**: `> 引用内容`
- **粗体**: `**text**`
- **斜体**: `*text*`
- **表格**: Markdown 表格语法
- **分割线**: `---`

## API 说明

### 主要函数

#### `convertMarkdown()`
将 Markdown 文本转换为 Slate 数据结构

```javascript
const convertMarkdown = async () => {
  const result = await unified()
    .use(remarkParse)
    .use(remarkSlate)
    .process(markdownInput.value)
  
  slateData.value = result.result
}
```

#### `renderWithRedoc()`
使用 `@xhs/docs-editor-api-react-wc` 渲染 Slate 数据

```javascript
const renderWithRedoc = async () => {
  // 使用 @xhs/docs-editor-api-react-wc 进行渲染
  await renderWithRedocSDK(container, slateData.value)
}
```

#### `renderWithRedocSDK()`
具体的 RedocSDK 渲染实现

```javascript
const renderWithRedocSDK = async (container: HTMLElement, slateNodes: any[]) => {
  // 动态导入 @xhs/docs-editor-api-react-wc
  const { registerReditorWC } = await import('@xhs/docs-editor-api-react-wc')
  
  // 注册 Web Component
  registerReditorWC()
  
  // 创建 red-editor 元素
  const redocElement = document.createElement('red-editor')
  
  // 设置属性
  redocElement.setAttribute('value', JSON.stringify(slateNodes))
  redocElement.setAttribute('readonly', 'true')
  redocElement.setAttribute('box-style', JSON.stringify({
    width: '100%',
    borderRadius: '4px',
    backgroundColor: '#f7f7f7',
    padding: '0px',
  }))
  
  // 添加到容器
  container.appendChild(redocElement)
}
```

#### `copySlateData()`
复制 Slate 数据到剪贴板

```javascript
const copySlateData = () => {
  const dataStr = JSON.stringify(slateData.value, null, 2)
  copy(dataStr)
}
```

#### `copyRenderedContent()`
复制渲染结果的 HTML 内容

```javascript
const copyRenderedContent = () => {
  // 获取 red-editor 内部的 HTML 内容
  const editorContent = redocEditor.value.querySelector('.sdk-editor') || redocEditor.value
  
  if (editorContent) {
    // 复制 HTML 内容
    const htmlContent = editorContent.innerHTML
    copy(htmlContent)
  }
}
```

### 数据结构

#### Slate 节点结构

```javascript
{
  type: 'paragraph' | 'heading' | 'list' | 'listItem' | 'code' | 'blockquote' | 'link' | 'image' | 'table' | 'thematicBreak',
  children: [...], // 子节点数组
  depth?: number, // 标题层级
  ordered?: boolean, // 列表是否有序
  lang?: string, // 代码语言
  url?: string, // 链接或图片 URL
  alt?: string, // 图片 alt 文本
  bold?: boolean, // 粗体
  italic?: boolean, // 斜体
  code?: boolean // 内联代码
}
```

## RedocSDK 集成

### 使用 @xhs/docs-editor-api-react-wc

组件使用 `@xhs/docs-editor-api-react-wc` 包进行渲染，这是一个 Web Component 形式的 RedocSDK。

#### 渲染流程

1. **动态导入**: 使用动态导入加载 `@xhs/docs-editor-api-react-wc`
2. **注册组件**: 调用 `registerReditorWC()` 注册 Web Component
3. **创建元素**: 创建 `red-editor` 元素
4. **设置属性**: 配置编辑器属性
5. **添加到 DOM**: 将元素添加到容器中

#### 属性配置

```javascript
// 基本属性
redocElement.setAttribute('value', JSON.stringify(slateNodes)) // Slate 数据
redocElement.setAttribute('readonly', 'true') // 只读模式

// 样式配置
redocElement.setAttribute('box-style', JSON.stringify({
  width: '100%',
  borderRadius: '4px',
  backgroundColor: '#f7f7f7',
  padding: '0px',
}))
```

### HTML 内容复制

复制功能会获取 `red-editor` 内部的 HTML 内容：

```javascript
// 获取编辑器内容
const editorContent = redocEditor.value.querySelector('.sdk-editor') || redocEditor.value

// 复制 HTML 内容
const htmlContent = editorContent.innerHTML
copy(htmlContent)
```

## 样式定制

组件使用 Stylus 进行样式定制，主要样式类：

- `.md2redoc-container`: 主容器
- `.input-section`: 输入区域
- `.slate-section`: Slate 数据展示区域
- `.render-section`: 渲染结果区域
- `.redoc-content`: RedocSDK 渲染内容样式

### RedocSDK 样式定制

```stylus
.redoc-content
  // RedocSDK 渲染内容的样式
  :deep(red-editor)
    width: 100%
    
  :deep(.sdk-editor)
    padding: 0
    
  :deep(.doc-title)
    display: none
```

## 注意事项

1. **依赖安装**: 确保已安装 `unified`、`remark-parse`、`remark-slate` 等依赖
2. **RedocSDK 依赖**: 确保 `@xhs/docs-editor-api-react-wc` 包已正确安装
3. **类型声明**: 使用 `@ts-ignore` 忽略类型错误，因为这些包可能没有完整的类型定义
4. **错误处理**: 组件包含完整的错误处理和用户提示
5. **动态导入**: 使用动态导入避免打包时的问题

## 扩展功能

### 1. 添加更多 Markdown 语法支持

可以通过添加更多的 remark 插件来支持更多 Markdown 语法：

```javascript
import remarkGfm from 'remark-gfm' // GitHub Flavored Markdown
import remarkMath from 'remark-math' // 数学公式

const result = await unified()
  .use(remarkParse)
  .use(remarkGfm)
  .use(remarkMath)
  .use(remarkSlate)
  .process(markdownInput.value)
```

### 2. 自定义 RedocSDK 配置

可以扩展 RedocSDK 的配置选项：

```javascript
// 更多配置选项
redocElement.setAttribute('box-style', JSON.stringify({
  width: '100%',
  height: '500px',
  borderRadius: '8px',
  backgroundColor: '#ffffff',
  padding: '16px',
  border: '1px solid #e8e8e8',
}))
```

### 3. 集成其他编辑器

可以集成其他富文本编辑器，如 Slate.js、Quill 等：

```javascript
import { createEditor } from 'slate'
import { Slate, Editable } from 'slate-vue3'

// 使用 Slate.js 编辑器
const editor = createEditor()
```

## 故障排除

### 常见问题

1. **转换失败**: 检查 Markdown 语法是否正确
2. **渲染异常**: 确认 `@xhs/docs-editor-api-react-wc` 是否正确加载
3. **复制失败**: 检查浏览器是否支持 Clipboard API
4. **Web Component 未注册**: 确保 `registerReditorWC()` 被正确调用

### 调试技巧

1. 查看控制台输出的 Slate 数据结构
2. 使用浏览器开发者工具检查 `red-editor` 元素
3. 检查网络请求确认依赖包是否正确加载
4. 查看 RedocSDK 的渲染结果

## 更新日志

- **v1.0.0**: 初始版本，支持基本的 Markdown 转 Slate 功能
- **v1.1.0**: 添加 RedocSDK 集成示例
- **v1.2.0**: 优化错误处理和用户体验
- **v2.0.0**: 使用 `@xhs/docs-editor-api-react-wc` 进行渲染，支持 HTML 内容复制 