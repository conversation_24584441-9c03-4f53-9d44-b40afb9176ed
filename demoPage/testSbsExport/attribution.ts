import { requestAttributionSchema } from '@attribution/services'
import { AttributionInfo } from '@attribution/types/schema'
import { getAttributionExportQuery } from '@xhs/redbi-share-dsl'

export const getAttributionExportDSL = (attributionId: number): void => {
  requestAttributionSchema({ attributionId }).then((attributionInfo: AttributionInfo) => {
    const exportDsl = getAttributionExportQuery(attributionInfo, {
      fileName: 'xxx',
      format: 'XLSX',
    })
    return exportDsl
  })
}
