import { getDataSetBasicInfo, getDatasetFieldListV2 } from '@xhs/redbi-share-services'
import { getSettingByAnalysis } from '@analysis/services/basicInfo'
import { getAnalysisExportQuery, getCustomFieldIds } from '@xhs/redbi-share-dsl'
import { getDashboardDetail } from '@dashboard/services/basicInfo'
import handleDashBoardPreResolve from '@dashboard/share/handleDashBoardPreResolve'

export const getAnalysisExportDSL = async (analysisId: string, dashboardId?: string, chartId?: string): Promise<void> => {
  let analysisInfo
  let datasetInfo
  let fieldInfo
  let dashboardInfo: any
  await getSettingByAnalysis(analysisId).then(analysisRes => {
    analysisInfo = analysisRes
    const selectedDatasetId = analysisRes?.datasetInfo?.selectedDatasetId
    const fieldIds = getCustomFieldIds(analysisRes)
    return Promise.all([
      getDatasetFieldListV2({
        analysisId,
        datasetId: selectedDatasetId,
        fieldIds,
      }).then(fieldRes => {
        fieldInfo = fieldRes
      }),
      getDataSetBasicInfo(selectedDatasetId).then(datasetRes => {
        datasetInfo = datasetRes
      }),
    ])
  })

  if (dashboardId && chartId) {
    dashboardInfo = handleDashBoardPreResolve(await getDashboardDetail(dashboardId))
  }

  const exportRes = getAnalysisExportQuery((analysisInfo as any), (datasetInfo as any), (fieldInfo as any), {
    fileName: '',
    format: 'XLSX',
    limit: 2000,
  }, {
    dashboardInfo,
    chartId,
  })
  return exportRes
}
