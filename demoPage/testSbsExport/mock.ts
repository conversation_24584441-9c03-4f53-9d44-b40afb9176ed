export const params = {
  dashboardInfo: {
    assetAssuranceInfo: { currentLevel: 'NO' },
    components: [
      {
        created: 1678360976289,
        dashboardId: 32,
        id: 'container_rqiOflc0AD',
        role: 'container',
        setting: {
          data: { title: '未命名1' },
          style: { tabStyle: 'underline', tabPosition: 'left' },
        },
        slots: [
          {
            title: '未命名1',
            description: '',
            id: 'slot_m3KY1AEmL9',
            index: 0,
            visible: true,
            grid: 32,
            padding: [8, 8],
            margin: [12, 12],
          },
          {
            id: 'slot_gIndpMcNSP', title: '趋势分析表', description: '', visible: true, index: 1,
          },
        ],
        title: '未命名1',
        type: 'TabContainer',
      },
      {
        created: 1678360976292,
        dashboardId: 32,
        id: 'chart_2hcX412msz',
        role: 'chart',
        setting: {
          data: {
            datasetId: 12,
            rows: [
              {
                fieldId: 266,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                comment: '时间',
                dataType: 'Whole',
                partition: true,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                originRole: 'Dimension',
              },
            ],
            columns: [
              {
                fieldId: 211,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'act_id',
                alias: 'act_id',
                comment: '活动id',
                dataType: 'String',
                partition: false,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                originRole: 'Dimension',
                axis: 'y0',
                axisIndex: 0,
                aggregator: 'CNT',
              },
            ],
            colors: [],
            filters: [
              {
                fieldId: 266,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                comment: '时间',
                dataType: 'Whole',
                partition: true,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                filterConfig: {
                  formType: 'condition',
                  query: { range: { rangeBoundary: { minBound: 20230108, maxBound: 20230308 } } },
                },
                originRole: 'Dimension',
              },
            ],
            orders: [],
          },
          style: {
            common: {
              title: { enable: true, text: '未命名图表_Fi6YNJTSyR', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              legend: { enable: true, position: 'top-right' },
              marker: { enable: false },
              tooltip: { enable: true },
              label: { enable: false },
              spline: false,
            },
            axis: {
              x0: {
                title: { enable: true, text: '' },
                slider: { enable: true, visibility: 'auto' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y0: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y1: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
            },
          },
        },
        slots: [],
        title: '未命名图表_Fi6YNJTSyR',
        type: 'LineChart',
      },
      {
        created: 1682304833755,
        dashboardId: 32,
        id: 'chart_t72zkLCL8g',
        role: 'chart',
        setting: {
          data: {
            datasetId: 12,
            rows: [
              {
                fieldId: 6426,
                produced: 'Calculated',
                role: 'Dimension',
                fieldName: '',
                alias: '时间分区dtm',
                comment: '',
                dataType: 'Date',
                partition: false,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                expr: 'DATEPARSE("yyyyMMdd", Str($$266))',
                key: 'jyQrnRwVFP',
                originRole: 'Dimension',
                mapFunction: 'YEAR-MONTH-DAY',
              },
            ],
            columns: [
              {
                fieldId: 251,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'seller_id',
                alias: 'seller_id',
                comment: '卖家id',
                dataType: 'String',
                partition: false,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                key: 'seller_idO8xn7kb2TV',
                originRole: 'Dimension',
                aggregator: 'CNT',
              },
            ],
            colors: [],
            filters: [],
            orders: [],
          },
          style: {
            common: {
              title: { enable: true, text: '指标卡_1', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              relationship: 'affiliated',
              indicator: {
                blockStyle: 'minimalism',
                displayType: 'newline',
                rowMax: 4,
                align: 'left',
                showMainName: true,
                main: {
                  nameSize: 12,
                  nameColor: [0, 0, 0, 1],
                  valueSize: 20,
                  valueColor: [0, 0, 0, 1],
                  layout: 'upDown',
                  alignment: 'center',
                },
                minor: {
                  nameSize: 12,
                  nameColor: [0, 0, 0, 1],
                  valueSize: 12,
                  valueColor: [0, 0, 0, 1],
                },
              },
              showDimension: true,
              dimensionSize: 16,
              dimension: { alignment: 'center' },
            },
            format: { list: [] },
          },
          version: '',
          tableConfig: '',
          advanced: { analysis: { yom: { enable: false, list: [] } } },
          formatType: ['mark'],
        },
        slots: [],
        title: '指标卡_1',
        type: 'StatisticCard',
      },
      {
        created: 1682305186966,
        dashboardId: 32,
        id: 'chart_g2TUBAxQHy',
        role: 'chart',
        setting: {
          data: {
            rows: [
              {
                fieldId: 266,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                comment: '时间',
                dataType: 'Whole',
                partition: true,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                key: 'dtmzkG4748Ec0',
                originRole: 'Dimension',
              },
            ],
            columns: [
              {
                fieldId: 210,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'coupon_id',
                alias: 'coupon_id',
                comment: '券id',
                dataType: 'String',
                partition: false,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                key: 'coupon_idjSDlce2OSl',
                originRole: 'Dimension',
                axis: 'y0',
                axisIndex: 0,
                aggregator: 'CNT',
              },
            ],
            colors: [],
            filters: [],
            orders: [],
            datasetId: 12,
          },
          version: '',
          style: {
            common: {
              title: { enable: true, text: '线柱组合图_1', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              legend: { enable: true, position: 'top-right' },
              marker: { enable: false },
              tooltip: { enable: true },
              label: { enable: false },
              spline: false,
              holiday: { enable: false, weekend: false, holiday: false },
            },
            axis: {
              x0: {
                title: { enable: true, text: '' },
                slider: { enable: false, visibility: 'auto' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y0: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y1: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
            },
          },
          advanced: {
            analysis: {
              guideLine: {
                enable: true,
                list: [{ type: 'AVG', measure: 'coupon_idjSDlce2OSl', percentile: '0.5' }],
              },
            },
          },
        },
        slots: [],
        title: '线柱组合图_1',
        type: 'LineColumnChart',
      },
      {
        created: 1682308417375,
        dashboardId: 32,
        id: 'chart_5cm6aAVzTe',
        role: 'chart',
        setting: {
          data: {
            datasetId: 12,
            rows: [
              {
                fieldId: 6426,
                produced: 'Calculated',
                role: 'Dimension',
                fieldName: '',
                alias: '时间分区dtm',
                comment: '',
                dataType: 'Date',
                partition: false,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                expr: 'DATEPARSE("yyyyMMdd", Str($$266))',
                key: 'vCYajVV7gh',
                originRole: 'Dimension',
                mapFunction: 'YEAR-MONTH-DAY',
                orderConfig: { asc: true },
              },
            ],
            columns: [
              {
                fieldId: 256,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'op_seller_main_second_category_name',
                alias: 'op_seller_main_second_category_name',
                comment:
                    '运营管理商家主营二级类目$$eg:面部彩妆 ，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
                dataType: 'String',
                partition: false,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                key: 'op_seller_main_second_category_nameaRQsXQ7zIW',
                originRole: 'Dimension',
                axis: 'y0',
                axisIndex: 0,
                aggregator: 'CNTD',
              },
              {
                fieldId: 216,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'coupon_thr',
                alias: 'coupon_thr',
                comment: '券门槛',
                dataType: 'String',
                partition: false,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                key: 'coupon_thr4qseiWRJW4',
                originRole: 'Dimension',
                axis: 'y1',
                axisIndex: 0,
                aggregator: 'CNT',
              },
              {
                fieldId: 255,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'op_seller_main_first_category_name',
                alias: 'op_seller_main_first_category_name',
                comment:
                    '运营管理商家主营一级类目$$eg:面部彩妆 ，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
                dataType: 'String',
                partition: false,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                key: 'op_seller_main_first_category_nameDxQ7tKxWej',
                originRole: 'Dimension',
                axis: 'y1',
                axisIndex: 1,
                aggregator: 'CNT',
              },
              {
                fieldId: 257,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'discount',
                alias: 'discount',
                comment: '券满减',
                dataType: 'Whole',
                partition: false,
                tableId: 12,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                key: 'discountXVHcQ5quaA',
                originRole: 'Measure',
                axis: 'y0',
                axisIndex: 3,
                formatter: {
                  type: 'normal',
                  config: { thousandth: true, percentage: false, digits: 0 },
                },
                aggregator: 'SUM',
              },
            ],
            colors: [
              {
                fieldId: 6426,
                produced: 'Calculated',
                role: 'Dimension',
                fieldName: '',
                alias: '时间分区dtm',
                comment: '',
                dataType: 'Date',
                partition: false,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                expr: 'DATEPARSE("yyyyMMdd", Str($$266))',
                key: 'Kp9brNMHAM',
                originRole: 'Dimension',
                mapFunction: 'YEAR-MONTH-DAY',
              },
            ],
            filters: [
              {
                fieldId: 6426,
                produced: 'Calculated',
                role: 'Dimension',
                fieldName: '',
                alias: '时间分区dtm',
                comment: '',
                dataType: 'Date',
                partition: false,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                expr: 'DATEPARSE("yyyyMMdd", Str($$266))',
                key: 'aLER49SRiP',
                filterConfig: {
                  formType: 'dynamic',
                  query: { range: { enumBoundary: { boundaryValue: 'last30Days' } } },
                },
                originRole: 'Dimension',
                mapFunction: 'YEAR-MONTH-DAY',
              },
            ],
            orders: ['vCYajVV7gh'],
          },
          version: 'version_z6jIRqTEfH',
          style: {
            common: {
              title: { enable: true, text: '堆叠组合图_1堆叠组合图_1堆叠组合图_', fontSize: 20 },
              description: {
                enable: true,
                text: '堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠组合图_1堆叠',
              },
            },
            design: {
              legend: { enable: true, position: 'bottom-left' },
              marker: { enable: false },
              tooltip: { enable: true },
              label: { enable: false },
              spline: true,
              holiday: { enable: true, weekend: true, holiday: false },
            },
            axis: {
              x0: {
                title: { enable: true, text: '' },
                slider: { enable: false, visibility: 'auto' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y0: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y1: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
            },
          },
          advanced: { analysis: { guideLine: { enable: true, list: [] } } },
        },
        slots: [],
        title: '堆叠组合图_1堆叠组合图_1堆叠组合图_',
        type: 'StackChart',
      },
      {
        created: 1682308417379,
        dashboardId: 32,
        id: 'chart_w0PotAjGEl',
        role: 'chart',
        setting: {
          data: {
            rows: [], columns: [], colors: [], filters: [], orders: [], datasetId: '',
          },
          version: '',
          style: {
            common: {
              title: { enable: true, text: '堆叠组合图_1', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              legend: { enable: true, position: 'top-right' },
              marker: { enable: false },
              tooltip: { enable: true },
              label: { enable: false },
              spline: false,
              holiday: { enable: false, weekend: false, holiday: false },
            },
            axis: {
              x0: {
                title: { enable: true, text: '' },
                slider: { enable: false, visibility: 'auto' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y0: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y1: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
            },
          },
          advanced: { analysis: { guideLine: { enable: true, list: [] } } },
        },
        slots: [],
        title: '堆叠组合图_1',
        type: 'StackChart',
      },
      {
        created: 1682308417382,
        dashboardId: 32,
        id: 'container_oK3G0PbUrI',
        role: 'container',
        setting: {
          data: { title: '未命名1' },
          style: { tabStyle: 'underline', tabPosition: 'left' },
        },
        slots: [
          {
            title: '未命名1',
            id: 'slot_pflw7pYejz',
            visible: true,
            grid: 32,
            padding: [8, 8],
            margin: [12, 12],
          },
        ],
        title: '未命名1',
        type: 'TabContainer',
      },
      {
        created: 1682308417385,
        dashboardId: 32,
        id: 'chart_1L3qCJqpz8',
        role: 'chart',
        setting: {
          data: {
            rows: [], columns: [], colors: [], filters: [], orders: [], datasetId: '',
          },
          version: '',
          style: {
            common: {
              title: { enable: true, text: '线柱组合图_1', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              legend: { enable: true, position: 'top-right' },
              marker: { enable: false },
              tooltip: { enable: true },
              label: { enable: false },
              spline: false,
              holiday: { enable: false, weekend: false, holiday: false },
            },
            axis: {
              x0: {
                title: { enable: true, text: '' },
                slider: { enable: false, visibility: 'auto' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y0: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y1: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
            },
          },
          advanced: { analysis: { guideLine: { enable: true, list: [] } } },
        },
        slots: [],
        title: '线柱组合图_1',
        type: 'LineColumnChart',
      },
      {
        created: 1683358467861,
        dashboardId: 32,
        id: 'chart_VBhBACPdgg',
        role: 'chart',
        setting: {
          data: {
            rows: [
              {
                fieldId: 8184,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                dataType: 'String',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                convert: 'DateParse',
                convertedType: 'Date',
                state: 0,
                hidden: false,
                realDataType: 'Date',
                key: 'dtm0PNBkz3bs0',
                originRole: 'Dimension',
                mapFunction: 'YEAR-MONTH-DAY',
                orderConfig: { type: 'normal', config: { asc: true } },
              },
            ],
            columns: [
              {
                fieldId: 8147,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'cost',
                alias: 'cost',
                dataType: 'Decimal',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                realDataType: 'Decimal',
                key: 'costvUsgS1VxQC',
                originRole: 'Measure',
                axis: 'y0',
                axisIndex: 0,
                formatter: {
                  type: 'normal',
                  config: { thousandth: true, percentage: false, digits: 0 },
                },
                aggregator: 'SUM',
              },
            ],
            colors: [],
            filters: [],
            orders: ['dtm0PNBkz3bs0'],
            datasetId: 164,
          },
          version: 'version_manual_Ev82sIx1xw',
          style: {
            common: {
              title: { enable: true, text: '定价广告日周月报表', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              legend: { enable: true, position: 'top-right' },
              marker: { enable: false },
              tooltip: { enable: true },
              label: { enable: false },
              spline: false,
              holiday: { enable: false, weekend: false, holiday: false },
            },
            axis: {
              x0: {
                title: { enable: true, text: '' },
                slider: { enable: false, visibility: 'auto' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y0: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y1: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
            },
          },
          advanced: { analysis: { guideLine: { enable: false, list: [] } } },
        },
        slots: [],
        title: '定价广告日周月报表',
        type: 'LineChart',
      },
      {
        created: 1683358571530,
        dashboardId: 32,
        id: 'chart_J0JoglBYdY',
        role: 'chart',
        setting: {
          data: {
            datasetId: 164,
            rows: [
              {
                fieldId: 8145,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'date_key',
                alias: 'date_key',
                dataType: 'String',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                realDataType: 'String',
                key: 'date_keyRlvRL5H7lX',
                originRole: 'Dimension',
                dimensionRole: 'row',
              },
              {
                fieldId: 8184,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                dataType: 'String',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 1,
                hidden: false,
                realDataType: 'String',
                key: 'dtmIfRQxIwouB',
                originRole: 'Dimension',
                dimensionRole: 'row',
              },
              {
                role: 'Dimension',
                alias: '指标名',
                isVirtualPill: true,
                dimensionRole: 'column',
                key: 'columns-reference',
              },
            ],
            columns: [
              {
                fieldId: 8147,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'cost',
                alias: 'cost',
                dataType: 'Decimal',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                realDataType: 'Decimal',
                key: 'costDPYZ57Dfcy',
                originRole: 'Measure',
                formatter: {
                  type: 'normal',
                  config: { thousandth: true, percentage: false, digits: 0 },
                },
                aggregator: 'SUM',
              },
            ],
            colors: [],
            filters: [],
            orders: [],
            limit: 20,
            offset: 0,
          },
          style: {
            common: {
              title: { enable: true, text: '透视表_1', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              tableStyle: 'zebraLine',
              columnWidth: 'auto',
              displayMode: 'classic',
              combine: { enable: true },
              lineLabelName: '',
              expand: 2,
              header: { size: 12, color: [0, 0, 0, 1], align: 'center' },
              freeze: { enable: true },
              lineNumber: { enable: true },
              pagination: { enable: true, pageSize: 20 },
            },
            format: { list: [] },
          },
          version: 'version_manual_jHzqfiIijh',
          tableConfig: {
            treeData: [],
            meta: [
              {
                fieldId: 8147,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'cost',
                alias: 'cost',
                dataType: 'Decimal',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                realDataType: 'Decimal',
                key: 'costDPYZ57Dfcy',
                originRole: 'Measure',
                formatter: {
                  type: 'normal',
                  config: { thousandth: true, percentage: false, digits: 0 },
                },
                aggregator: 'SUM',
                showTitle: 'cost',
                title: 'cost',
                isLeaf: true,
                pid: 'M8147-SUM',
                type: 'measure',
                isDefaultShow: true,
                fieldType: 'measure',
                defaultExpanded: false,
                collapsed: true,
                checked: false,
              },
              {
                fieldId: 8145,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'date_key',
                alias: 'date_key',
                dataType: 'String',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 0,
                hidden: false,
                realDataType: 'String',
                key: 'date_keyRlvRL5H7lX',
                originRole: 'Dimension',
                dimensionRole: 'row',
                showTitle: 'date_key',
                title: 'date_key',
                isLeaf: true,
                pid: 'D8145-DEFAULT',
                type: 'dimension',
                isDefaultShow: true,
                fieldType: 'dimension',
                defaultExpanded: false,
                collapsed: true,
                checked: false,
              },
              {
                fieldId: 8184,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                dataType: 'String',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 1,
                hidden: false,
                realDataType: 'String',
                key: 'dtmIfRQxIwouB',
                originRole: 'Dimension',
                dimensionRole: 'row',
                showTitle: 'dtm',
                title: 'dtm',
                isLeaf: true,
                pid: 'D8184-DEFAULT',
                type: 'dimension',
                isDefaultShow: true,
                fieldType: 'dimension',
                defaultExpanded: false,
                collapsed: true,
                checked: false,
              },
              {
                role: 'Dimension',
                alias: '指标名',
                isVirtualPill: true,
                dimensionRole: 'column',
                key: 'columns-reference',
                showTitle: '指标名',
                title: '指标名',
                isLeaf: true,
                pid: 'Dundefined-DEFAULT',
                type: 'dimension',
                isDefaultShow: true,
                fieldType: 'dimension',
                defaultExpanded: false,
                collapsed: true,
                checked: false,
              },
            ],
            isDimout: true,
          },
          advanced: {
            analysis: {
              yom: { enable: false, position: 'bottom', list: [] },
              dateAcc: { enable: false, list: [] },
              topN: {
                enable: true,
                computeType: 'result',
                dimSort: {
                  sortDir: 'asc',
                  dimension: 'date_keyRlvRL5H7lX',
                  sortBy: 'costDPYZ57Dfcy',
                },
                resultSort: [{ sortDir: 'asc' }],
                numN: 3,
                limitType: 'limit',
                topType: 'by_dimensions',
              },
              percentage: {
                enable: false, measures: [], perTypes: [], position: 'right',
              },
            },
          },
        },
        slots: [],
        title: '透视表_1',
        type: 'PivotDataTable',
      },
      {
        created: 1683360166544,
        dashboardId: 32,
        id: 'chart_o2RNj4wmCu',
        role: 'chart',
        setting: {
          data: {
            datasetId: 164,
            rows: [
              {
                fieldId: 8184,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                dataType: 'String',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                state: 1,
                hidden: false,
                realDataType: 'String',
                key: 'dtmEDWekT96HM',
                originRole: 'Dimension',
                orderConfig: { type: 'normal', config: { asc: true } },
              },
            ],
            columns: [
              {
                fieldId: 8144,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'grain',
                alias: 'grain',
                dataType: 'Whole',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                realDataType: 'Whole',
                key: 'grain0FaRPqBjqw',
                originRole: 'Measure',
                axis: 'y0',
                axisIndex: 0,
                formatter: {
                  type: 'normal',
                  config: { thousandth: true, percentage: false, digits: 0 },
                },
                aggregator: 'SUM',
              },
            ],
            colors: [],
            filters: [],
            orders: ['dtmEDWekT96HM'],
          },
          version: 'version_manual_e9BU5xZypJ',
          style: {
            common: {
              title: { enable: true, text: '折线图_2', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              legend: { enable: true, position: 'top-right' },
              marker: { enable: false },
              tooltip: { enable: true },
              label: { enable: false },
              spline: false,
              holiday: { enable: false, weekend: false, holiday: false },
            },
            axis: {
              x0: {
                title: { enable: true, text: '' },
                slider: { enable: false, visibility: 'auto' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y0: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
              y1: {
                title: { enable: true, text: '' },
                range: { enable: false, min: '', max: '' },
                line: { enable: true },
                tick: { enable: false },
                label: { enable: true, posture: 'auto' },
                grid: { enable: false },
              },
            },
          },
          advanced: { analysis: { guideLine: { enable: false, list: [] } } },
        },
        slots: [],
        title: '折线图_2',
        type: 'LineChart',
      },
      {
        created: 1683360221004,
        dashboardId: 32,
        id: 'chart_MLujoJWqWI',
        role: 'chart',
        setting: {
          data: {
            rows: [
              {
                fieldId: 8184,
                produced: 'Existed',
                role: 'Dimension',
                fieldName: 'dtm',
                alias: 'dtm',
                dataType: 'String',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'CNT',
                convert: 'DateParse',
                convertedType: 'DateTime',
                convertArg: { date: 'YYYY-MM-DD', time: 'HH:MI:SS', notUseInFilter: true },
                state: 0,
                hidden: false,
                dateFlag: false,
                realDataType: 'DateTime',
                key: 'dtmxgD1bMxrwu',
                plainField: {
                  fieldId: 8184,
                  produced: 'Existed',
                  role: 'Dimension',
                  fieldName: 'dtm',
                  alias: 'dtm',
                  dataType: 'String',
                  partition: false,
                  tableId: 185,
                  granularity: 'g0',
                  defaultAggregator: 'CNT',
                  convert: 'DateParse',
                  convertedType: 'DateTime',
                  convertArg: { date: 'YYYY-MM-DD', time: 'HH:MI:SS', notUseInFilter: true },
                  state: 0,
                  hidden: false,
                  dateFlag: false,
                  realDataType: 'DateTime',
                },
                originRole: 'Dimension',
                mapFunction: 'YEAR-MONTH-DAY',
                isDateRow: true,
                orderConfig: { type: 'normal', config: { asc: true } },
              },
            ],
            columns: [
              {
                fieldId: 8144,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'grain',
                alias: 'grain',
                dataType: 'Whole',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                dateFlag: false,
                realDataType: 'Whole',
                key: 'grainWyKJGRSoid',
                plainField: {
                  fieldId: 8144,
                  produced: 'Existed',
                  role: 'Measure',
                  fieldName: 'grain',
                  alias: 'grain',
                  dataType: 'Whole',
                  partition: false,
                  tableId: 185,
                  granularity: 'g0',
                  defaultAggregator: 'SUM',
                  state: 0,
                  hidden: false,
                  dateFlag: false,
                  realDataType: 'Whole',
                },
                originRole: 'Measure',
                formatter: {
                  type: 'normal',
                  config: {
                    thousandth: true,
                    percentage: false,
                    digits: 0,
                    scale: 'auto',
                    suffix: '',
                  },
                },
                aggregator: 'SUM',
              },
            ],
            filters: [],
            orders: ['dtmxgD1bMxrwu'],
            datasetId: 164,
            colors: [],
          },
          style: {
            common: {
              title: { enable: true, text: '趋势分析表_1', fontSize: 14 },
              description: { enable: false, text: '' },
            },
            design: {
              tableStyle: 'zebraLine',
              firstColumnWidth: 'auto',
              avgSumSwitch: { enable: false, value: 'sum' },
            },
            period: {
              defaultShowPeriod: 'month',
              year: { enable: true, offset: 2 },
              quarter: { enable: true, offset: 4 },
              month: { enable: true, offset: 6 },
              week: { enable: true, offset: 4, begin: 'Monday' },
              day: { enable: true, offset: 7 },
              custom: { enable: false, periodList: [] },
            },
          },
          version: 'version_manual_VBI4Eunpyw',
          tableConfig: {
            treeData: [
              {
                fieldId: 8144,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'grain',
                alias: 'grain',
                dataType: 'Whole',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                dateFlag: false,
                realDataType: 'Whole',
                key: 'grainWyKJGRSoid',
                plainField: {
                  fieldId: 8144,
                  produced: 'Existed',
                  role: 'Measure',
                  fieldName: 'grain',
                  alias: 'grain',
                  dataType: 'Whole',
                  partition: false,
                  tableId: 185,
                  granularity: 'g0',
                  defaultAggregator: 'SUM',
                  state: 0,
                  hidden: false,
                  dateFlag: false,
                  realDataType: 'Whole',
                },
                originRole: 'Measure',
                formatter: {
                  type: 'normal',
                  config: {
                    thousandth: true,
                    percentage: false,
                    digits: 0,
                    scale: 'auto',
                    suffix: '',
                  },
                },
                aggregator: 'SUM',
                showTitle: 'grain',
                title: 'grain',
                isLeaf: true,
                pid: 'M8144-SUM',
                type: 1,
                isDefaultShow: true,
                level: 0,
                fieldType: '',
                defaultExpanded: false,
                collapsed: true,
                checked: false,
              },
            ],
            meta: [
              {
                fieldId: 8144,
                produced: 'Existed',
                role: 'Measure',
                fieldName: 'grain',
                alias: 'grain',
                dataType: 'Whole',
                partition: false,
                tableId: 185,
                granularity: 'g0',
                defaultAggregator: 'SUM',
                state: 0,
                hidden: false,
                dateFlag: false,
                realDataType: 'Whole',
                key: 'grainWyKJGRSoid',
                plainField: {
                  fieldId: 8144,
                  produced: 'Existed',
                  role: 'Measure',
                  fieldName: 'grain',
                  alias: 'grain',
                  dataType: 'Whole',
                  partition: false,
                  tableId: 185,
                  granularity: 'g0',
                  defaultAggregator: 'SUM',
                  state: 0,
                  hidden: false,
                  dateFlag: false,
                  realDataType: 'Whole',
                },
                originRole: 'Measure',
                formatter: {
                  type: 'normal',
                  config: {
                    thousandth: true,
                    percentage: false,
                    digits: 0,
                    scale: 'auto',
                    suffix: '',
                  },
                },
                aggregator: 'SUM',
                showTitle: 'grain',
                title: 'grain',
                isLeaf: true,
                pid: 'M8144-SUM',
                type: 1,
                isDefaultShow: true,
                level: 0,
                fieldType: '',
                defaultExpanded: false,
                collapsed: true,
                checked: false,
              },
            ],
            isDimOut: true,
          },
          advanced: {
            filter: {
              deadline: { type: 'dynamic', offset: 1, date: '2023-05-06' },
              dimensions: { enable: false, list: [] },
            },
            analysis: {
              yom: {
                enable: true,
                list: [{ valueTypes: ['growthRate'], measures: ['grainWyKJGRSoid'] }],
                contrastType: 'unify',
                toggle: 'mom',
                unify: ['mom'],
                custom: {
                  year: ['mom'],
                  quarter: ['mom'],
                  month: ['mom'],
                  week: ['mom'],
                  day: ['mom'],
                },
                renderStyle: 'green',
              },
            },
            trendModal: {
              day: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: true },
                  mom: { enable: true, on: false },
                  yoyw: { enable: true, on: false },
                  yoym: { enable: true, on: false },
                  yoy: { enable: true, on: false },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
              week: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: true },
                  mom: { enable: true, on: false },
                  yoy: { enable: true, on: false },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
              month: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: true },
                  mom: { enable: true, on: false },
                  yoy: { enable: true, on: false },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
              bimonth: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: true },
                  mom: { enable: true, on: false },
                  yoy: { enable: true, on: false },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
            },
          },
        },
        slots: [],
        title: '趋势分析表_1',
        type: 'TrendAnalysisTable',
      },
      {
        created: 1685517764880,
        dashboardId: 32,
        id: 'chart_oQS7YYmTMJ',
        role: 'chart',
        setting: {
          data: {
            rows: [], columns: [], filters: [], orders: [], datasetId: 164, colors: [],
          },
          style: {
            common: {
              title: { enable: true, text: '趋势分析表_1', fontSize: 12 },
              description: { enable: false, text: '' },
            },
            design: {
              tableStyle: 'zebraLine',
              firstColumnWidth: 'auto',
              avgSumSwitch: { enable: false, value: 'sum' },
            },
            period: {
              defaultShowPeriod: 'month',
              year: { enable: true, offset: 2 },
              quarter: { enable: true, offset: 4 },
              month: { enable: true, offset: 6 },
              week: { enable: true, offset: 4, begin: 'Monday' },
              day: { enable: true, offset: 7 },
              custom: { enable: false, periodList: [] },
            },
          },
          version: '',
          tableConfig: '',
          advanced: {
            filter: {
              deadline: { type: 'dynamic', offset: 1, date: '2023-05-31' },
              dimensions: { enable: false, list: [] },
            },
            analysis: {
              yom: {
                enable: true,
                list: [{ measures: [], valueTypes: { growthRate: { enable: true } } }],
                contrastType: 'unify',
                toggle: 'mom',
                unify: ['mom'],
                custom: {
                  year: ['mom'],
                  quarter: ['mom'],
                  month: ['mom'],
                  week: ['mom'],
                  day: ['mom'],
                },
                renderStyle: 'green',
              },
            },
            trendModal: {
              day: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: false },
                  mom: { enable: true, on: false },
                  yoyw: { enable: true, on: false },
                  yoym: { enable: true, on: false },
                  yoy: { enable: true, on: true },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
              week: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: false },
                  mom: { enable: true, on: false },
                  yoy: { enable: true, on: true },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
              month: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: false },
                  mom: { enable: true, on: false },
                  yoy: { enable: true, on: true },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
              bimonth: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: false },
                  mom: { enable: true, on: false },
                  yoy: { enable: true, on: true },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
              quarter: {
                enable: true,
                show: false,
                contrastType: {
                  custom: { enable: true, on: false },
                  mom: { enable: true, on: false },
                  yoy: { enable: true, on: true },
                },
                contrastContent: {
                  growthRate: { enable: true, on: false },
                  compareValue: { enable: true, on: true },
                },
              },
            },
          },
        },
        slots: [],
        title: '趋势分析表_1',
        type: 'TrendAnalysisTable',
      },
    ],
    created: 1678360802034,
    dashboardFavorite: false,
    dashboardNode: {
      dashboardId: 32,
      hasDeveloperPerm: true,
      hasManagerPerm: true,
      hasOwnerPerm: true,
      hasParentDeveloperPerm: true,
      hasParentManagerPerm: true,
      hasParentOwnerPerm: true,
      hasParentReviewerPerm: true,
      hasReviewerPerm: true,
      isParentSubProject: false,
      isSubProject: false,
      nodeId: 64,
      nodeName: '韩立测试看板',
      nodeType: 2,
      orderNum: 4000,
      owner: {
        departmentName: '数据应用研发组',
        departmentNames: ['中台技术部', '数据平台部', '数据应用研发组'],
        displayName: '韩立(陈苏洲)',
        redEmail: '<EMAIL>',
        userAvatar: 'https://wework.qpic.cn/wwpic3az/571775_9WkTLnxkQYi0KBQ_1703736262/0',
        userEmail: '<EMAIL>',
        userType: 1,
      },
      parentNodeId: 63,
      projectId: 3,
      remarks: '',
      sourceType: 1,
      subProjectId: 0,
    },
    datasets: [
      {
        assetAssuranceInfo: { currentLevel: 'A2' },
        characteristic: {
          discrepancies: {
            230: 6,
            231: 3941,
            232: 3935,
            233: 3960,
            234: 3950,
            235: 10,
            236: 16,
            237: 3867,
            238: 3868,
            239: 9,
            240: 628,
            241: 898,
            242: 1791,
            243: 1813,
            244: 2009,
            245: 1803,
            246: 3131,
            247: 842,
            248: 842,
            249: 2,
            208: 99528,
            209: 36,
            250: 3,
            251: 2800,
            252: 2800,
            253: 2,
            210: 99983,
            254: 11,
            211: 1,
            255: 41,
            212: 1,
            256: 197,
            213: 3,
            257: 16,
            214: 1,
            258: 3721,
            215: 1,
            259: 3719,
            216: 20,
            217: 2,
            218: 3,
            219: 1,
            260: 1306,
            261: 41,
            262: 194,
            263: 467,
            220: 89795,
            264: 634,
            221: 7,
            265: 11,
            222: 5,
            266: 1,
            223: 5,
            224: 1,
            225: 27,
            226: 27,
            227: 5,
            228: 5,
            229: 6,
          },
          estimateInfo: { previewTimes: 1361, totalScanRows: 116290306 },
          partitionsRows: 91952919,
        },
        datasetType: 1,
        datasetVisible: true,
        dbTypes: [2],
        fieldCategoryOrder: [],
        fieldLayers: [
          { fieldId: 209, fieldLayers: [] },
          { fieldId: 210, fieldLayers: [] },
          { fieldId: 211, fieldLayers: [] },
          { fieldId: 212, fieldLayers: [] },
          { fieldId: 213, fieldLayers: [] },
          { fieldId: 214, fieldLayers: [] },
          { fieldId: 215, fieldLayers: [] },
          { fieldId: 216, fieldLayers: [] },
          { fieldId: 218, fieldLayers: [] },
          { fieldId: 220, fieldLayers: [] },
          { fieldId: 221, fieldLayers: [] },
          { fieldId: 222, fieldLayers: [] },
          { fieldId: 223, fieldLayers: [] },
          { fieldId: 224, fieldLayers: [] },
          { fieldId: 226, fieldLayers: [] },
          { fieldId: 228, fieldLayers: [] },
          { fieldId: 230, fieldLayers: [] },
          { fieldId: 231, fieldLayers: [] },
          { fieldId: 232, fieldLayers: [] },
          { fieldId: 233, fieldLayers: [] },
          { fieldId: 234, fieldLayers: [] },
          { fieldId: 235, fieldLayers: [] },
          { fieldId: 236, fieldLayers: [] },
          { fieldId: 237, fieldLayers: [] },
          { fieldId: 238, fieldLayers: [] },
          { fieldId: 250, fieldLayers: [] },
          { fieldId: 251, fieldLayers: [] },
          { fieldId: 252, fieldLayers: [] },
          { fieldId: 253, fieldLayers: [] },
          { fieldId: 254, fieldLayers: [] },
          { fieldId: 255, fieldLayers: [] },
          { fieldId: 256, fieldLayers: [] },
          { fieldId: 258, fieldLayers: [] },
          { fieldId: 259, fieldLayers: [] },
          { fieldId: 260, fieldLayers: [] },
          { fieldId: 261, fieldLayers: [] },
          { fieldId: 262, fieldLayers: [] },
          { fieldId: 263, fieldLayers: [] },
          { fieldId: 264, fieldLayers: [] },
          { fieldId: 265, fieldLayers: [] },
          { fieldId: 241, fieldLayers: [] },
          { fieldId: 266, fieldLayers: [] },
          { fieldId: 208, fieldLayers: [] },
          { fieldId: 217, fieldLayers: [] },
          { fieldId: 219, fieldLayers: [] },
          { fieldId: 225, fieldLayers: [] },
          { fieldId: 227, fieldLayers: [] },
          { fieldId: 229, fieldLayers: [] },
          { fieldId: 239, fieldLayers: [] },
          { fieldId: 240, fieldLayers: [] },
          { fieldId: 242, fieldLayers: [] },
          { fieldId: 243, fieldLayers: [] },
          { fieldId: 244, fieldLayers: [] },
          { fieldId: 245, fieldLayers: [] },
          { fieldId: 246, fieldLayers: [] },
          { fieldId: 247, fieldLayers: [] },
          { fieldId: 248, fieldLayers: [] },
          { fieldId: 249, fieldLayers: [] },
          { fieldId: 257, fieldLayers: [] },
          { fieldId: 317, fieldLayers: [] },
          { fieldId: 330, fieldLayers: [] },
          { fieldId: 335, fieldLayers: [] },
          { fieldId: 6426, fieldLayers: [] },
          { fieldId: 323, fieldLayers: [] },
          { fieldId: 341, fieldLayers: [] },
        ],
        fieldOrderType: 1,
        fields: [
          {
            alias: 'id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '券模版id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 36,
            fieldId: 209,
            fieldName: 'coupon_template_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'coupon_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            bucket: true,
            comment: '券id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 99983,
            fieldId: 210,
            fieldName: 'coupon_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'act_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '活动id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 1,
            fieldId: 211,
            fieldName: 'act_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'act_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '活动名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 1,
            fieldId: 212,
            fieldName: 'act_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'coupon_template_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '券名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3,
            fieldId: 213,
            fieldName: 'coupon_template_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'claim_start_time',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '券领用开始时间',
            convert: 'DateParse',
            convertArg: { date: 'YYYYMMDD', notUseInFilter: true },
            convertedType: 'Date',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 1,
            fieldId: 214,
            fieldName: 'claim_start_time',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Date',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'claim_end_time',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '券领用截止时间',
            convert: 'DateParse',
            convertedType: 'Date',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 1,
            fieldId: 215,
            fieldName: 'claim_end_time',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Date',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'coupon_thr',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '券门槛',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 20,
            fieldId: 216,
            fieldName: 'coupon_thr',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'coupon_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '薯券名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3,
            fieldId: 218,
            fieldName: 'coupon_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'created_time',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '创建时间',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 89795,
            fieldId: 220,
            fieldName: 'created_time',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'display_time',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '展示时间',
            convert: 'DateParse',
            convertedType: 'Date',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 7,
            fieldId: 221,
            fieldName: 'display_time',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Date',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'claim_channel',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '发券渠道$$6:大促券页面 76:惊喜盒子-搜索商品 78:惊喜盒子-商品笔记-微商详 详见ClaimChannel说明 https://code.devops.xiaohongshu.com/fulishe-idls/coupon/-/blob/develop/coupon_service/constants.thrift#L311',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 5,
            fieldId: 222,
            fieldName: 'claim_channel',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'claim_channel_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '发券渠道名称$$6:大促券页面 76:惊喜盒子-搜索商品 78:惊喜盒子-商品笔记-微商详 详见ClaimChannel说明 https://code.devops.xiaohongshu.com/fulishe-idls/coupon/-/blob/develop/coupon_service/constants.thrift#L311',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 5,
            fieldId: 223,
            fieldName: 'claim_channel_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'coupon_usage',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '券用途$$枚举：新人券、复购券、流失券、其他',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 1,
            fieldId: 224,
            fieldName: 'coupon_usage',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'strategy_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '策略名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 27,
            fieldId: 226,
            fieldName: 'strategy_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'scene_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '场域名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 5,
            fieldId: 228,
            fieldName: 'scene_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'rule_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '规则名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 6,
            fieldId: 230,
            fieldName: 'rule_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'buy_time',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '订单创建时间',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3941,
            fieldId: 231,
            fieldName: 'buy_time',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'pay_time',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '支付时间',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3935,
            fieldId: 232,
            fieldName: 'pay_time',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'package_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '包裹id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3960,
            fieldId: 233,
            fieldName: 'package_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'order_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '订单id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3950,
            fieldId: 234,
            fieldName: 'order_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'channel_group',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '核销渠道组',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 10,
            fieldId: 235,
            fieldName: 'channel_group',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'channel',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '核销渠道',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 16,
            fieldId: 236,
            fieldName: 'channel',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'goods_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商品id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3867,
            fieldId: 237,
            fieldName: 'goods_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'goods_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商品名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3868,
            fieldId: 238,
            fieldName: 'goods_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'user_tag_platform',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '平台新老客',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3,
            fieldId: 250,
            fieldName: 'user_tag_platform',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'seller_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '卖家id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 2800,
            fieldId: 251,
            fieldName: 'seller_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'shop_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商家名称',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 2800,
            fieldId: 252,
            fieldName: 'shop_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'seller_type',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商户类型$$thirdparty:三方 resell:自营',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 2,
            fieldId: 253,
            fieldName: 'seller_type',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'seller_industry',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理主营行业$$eg:面部彩妆 ，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 11,
            fieldId: 254,
            fieldName: 'seller_industry',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'op_seller_main_first_category_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理商家主营一级类目$$eg:面部彩妆 ，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 41,
            fieldId: 255,
            fieldName: 'op_seller_main_first_category_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'op_seller_main_second_category_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理商家主营二级类目$$eg:面部彩妆 ，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 197,
            fieldId: 256,
            fieldName: 'op_seller_main_second_category_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'spu_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商品id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3721,
            fieldId: 258,
            fieldName: 'spu_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'spu_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: 'spu名称$$eg:保湿修护清爽乳液',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 3719,
            fieldId: 259,
            fieldName: 'spu_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'brand_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '品牌名称$$推荐使用 eg：芙丽芳丝',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 1306,
            fieldId: 260,
            fieldName: 'brand_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'op_first_category_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理商品一级类目名称$$行业侧行业类目规则是基于系统工业类目提供商品的1-4级类目，加工成行业和1-5级类目规则，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 41,
            fieldId: 261,
            fieldName: 'op_first_category_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'op_second_category_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理商品二级类目名称$$行业侧行业类目规则是基于系统工业类目提供商品的1-4级类目，加工成行业和1-5级类目规则，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 194,
            fieldId: 262,
            fieldName: 'op_second_category_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'op_third_category_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理商品三级类目名称$$行业侧行业类目规则是基于系统工业类目提供商品的1-4级类目，加工成行业和1-5级类目规则，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 467,
            fieldId: 263,
            fieldName: 'op_third_category_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'op_fourth_category_name',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理商品四级类目名称$$行业侧行业类目规则是基于系统工业类目提供商品的1-4级类目，加工成行业和1-5级类目规则，详见文档说明https://wiki.xiaohongshu.com/pages/viewpage.action?pageid=223401215',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 634,
            fieldId: 264,
            fieldName: 'op_fourth_category_name',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'op_industry',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '运营管理商品行业$$行业侧行业类目规则是基于系统工业类目提供商品的1-4级类目，加工成行业和1-5级类目规则',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 11,
            fieldId: 265,
            fieldName: 'op_industry',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'raw_price',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商品价格',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 898,
            fieldId: 241,
            fieldName: 'raw_price',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'dtm',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '时间',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: true,
            defaultAggregator: 'SUM',
            discrepancy: 1,
            fieldId: 266,
            fieldName: 'dtm',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: true,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: '用户ID',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '访问用户id',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 99528,
            fieldId: 208,
            fieldName: 'user_id',
            granularity: 'g0',
            hidden: false,
            lastUpdateTime: 1698828496340,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'String',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'status',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment:
                '薯券状态$$未显示 invisible_status = 0,未使用 unused_status = 1,已使用redeemed_status = 2, 涉嫌欺诈作废 fraud_status = 993, 不可使用 unavailable_status = 3,',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2,
            fieldId: 217,
            fieldName: 'status',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'coupon_type',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '薯券类型$$1（all_sellers）平台券 4（seller）店铺券',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1,
            fieldId: 219,
            fieldName: 'coupon_type',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'strategy_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '策略id',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 27,
            fieldId: 225,
            fieldName: 'strategy_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'scene_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '场域id',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 5,
            fieldId: 227,
            fieldName: 'scene_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'rule_id',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '规则id',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 6,
            fieldId: 229,
            fieldName: 'rule_id',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'goods_total',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商品数量',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 9,
            fieldId: 239,
            fieldName: 'goods_total',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'sale_price',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '售价',
            convert: 'INT',
            convertedType: 'Whole',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 628,
            fieldId: 240,
            fieldName: 'sale_price',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'discounted_price',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '优惠金额',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1791,
            fieldId: 242,
            fieldName: 'discounted_price',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'rgmv',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: 'rgmv',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1813,
            fieldId: 243,
            fieldName: 'rgmv',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'pgmv',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: 'pgmv',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2009,
            fieldId: 244,
            fieldName: 'pgmv',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'agmv',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: 'agmv$$if deal_gmv/rgmv<0.3 then pgmv else rgmv',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1803,
            fieldId: 245,
            fieldName: 'agmv',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'deal_gmv',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: 'dgmv',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 3131,
            fieldId: 246,
            fieldName: 'deal_gmv',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'coupon_discount',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '优惠金额',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 842,
            fieldId: 247,
            fieldName: 'coupon_discount',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'platform_discount_amount',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '平台券优惠金额',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 842,
            fieldId: 248,
            fieldName: 'platform_discount_amount',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'is_valid',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '是否有效订单',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2,
            fieldId: 249,
            fieldName: 'is_valid',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'discount',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '券满减',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 16,
            fieldId: 257,
            fieldName: 'discount',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 12,
            visible: 1,
          },
          {
            alias: 'user_id自定义',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            defaultGroup: '未分组',
            fieldId: 317,
            fieldName: '',
            granularity: 'g0',
            groups: { '分组 1': { values: ['6070477200000000010086fc'] } },
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Grouping',
            realDataType: 'String',
            refIds: ['208'],
            refTypes: ['String'],
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
          {
            alias: 'price',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '啊啊啊啊啊啊啊啊啊',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            defaultGroup: '其他分组',
            fieldId: 330,
            fieldName: '',
            groups: {
              '分组 3': { values: ['2022-06-10 09:04:01'] },
              '分组 2': {
                values: ['2023-02-22 20:45:08', '2022-11-05 08:03:20', '2022-06-10 09:04:01'],
              },
              '分组 1': { values: ['2022-11-05 08:03:20', '2023-02-22 20:45:08'] },
            },
            hidden: false,
            originField: true,
            produced: 'Grouping',
            realDataType: 'String',
            refIds: ['232'],
            refTypes: ['String'],
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
          {
            alias: '商品价格',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '商品价格分组测试',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultGroup: '未分组',
            fieldId: 335,
            fieldName: '',
            groups: {
              '1-5': {
                range: {
                  excludeMax: false, excludeMin: false, maxBound: 5, minBound: 1,
                },
              },
              '1-10': {
                range: {
                  excludeMax: false, excludeMin: false, maxBound: 10, minBound: 1,
                },
              },
            },
            hidden: false,
            originField: true,
            produced: 'Grouping',
            realDataType: 'String',
            refIds: ['241'],
            refTypes: ['Decimal'],
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
          {
            alias: '时间分区dtm',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '',
            createdManually: false,
            dataType: 'Date',
            dateFlag: false,
            defaultAggregator: 'CNT',
            expr: 'DATEPARSE("yyyyMMdd", Str($$266))',
            fieldId: 6426,
            fieldName: '',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Calculated',
            realDataType: 'Date',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
          {
            alias: '用户id长度',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '用来计算user id 长度',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            expr: 'LENGTH($$208)',
            fieldId: 323,
            fieldName: '',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Calculated',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
          {
            alias: 'qq',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '',
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            expr: 'SUM(10)',
            fieldId: 341,
            fieldName: '',
            granularity: 'g1',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Calculated',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
        ],
        forAnalysis: false,
        forDashboard: true,
        hasDeveloperPerm: true,
        hasManagerPerm: true,
        hasOwnerPerm: true,
        hasReviewerPerm: true,
        id: 12,
        isMirrorDataset: false,
        lastAsyncTime: '2024-06-11T06:08:09',
        lastPartitionValues: { dtm: '20240610' },
        lastUpdateTime: 1715743054065,
        name: 'app_ecm_toc_trd_mkt_activity_coupon_get_use_df',
        nodeId: 35,
        regionFields: [],
      },
      {
        assetAssuranceInfo: { currentLevel: 'A2' },
        characteristic: {
          discrepancies: {
            8156: 1413,
            8178: 1106,
            8157: 469,
            8179: 1104,
            8154: 2591,
            8176: 1105,
            8155: 2180,
            8177: 1106,
            8158: 61,
            8159: 97,
            8170: 1037,
            8171: 1054,
            8152: 586,
            8174: 1106,
            8153: 1718,
            8175: 1105,
            8150: 2587,
            8172: 1045,
            8151: 2567,
            8173: 1106,
            8145: 755,
            8167: 1103,
            8146: 109,
            8168: 1088,
            8165: 45,
            8144: 3,
            8166: 1103,
            8149: 1750,
            8147: 2516,
            8169: 1088,
            8148: 1320,
            8181: 1105,
            8160: 440,
            8182: 1105,
            8180: 1106,
            8163: 122,
            8164: 121,
            8161: 150,
            8183: 1105,
            8184: 3,
            8162: 21,
          },
          estimateInfo: { previewTimes: 158, totalScanRows: 43911 },
          partitionsRows: 896,
        },
        datasetType: 1,
        datasetVisible: true,
        dbTypes: [3],
        fieldCategoryOrder: [],
        fieldLayers: [
          { fieldId: 8145, fieldLayers: [] },
          { fieldId: 8184, fieldLayers: [] },
          { fieldId: 8144, fieldLayers: [] },
          { fieldId: 8146, fieldLayers: [] },
          { fieldId: 8147, fieldLayers: [] },
          { fieldId: 8148, fieldLayers: [] },
          { fieldId: 8149, fieldLayers: [] },
          { fieldId: 8150, fieldLayers: [] },
          { fieldId: 8151, fieldLayers: [] },
          { fieldId: 8152, fieldLayers: [] },
          { fieldId: 8153, fieldLayers: [] },
          { fieldId: 8154, fieldLayers: [] },
          { fieldId: 8155, fieldLayers: [] },
          { fieldId: 8156, fieldLayers: [] },
          { fieldId: 8157, fieldLayers: [] },
          { fieldId: 8158, fieldLayers: [] },
          { fieldId: 8159, fieldLayers: [] },
          { fieldId: 8160, fieldLayers: [] },
          { fieldId: 8161, fieldLayers: [] },
          { fieldId: 8162, fieldLayers: [] },
          { fieldId: 8163, fieldLayers: [] },
          { fieldId: 8164, fieldLayers: [] },
          { fieldId: 8165, fieldLayers: [] },
          { fieldId: 8166, fieldLayers: [] },
          { fieldId: 8167, fieldLayers: [] },
          { fieldId: 8168, fieldLayers: [] },
          { fieldId: 8169, fieldLayers: [] },
          { fieldId: 8170, fieldLayers: [] },
          { fieldId: 8171, fieldLayers: [] },
          { fieldId: 8172, fieldLayers: [] },
          { fieldId: 8173, fieldLayers: [] },
          { fieldId: 8174, fieldLayers: [] },
          { fieldId: 8175, fieldLayers: [] },
          { fieldId: 8176, fieldLayers: [] },
          { fieldId: 8177, fieldLayers: [] },
          { fieldId: 8178, fieldLayers: [] },
          { fieldId: 8179, fieldLayers: [] },
          { fieldId: 8180, fieldLayers: [] },
          { fieldId: 8181, fieldLayers: [] },
          { fieldId: 8182, fieldLayers: [] },
          { fieldId: 8183, fieldLayers: [] },
          { fieldId: 8252, fieldLayers: [] },
          { fieldId: 8253, fieldLayers: [] },
        ],
        fieldOrderType: 1,
        fields: [
          {
            alias: 'date_key',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            convert: 'DateParse',
            convertArg: { date: 'YYYY-MM-DD', notUseInFilter: true },
            convertedType: 'Date',
            createdManually: false,
            dataType: 'String',
            dateFlag: false,
            defaultAggregator: 'CNT',
            discrepancy: 755,
            fieldId: 8145,
            fieldName: 'date_key',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Date',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'dtm',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            convert: 'DateParse',
            convertArg: { date: 'YYYY-MM-DD', notUseInFilter: true, time: 'HH:MI:SS' },
            convertedType: 'DateTime',
            createdManually: false,
            dataType: 'String',
            dateFlag: true,
            defaultAggregator: 'CNT',
            discrepancy: 3,
            fieldId: 8184,
            fieldName: 'dtm',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'DateTime',
            role: 'Dimension',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'grain',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 3,
            fieldId: 8144,
            fieldName: 'grain',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'date_key_rank',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 109,
            fieldId: 8146,
            fieldName: 'date_key_rank',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2516,
            fieldId: 8147,
            fieldName: 'cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'open_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1320,
            fieldId: 8148,
            fieldName: 'open_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'feed_gd_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1750,
            fieldId: 8149,
            fieldName: 'feed_gd_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'brand_zone_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2587,
            fieldId: 8150,
            fieldName: 'brand_zone_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'other_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2567,
            fieldId: 8151,
            fieldName: 'other_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'order_adj_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 586,
            fieldId: 8152,
            fieldName: 'order_adj_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'marketing_and_order_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1718,
            fieldId: 8153,
            fieldName: 'marketing_and_order_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'other_non_standard_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2591,
            fieldId: 8154,
            fieldName: 'other_non_standard_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'ip_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 2180,
            fieldId: 8155,
            fieldName: 'ip_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'dmp_cost',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1413,
            fieldId: 8156,
            fieldName: 'dmp_cost',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 469,
            fieldId: 8157,
            fieldName: 'company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'open_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 61,
            fieldId: 8158,
            fieldName: 'open_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'feed_gd_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 97,
            fieldId: 8159,
            fieldName: 'feed_gd_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'brand_zone_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 440,
            fieldId: 8160,
            fieldName: 'brand_zone_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'other_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 150,
            fieldId: 8161,
            fieldName: 'other_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'order_adj_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 21,
            fieldId: 8162,
            fieldName: 'order_adj_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'marketing_and_order_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 122,
            fieldId: 8163,
            fieldName: 'marketing_and_order_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'ip_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 121,
            fieldId: 8164,
            fieldName: 'ip_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'dmp_company_num',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 45,
            fieldId: 8165,
            fieldName: 'dmp_company_num',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'open_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1103,
            fieldId: 8166,
            fieldName: 'open_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'open_click_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1103,
            fieldId: 8167,
            fieldName: 'open_click_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_open_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1088,
            fieldId: 8168,
            fieldName: 'no_red_open_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_open_click_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1088,
            fieldId: 8169,
            fieldName: 'no_red_open_click_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'open_stock_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1037,
            fieldId: 8170,
            fieldName: 'open_stock_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'open_buy_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1054,
            fieldId: 8171,
            fieldName: 'open_buy_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_open_buy_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1045,
            fieldId: 8172,
            fieldName: 'no_red_open_buy_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'feed_gd_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1106,
            fieldId: 8173,
            fieldName: 'feed_gd_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'feed_gd_click_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1106,
            fieldId: 8174,
            fieldName: 'feed_gd_click_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_feed_gd_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1105,
            fieldId: 8175,
            fieldName: 'no_red_feed_gd_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_feed_gd_click_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1105,
            fieldId: 8176,
            fieldName: 'no_red_feed_gd_click_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'feed_gd_stock_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1106,
            fieldId: 8177,
            fieldName: 'feed_gd_stock_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'feed_gd_buy_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1106,
            fieldId: 8178,
            fieldName: 'feed_gd_buy_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_feed_gd_buy_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1104,
            fieldId: 8179,
            fieldName: 'no_red_feed_gd_buy_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'brand_zone_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1106,
            fieldId: 8180,
            fieldName: 'brand_zone_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'brand_zone_click_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1105,
            fieldId: 8181,
            fieldName: 'brand_zone_click_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_brand_zone_imp_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1105,
            fieldId: 8182,
            fieldName: 'no_red_brand_zone_imp_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: 'no_red_brand_zone_click_cnt',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            createdManually: false,
            dataType: 'Whole',
            dateFlag: false,
            defaultAggregator: 'SUM',
            discrepancy: 1105,
            fieldId: 8183,
            fieldName: 'no_red_brand_zone_click_cnt',
            granularity: 'g0',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Existed',
            realDataType: 'Whole',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 185,
            visible: 1,
          },
          {
            alias: '开屏填充率',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            expr: 'sum($$8168)/sum($$8170)',
            fieldId: 8252,
            fieldName: '',
            granularity: 'g1',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Calculated',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
          {
            alias: '信息流GD填充率',
            analysisDimensionRequired: false,
            analysisFilterGroupRequired: false,
            analysisFilterRequired: false,
            analysisMaxQueryDays: 0,
            analysisOnlyForFilter: false,
            comment: '',
            createdManually: false,
            dataType: 'Decimal',
            dateFlag: false,
            defaultAggregator: 'SUM',
            expr: 'sum($$8175)/sum($$8177)',
            fieldId: 8253,
            fieldName: '',
            granularity: 'g1',
            hidden: false,
            originField: true,
            partition: false,
            produced: 'Calculated',
            realDataType: 'Decimal',
            role: 'Measure',
            selected: false,
            state: 0,
            tableId: 1,
            visible: 1,
          },
        ],
        forAnalysis: false,
        forDashboard: true,
        hasDeveloperPerm: true,
        hasManagerPerm: true,
        hasOwnerPerm: true,
        hasReviewerPerm: true,
        id: 164,
        isMirrorDataset: false,
        lastAsyncTime: '2024-06-10T16:42:08',
        lastPartitionValues: { dtm: '2024-06-09 00:00:00' },
        lastUpdateTime: 1715743054568,
        name: '定价广告日周月报表',
        nodeId: 297,
        regionFields: [],
      },
    ],
    id: 32,
    nodeId: 64,
    owner: {
      departmentName: '数据应用研发组',
      departmentNames: ['中台技术部', '数据平台部', '数据应用研发组'],
      displayName: '韩立(陈苏洲)',
      redEmail: '<EMAIL>',
      userAvatar: 'https://wework.qpic.cn/wwpic3az/571775_9WkTLnxkQYi0KBQ_1703736262/0',
      userEmail: '<EMAIL>',
      userType: 1,
    },
    pages: [
      {
        title: '页面1',
        id: 'page_Dtsa6PKo9c',
        visible: true,
        index: 0,
        filter: { filters: [], otherParams: {} },
      },
    ],
    project: {
      canApply: false,
      defaultAttributionCreatePerm: true,
      defaultDashboardCreatePerm: true,
      defaultDatasetCreatePerm: true,
      hasAttributionCreatePerm: false,
      hasDashboardCreatePerm: false,
      hasDatasetCreatePerm: false,
      hasProjectManagerPerm: false,
      hasProjectUserPerm: false,
      hasSubProjects: false,
      managers: [],
      openSubProject: true,
      projectId: 3,
      projectName: 'TEST项目组',
      remarks: '分析工具产研内部测试项目',
      type: 3,
      users: [],
    },
    rowPermLabels: [],
    setting: { gridLine: true },
    structure: {
      page_Dtsa6PKo9c: [
        {
          layout: 'container',
          id: 'container_rqiOflc0AD',
          x: 0,
          y: 0,
          w: 24,
          h: 67,
          children: [
            {
              layout: 'slot',
              id: 'slot_m3KY1AEmL9',
              children: [
                {
                  layout: 'chart', id: 'chart_2hcX412msz', w: 13, h: 10, x: 0, y: 51,
                },
                {
                  id: 'chart_t72zkLCL8g', layout: 'chart', x: 0, y: 69, w: 24, h: 10,
                },
                {
                  id: 'chart_g2TUBAxQHy', layout: 'chart', x: 12, y: 61, w: 12, h: 8,
                },
                {
                  id: 'chart_VBhBACPdgg', layout: 'chart', w: 12, h: 8, x: 0, y: 0,
                },
                {
                  id: 'chart_J0JoglBYdY', layout: 'chart', w: 24, h: 10, x: 0, y: 33,
                },
                {
                  id: 'chart_o2RNj4wmCu', layout: 'chart', w: 12, h: 8, x: 0, y: 43,
                },
                {
                  id: 'chart_MLujoJWqWI', layout: 'chart', w: 24, h: 25, x: 0, y: 8,
                },
              ],
            },
            {
              layout: 'slot',
              id: 'slot_gIndpMcNSP',
              children: [{
                id: 'chart_oQS7YYmTMJ', layout: 'chart', w: 24, h: 16, x: 0, y: 0,
              }],
            },
          ],
        },
        {
          id: 'container_oK3G0PbUrI',
          layout: 'container',
          w: 24,
          h: 17,
          x: 0,
          y: 67,
          children: [
            {
              layout: 'slot',
              id: 'slot_pflw7pYejz',
              children: [
                {
                  id: 'chart_5cm6aAVzTe', layout: 'chart', w: 12, h: 8, x: 0, y: 0,
                },
                {
                  id: 'chart_w0PotAjGEl', layout: 'chart', w: 12, h: 8, x: 12, y: 0,
                },
                {
                  id: 'chart_1L3qCJqpz8', layout: 'chart', w: 12, h: 8, x: 12, y: 8,
                },
              ],
            },
          ],
        },
      ],
    },
    title: '韩立测试看板',
    updated: 1686042081008,
  },
  chartId: 'chart_J0JoglBYdY',
  exportConfig: {},
}
