<template>
  <div class="container">
    <Button @click="handleGetAnalysisExportDSL">获取自助分析导出dsl</Button>
    <Button @click="handleGetAttributionExportDSL">获取指标诊断导出dsl</Button>
    <Button @click="handleGetDashboardExportDSL">获取看板导出dsl</Button>
    <Button @click="handleDSLConfig">DSLConfig</Button>
    <Button @click="handleGetAnalysisDSL">获取分析DSL</Button>

  </div>
</template>

<script setup lang="ts">
  import { But<PERSON> } from '@xhs/delight'
  import { computed } from 'vue'
  import { useRoute } from 'vue-router'
  import { transformDslToConfig, getAnalysisDSL } from '@xhs/redbi-share-dsl'
  import { getAnalysisExportDSL } from './analysis'
  import { getAttributionExportDSL } from './attribution'
  import { getDashboardExportDSL } from './dashboard'

  import { dsl2ConfigMock, datasetMock } from './dsltoconfigmock'
  import { anaDslParams } from './mock.getAnaDsl'

  const route = useRoute()
  // const currentProjectId = computed(() => route.query.projectId)
  const analysisId = computed(() => route.query.analysisId || '')
  const dashboardId = computed(() => route.query.dashboardId || '')
  const chartId = computed(() => route.query.chartId || '')
  const attributionId = computed(() => route.query.attributionId || '')

  const handleGetAnalysisExportDSL = () => {
    getAnalysisExportDSL(String(analysisId.value), String(dashboardId.value), String(chartId.value))
  }

  const handleGetAttributionExportDSL = () => {
    getAttributionExportDSL(Number(attributionId.value))
  }

  const handleDSLConfig = () => {
    // eslint-disable-next-line
    console.log(transformDslToConfig({
      dsl: dsl2ConfigMock as any,
      dataset: datasetMock,
    }))
  }
  const handleGetDashboardExportDSL = () => {
    const dsl = getDashboardExportDSL()
    return dsl
  }
  const handleGetAnalysisDSL = () => {
    const analysisInfo = anaDslParams?.analysisInfo
    const datasetInfo = anaDslParams?.datasetInfo
    const fieldInfo = anaDslParams?.fieldInfo

    // eslint-disable-next-line
    console.log(getAnalysisDSL(analysisInfo as any, datasetInfo, fieldInfo, {}))
  }
</script>

<style lang="stylus" scoped>
    .container
        padding 12px
        display flex
        flex-direction row
        column-gap 10px
</style>
