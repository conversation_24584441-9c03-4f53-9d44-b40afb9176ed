export const dsl2ConfigMock = {
  filter: {
    measures: [],
    dimensions: [
      {
        filter: {
          range: {
            rangeBoundary: {
              maxBound: '2025-03-20',
              minBound: '2025-03-20',
            },
          },
        },
        dimension: {
          fieldName: 'dtm',
          dataType: 'Whole',
          tableId: 478408,
          mapFunction: 'YEAR-MONTH-DAY',
          convertArg: { date: 'YYYYMMDD', notUseInFilter: true },
          fieldIdForStats: 621396,
          convert: 'DateParse',
          produced: 'Existed',
        },
      },
    ],
  },
  chartConfig: {},
  measures: [
    {
      aggregator: 'SUM',
      expr: 'DM_IDEA($$621375)',
      fieldIdForStats: 621472,
      produced: 'Calculated',
    },
    {
      compareConfig: {
        customConfig: { start: '', end: '' },
        compareTypes: ['yoy'],
        valueTypes: ['growthRate'],
        dateDimension: {
          fieldName: 'dtm',
          dataType: 'Whole',
          tableId: 478408,
          mapFunction: 'YEAR-MONTH-DAY',
          convertArg: { date: 'YYYYMMDD', notUseInFilter: true },
          fieldIdForStats: 621396,
          convert: 'DateParse',
          produced: 'Existed',
        },
      },
      aggregator: 'SUM',
      expr: 'DM_IDEA($$621375)',
      fieldIdForStats: 621472,
      produced: 'Calculated',
    },
    {
      compareConfig: {
        customConfig: { start: '', end: '' },
        compareTypes: ['yoy'],
        valueTypes: ['growthValue'],
        dateDimension: {
          fieldName: 'dtm',
          dataType: 'Whole',
          tableId: 478408,
          mapFunction: 'YEAR-MONTH-DAY',
          convertArg: { date: 'YYYYMMDD', notUseInFilter: true },
          fieldIdForStats: 621396,
          convert: 'DateParse',
          produced: 'Existed',
        },
      },
      aggregator: 'SUM',
      expr: 'DM_IDEA($$621375)',
      fieldIdForStats: 621472,
      produced: 'Calculated',
    },
  ],
  offset: 0,
  queryOption: {
    queryMode: 'SYNC',
    useMV: true,
    onlyReadCache: false,
    datasetDynamicParams: {},
    useTablePrune: true,
    needMemoryPaging: false,
    flushTime: '1970-01-01 00:00:00',
    validateMvOnly: false,
  },
  limit: 500,
  orderBy: [],
  datasetId: 10192,
  subQueryDatasets: [],
  aggregate: true,
  dimensions: [
    {
      fieldName: 'brand_name',
      dataType: 'String',
      tableId: 478408,
      fieldIdForStats: 621351,
      produced: 'Existed',
    },
    {
      fieldName: 'dtm',
      dataType: 'Whole',
      tableId: 478408,
      mapFunction: 'YEAR-MONTH-DAY',
      convertArg: { date: 'YYYYMMDD', notUseInFilter: true },
      fieldIdForStats: 621396,
      convert: 'DateParse',
      produced: 'Existed',
    },
  ],
}

export const datasetMock = {
  id: 10192,
  name: '笔记品牌互动数据每日增量表',
  lastUpdateTime: 1742628673764,
  hasOwnerPerm: false,
  hasManagerPerm: false,
  hasDeveloperPerm: false,
  hasReviewerPerm: true,
  fields: [
    {
      datasetId: null,
      fieldId: 621351,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'brand_name',
      alias: '笔记对应的品牌名',
      fieldAliasList: [
        '笔记对应的品牌名',
      ],
      comment: '与笔记相关联的品牌的名称，如"迪奥"，"香奈儿"',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 8800,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: 1941,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 837,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 621352,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'discovery_id',
      alias: '笔记id',
      fieldAliasList: [
        '笔记id',
      ],
      comment: '笔记的唯一标识符。',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 99994,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: 7474,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 809,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 621353,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'create_time',
      alias: '笔记创建时间',
      fieldAliasList: [
        '创建时间',
      ],
      comment: '创建时间',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: 'DateParse',
      convertedType: 'DateTime',
      convertArg: {
        date: 'YYYY-MM-DD',
        time: 'HH:MI:SS',
        notUseInFilter: true,
      },
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 2239,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 838,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'DateTime',
    },
    {
      datasetId: null,
      fieldId: 621354,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'is_brand',
      alias: '是否企业号',
      fieldAliasList: [
        '是否企业号',
      ],
      comment: '发布笔记的用户账号是否属于企业官方账号，布尔枚举值：1/0',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 2,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '用户',
      analysisQueryCount: 71,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 839,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621355,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'is_bind',
      alias: '是否报备笔记',
      fieldAliasList: null,
      comment: '是否为向平台报备的品牌合作笔记，布尔枚举值：1/0',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 2,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1720173339378,
      dict: null,
      category: '笔记',
      analysisQueryCount: 93,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621356,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'level',
      alias: '笔记审核状态',
      fieldAliasList: [
        '笔记level',
      ],
      comment: '',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 13,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742287111662,
      dict: null,
      category: '笔记',
      analysisQueryCount: 4000,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 819,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621357,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'title',
      alias: '笔记标题',
      fieldAliasList: [
        '笔记标题',
      ],
      comment: '笔记的标题',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 87510,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 810,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 621358,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'taxonomy1',
      alias: '社区一级类目',
      fieldAliasList: [
        '社区一级类目,笔记一级类目名称,笔记一级分类,关键词一级类目',
      ],
      comment: '社区类目划分，和社区侧对齐\n',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 41,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: 2507,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 765,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 621359,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'taxonomy2',
      alias: '社区二级类目',
      fieldAliasList: [
        '社区二级类目,笔记二级类目名称,笔记二级分类',
      ],
      comment: '社区类目划分，和社区侧对齐\n',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 267,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: 538,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 766,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 621360,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'taxonomy3',
      alias: '社区三级类目',
      fieldAliasList: [
        '社区三级类目,笔记三级类目名称,笔记三级分类',
      ],
      comment: '社区类目划分，和社区侧对齐\n',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 458,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: 137,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 767,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 621361,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'read_feed_num',
      alias: '社区阅读量',
      fieldAliasList: [
        '阅读数',
        '阅读次数',
      ],
      comment: '笔记产生的阅读次数（自然+广告）',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 540,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 3842,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621362,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'like_num',
      alias: '搜索双列笔记点赞数',
      fieldAliasList: [],
      comment: '搜索双列笔记点赞数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 112,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742266541460,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 1713,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621363,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'fav_num',
      alias: '搜索双列笔记收藏数',
      fieldAliasList: [],
      comment: '搜索双列笔记收藏数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 70,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742266560687,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 1714,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621364,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'cmt_num',
      alias: '搜索双列笔记评论数',
      fieldAliasList: [],
      comment: '搜索双列笔记评论数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 38,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742266579204,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 1715,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621365,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'share_num',
      alias: '搜索双列笔记分享数',
      fieldAliasList: [],
      comment: '搜索双列笔记分享数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 41,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742266595201,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 1716,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621366,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'follow_from_discovery_num',
      alias: '关注数',
      fieldAliasList: null,
      comment: '关注数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 47,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621367,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'imp_num',
      alias: '社区曝光量',
      fieldAliasList: [
        '曝光数',
        '曝光次数',
      ],
      comment: '笔记产生的曝光次数（自然+广告）',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 1276,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 3841,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621368,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'click_num',
      alias: '商卡点击次数',
      fieldAliasList: [
        '商卡点击数',
      ],
      comment: '商品卡片点击次数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 498,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 1610,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621369,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'imp_search_num',
      alias: '曝光次数--搜索',
      fieldAliasList: null,
      comment: '曝光次数--搜索',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 799,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621370,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'click_search_num',
      alias: '点击次数--搜索',
      fieldAliasList: null,
      comment: '点击次数--搜索',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 310,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621371,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'imp_homefeed_num',
      alias: '发现feed曝光数',
      fieldAliasList: [],
      comment: 'algo口径发现feed曝光数，不包括内流',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 793,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 1702,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621372,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'click_homefeed_num',
      alias: '发现feed点击数',
      fieldAliasList: [],
      comment: 'algo口径发现feed点击数，不包括内流',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 357,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 1805,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621373,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'video_views',
      alias: '播放次数',
      fieldAliasList: null,
      comment: '播放次数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 378,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621374,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'full_views',
      alias: '完播次数',
      fieldAliasList: null,
      comment: '完播次数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 192,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621375,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_imp_num',
      alias: '广告流量总曝光数',
      fieldAliasList: null,
      comment: '广告流量不去重曝光数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 201,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621376,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_imp_homefeed_num',
      alias: '广告流量发现页曝光数',
      fieldAliasList: null,
      comment: '广告流量不去重曝光数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 118,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621377,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_imp_search_num',
      alias: '广告流量搜索曝光数',
      fieldAliasList: null,
      comment: '广告流量不去重曝光数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 118,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621378,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_click_num',
      alias: '广告流量总点击数',
      fieldAliasList: null,
      comment: '广告流量不去重曝光数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 95,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621379,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_click_homefeed_num',
      alias: '广告流量发现页点击数',
      fieldAliasList: null,
      comment: '广告流量不去重曝光数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 72,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621380,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_click_search_num',
      alias: '广告流量搜索页点击数',
      fieldAliasList: null,
      comment: '广告流量不去重曝光数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 52,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621381,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_like_num',
      alias: '广告归因-总的点赞数',
      fieldAliasList: null,
      comment: '广告归因-总的点赞数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 19,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621382,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_cmt_num',
      alias: '广告归因-总的评论数',
      fieldAliasList: null,
      comment: '广告归因-总的评论数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 5,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621383,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_share_num',
      alias: '广告归因-总的分享数',
      fieldAliasList: null,
      comment: '广告归因-总的分享数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 6,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621384,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_fav_num',
      alias: '广告归因-总的收藏数',
      fieldAliasList: null,
      comment: '广告归因-总的收藏数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 15,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621385,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_follow_num',
      alias: '广告归因-总的关注数',
      fieldAliasList: null,
      comment: '广告归因-总的关注数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 9,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621386,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_feed_like_num',
      alias: '广告归因-发现feed点赞数',
      fieldAliasList: null,
      comment: '广告归因-发现feed点赞数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 15,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621387,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_search_like_num',
      alias: '广告归因-搜索feed点赞数',
      fieldAliasList: null,
      comment: '广告归因-搜索feed点赞数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 9,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621388,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_feed_cmt_num',
      alias: '广告归因-发现feed评论数',
      fieldAliasList: null,
      comment: '广告归因-发现feed评论数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 4,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621389,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_search_cmt_num',
      alias: '广告归因-搜索页评论数',
      fieldAliasList: null,
      comment: '广告归因-搜索页评论数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 2,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621390,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_feed_share_num',
      alias: '广告归因-发现feed分享数',
      fieldAliasList: null,
      comment: '广告归因-发现feed分享数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 5,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621391,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_search_share_num',
      alias: '广告归因-搜索页分享数',
      fieldAliasList: null,
      comment: '广告归因-搜索页分享数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 4,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621392,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_feed_fav_num',
      alias: '广告归因-发现feed收藏数',
      fieldAliasList: null,
      comment: '广告归因-发现feed收藏数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 11,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621393,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_search_fav_num',
      alias: '广告归因-搜索页收藏数',
      fieldAliasList: null,
      comment: '广告归因-搜索页收藏数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 8,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621394,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_feed_follow_num',
      alias: '广告归因-发现feed关注数',
      fieldAliasList: null,
      comment: '广告归因-发现feed关注数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 7,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621395,
      produced: 'Existed',
      role: 'Measure',
      fieldName: 'ads_search_follow_num',
      alias: '广告归因-搜索页关注数',
      fieldAliasList: null,
      comment: '广告归因-搜索页关注数',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 5,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1703496235619,
      dict: null,
      category: null,
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621396,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'dtm',
      alias: '日期',
      fieldAliasList: null,
      comment: '数据日期，表示增量指标数据统计的日期',
      dataType: 'Whole',
      partition: true,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: 'DateParse',
      convertedType: 'Date',
      convertArg: {
        date: 'YYYYMMDD',
        time: null,
        notUseInFilter: true,
      },
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: true,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 1,
      analysisFilterDefault: false,
      analysisFilterRequired: true,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 731,
      lastUpdateTime: 1720173339378,
      dict: null,
      category: '时间',
      analysisQueryCount: 9389,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Date',
    },
    {
      datasetId: null,
      fieldId: 621397,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'topic_name',
      alias: '话题名称',
      fieldAliasList: [
        '话题名称',
      ],
      comment: '笔记关联的话题标签，如"我的护肤日常"，"美食随手拍"',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 79580,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742628673764,
      dict: null,
      category: '笔记',
      analysisQueryCount: 345,
      bucket: false,
      defaultOrderBy: {
        type: 'normal',
        config: {
          asc: false,
          custom: null,
        },
      },
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 842,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 621398,
      produced: 'Existed',
      role: 'Dimension',
      fieldName: 'brand_id',
      alias: '品牌ID',
      fieldAliasList: [
        '品牌ID',
      ],
      comment: '与笔记相关联的品牌的唯一标识符，若未关联品牌，则值为0',
      dataType: 'Whole',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: null,
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: 9058,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: 3498,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 843,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Whole',
    },
    {
      datasetId: null,
      fieldId: 621402,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区曝光量',
      fieldAliasList: [
        '曝光量-脱敏',
      ],
      comment: '笔记的曝光数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621367)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '流量',
      analysisQueryCount: 5794,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4159,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621404,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区点击量',
      fieldAliasList: [
        '点击量-脱敏',
        '阅读量-脱敏',
      ],
      comment: '用户点击该笔记的总次数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621368)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '流量',
      analysisQueryCount: 5291,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4130,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621405,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区互动量',
      fieldAliasList: [
        '互动数-脱敏',
      ],
      comment: '用户与笔记的全渠道所有互动的总数，包括点赞、评论、分享等。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621363 + $$621364 + $$621362 + $$621365 + $$621366)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '互动',
      analysisQueryCount: 4080,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4148,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621471,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区新发布笔记数',
      fieldAliasList: [
        '新发布笔记数-脱敏',
      ],
      comment: '在对应日期范围内新发布的笔记数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g1',
      defaultAggregator: 'ATTR',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: "DM_IDEA(rawsql_agg('Whole', 'APPROX_COUNT_DISTINCT(%1)', $$695513))",
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '笔记管理',
      analysisQueryCount: 3752,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4155,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621472,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区广告曝光量',
      fieldAliasList: [
        '广告曝光量-脱敏',
      ],
      comment: '该笔记以推广流量曝光的数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621375)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '流量',
      analysisQueryCount: 2784,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4118,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621475,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区广告点击量',
      fieldAliasList: [
        '广告点击量-脱敏',
      ],
      comment: '该笔记以推广流量曝光的点击数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621378)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '流量',
      analysisQueryCount: 2773,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4122,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621476,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区广告互动量',
      fieldAliasList: [
        '广告互动数-脱敏',
      ],
      comment: '该笔记以推广流量曝光的互动数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621384 + $$621385 + $$621382 + $$621381 + $$621383)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '互动',
      analysisQueryCount: 2677,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4163,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621478,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区自然曝光量',
      fieldAliasList: [
        '自然曝光量-脱敏',
      ],
      comment: '该笔记在自然流量的曝光数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621367 - $$621375)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '流量',
      analysisQueryCount: 2794,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4147,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621479,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区自然点击量',
      fieldAliasList: [
        '自然点击量-脱敏',
      ],
      comment: '该笔记在自然流量曝光的点击数。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'DM_IDEA($$621368 - $$621378)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '流量',
      analysisQueryCount: 2756,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4160,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 621483,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区自然互动量',
      fieldAliasList: [
        '自然互动数-脱敏',
      ],
      comment: '该笔记在自然流量曝光的所有互动的总数，包括点赞、评论、分享等。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'SUM',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: '$$621405 - $$621476',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '互动',
      analysisQueryCount: 2683,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4125,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 695513,
      produced: 'Calculated',
      role: 'Dimension',
      fieldName: '',
      alias: '新笔记id',
      fieldAliasList: [
        '新笔记id',
      ],
      comment: '',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: 'iif(date($$621396)= date($$621353),$$621352)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: null,
      category: '笔记',
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: 844,
      dimensionSync: true,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 733943,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '品牌名称',
      fieldAliasList: null,
      comment: '与笔记相关联的品牌的名称，如"I Do"。',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g1',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'MAX($$621351)',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1739880729841,
      dict: null,
      category: null,
      analysisQueryCount: 3645,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 1028096,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区有曝光笔记数',
      fieldAliasList: [
        '有曝光笔记数-脱敏',
      ],
      comment: '该笔记在字段"日期"的日期值下是否有曝光。布尔枚举值：1/0。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g1',
      defaultAggregator: 'ATTR',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: "DM_IDEA( rawsql_agg( 'Whole' , 'APPROX_COUNT_DISTINCT(%1)' ,(if $$621367 > 0 then $$621352) ) )",
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697124,
      dict: null,
      category: '笔记管理',
      analysisQueryCount: 499,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4131,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 1028099,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区广告有曝光笔记数',
      fieldAliasList: [
        '广告有曝光笔记数-脱敏',
      ],
      comment: '该笔记是否在"日期"当天通过推广流量曝光。布尔枚举值：1/0。属于脱敏后的数据，较原始数据有所膨胀。',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 478408,
      logicModelTableId: null,
      granularity: 'g1',
      defaultAggregator: 'ATTR',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: "DM_IDEA( rawsql_agg( 'Whole' , 'APPROX_COUNT_DISTINCT(%1)' , IF $$621375> 0 THEN $$621352 ) )",
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: '笔记管理',
      analysisQueryCount: 33,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: 4145,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 1552044,
      produced: 'Calculated',
      role: 'Measure',
      fieldName: '',
      alias: '脱敏社区点击内容渗透率',
      fieldAliasList: [
        '点击量-内容渗透率',
      ],
      comment: '脱敏社区点击内容渗透率',
      dataType: 'Decimal',
      partition: false,
      indexed: null,
      table: null,
      tableId: 1,
      logicModelTableId: null,
      granularity: 'g1',
      defaultAggregator: 'ATTR',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 1,
      expr: 'SUM($$621404) / SUM({exclude $$621351: SUM($$621404)})',
      refIds: null,
      refId: null,
      refTypes: null,
      refType: null,
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1741612965047,
      dict: null,
      category: null,
      analysisQueryCount: 178,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: {
        type: 'selectFiltersNotInUse',
        fieldIdList: [
          621351,
        ],
      },
      percent: 0,
      indicatorId: 4165,
      indicatorSync: true,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'Decimal',
    },
    {
      datasetId: null,
      fieldId: 1617515,
      produced: 'DataDict',
      role: 'Dimension',
      fieldName: '',
      alias: '是否企业号(映射值)',
      fieldAliasList: [
        '是否企业号',
      ],
      comment: '发布笔记的用户账号是否属于企业官方账号，布尔枚举值：1/0',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 1,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: '621354',
      refTypes: null,
      refType: 'String',
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742286697781,
      dict: [
        {
          k: '0',
          v: '否',
          type: 1,
          enumAlias: null,
        },
        {
          k: '1',
          v: '是',
          type: 1,
          enumAlias: null,
        },
      ],
      category: '用户',
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
    {
      datasetId: null,
      fieldId: 1618700,
      produced: 'DataDict',
      role: 'Dimension',
      fieldName: '',
      alias: '笔记审核状态(映射值)',
      fieldAliasList: [
        '笔记level',
      ],
      comment: '',
      dataType: 'String',
      partition: false,
      indexed: null,
      table: null,
      tableId: 1,
      logicModelTableId: null,
      granularity: 'g0',
      defaultAggregator: 'CNT',
      convert: null,
      convertedType: null,
      convertArg: null,
      mapFunction: null,
      state: 0,
      hidden: false,
      visible: 2,
      expr: null,
      refIds: null,
      refId: '621356',
      refTypes: null,
      refType: 'String',
      groups: null,
      defaultGroup: null,
      dateFlag: false,
      regionType: null,
      dashboardId: null,
      analysisId: null,
      discrepancy: null,
      analysisFilterDefault: false,
      analysisFilterRequired: false,
      analysisDimensionRequired: false,
      analysisOnlyForFilter: false,
      analysisFilterGroupRequired: false,
      analysisMaxQueryDays: 0,
      lastUpdateTime: 1742287111662,
      dict: [
        {
          k: '-102',
          v: '作弊惩罚等级二(推荐不召回、搜索不进前100位，其他地方可见)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '4',
          v: '机器通过(全站可曝光)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '5',
          v: '限流-流量调节(全站曝光，但发现feed根据违规原因进行不同程度降权，搜索沉底（“综合”排序沉底，“最新”排序7天沉底）)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '-101',
          v: '作弊惩罚等级一(全站曝光（推荐&搜索降权）)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '-103',
          v: '作弊惩罚等级三(推荐不召回、搜索不进前100位，其他地方可见)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '-2',
          v: '仅粉丝可见(所有人看作者的个人页可见、粉丝看关注feed可见，可分享&可编辑，分享后部分情况不可见（有标签结果且标签的来源source包含security则不可见），发现feed和搜索都不可见)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '1',
          v: '未审核(仅作者在自己的个人页可见、作者看自己的关注feed可见)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '2',
          v: '人工通过(全站可曝光)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '-1',
          v: '自嗨（介于下架、静默自嗨的中间程度 ）(仅作者在自己的个人页可见，不可分享 & 可编辑 （默认有贴条）)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '-5',
          v: '静默自嗨（pr不可见）(仅作者在自己的个人页可见，可分享&分享后不可见，可编辑（无贴条）)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '3',
          v: '精选',
          type: 1,
          enumAlias: null,
        },
        {
          k: '0',
          v: '仅个人页可见(所有人看作者的个人页可见、作者看自己的关注feed可见，可分享&可编辑，分享后不可见（有标签结果且标签的来源source包含security则不可见），发现feed和搜索都不可见)',
          type: 1,
          enumAlias: null,
        },
        {
          k: '-3',
          v: '下架(仅作者在自己的个人页可见，不可分享&不可编辑（默认有贴条）)',
          type: 1,
          enumAlias: null,
        },
      ],
      category: '笔记',
      analysisQueryCount: null,
      bucket: false,
      defaultOrderBy: null,
      selected: false,
      createdManually: false,
      resourceId: null,
      resourceType: null,
      defaultShowAs: null,
      defaultFormatter: null,
      lodFilterConfig: null,
      percent: 0,
      indicatorId: null,
      indicatorSync: null,
      dimensionId: null,
      dimensionSync: null,
      dimensionLayerSync: null,
      originField: true,
      realDataType: 'String',
    },
  ],
  fieldLayers: [
    {
      fieldId: 621351,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621352,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621353,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621354,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621355,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621356,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621357,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621358,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621359,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621360,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621361,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621362,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621363,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621364,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621365,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621366,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621367,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621368,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621369,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621370,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621371,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621372,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621373,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621374,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621375,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621376,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621377,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621378,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621379,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621380,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621381,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621382,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621383,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621384,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621385,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621386,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621387,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621388,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621389,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621390,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621391,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621392,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621393,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621394,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621395,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621396,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621397,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621398,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621402,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621404,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621405,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621471,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621472,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621475,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621476,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621478,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621479,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 621483,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 695513,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 733943,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 1028096,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 1028099,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 1552044,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 1617515,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
    {
      fieldId: 1618700,
      layerName: null,
      fieldLayers: [],
      depth: 1,
    },
  ],
  dbTypeList: [
    3,
  ],
  lastPartitionValues: null,
  fieldCategories: [
    '笔记',
    '时间',
    '品牌',
    '流量',
    '互动',
    '笔记管理',
    '测试',
    '类目',
    '用户',
  ],
  fieldCategoryOrder: [
    621396,
    621358,
    621359,
    621360,
    621352,
    621397,
    621355,
    621356,
    621398,
    621351,
    695513,
    621353,
    621357,
    1618700,
    621354,
    1617515,
    733943,
    1552044,
    621361,
    621362,
    621363,
    621364,
    621365,
    621366,
    621367,
    621368,
    621369,
    621370,
    621371,
    621372,
    621373,
    621374,
    621375,
    621376,
    621377,
    621378,
    621379,
    621380,
    621381,
    621382,
    621383,
    621384,
    621385,
    621386,
    621387,
    621388,
    621389,
    621390,
    621391,
    621392,
    621393,
    621394,
    621395,
    621402,
    621404,
    621472,
    621475,
    621478,
    621479,
    621405,
    621476,
    621483,
    621471,
    1028096,
    1028099,
  ],
  fieldOrderType: 2,
  regionFields: [],
  params: [],
  isMirrorDataset: null,
  sourceDataset: null,
  userNameMap: {},
  characteristic: {
    discrepancy: null,
    discrepancies: {
      621351: 8800,
      621352: 99994,
      621353: 2239,
      621354: 2,
      621355: 2,
      621356: 13,
      621357: 87510,
      621358: 41,
      621359: 267,
      621360: 458,
      621361: 540,
      621362: 112,
      621363: 70,
      621364: 38,
      621365: 41,
      621366: 47,
      621367: 1276,
      621368: 498,
      621369: 799,
      621370: 310,
      621371: 793,
      621372: 357,
      621373: 378,
      621374: 192,
      621375: 201,
      621376: 118,
      621377: 118,
      621378: 95,
      621379: 72,
      621380: 52,
      621381: 19,
      621382: 5,
      621383: 6,
      621384: 15,
      621385: 9,
      621386: 15,
      621387: 9,
      621388: 4,
      621389: 2,
      621390: 5,
      621391: 4,
      621392: 11,
      621393: 8,
      621394: 7,
      621395: 5,
      621396: 1,
      621397: 79580,
      621398: 9058,
    },
    estimateInfo: {
      totalScanRows: 1243251955530,
      previewTimes: 10567,
    },
    partitionsRows: null,
    partitionsMaxRows: null,
    partitionsTotalRows: null,
    sqlComplexity: null,
    lastBizDate: {},
  },
  mainTable: {
    databaseName: 'reddm',
    tableName: 'dm_soc_brand_discovery_engage_day_inc',
    fullTableName: 'reddm.dm_soc_brand_discovery_engage_day_inc',
    fieldName: 'dtm',
    tableId: null,
    partitionfieldList: null,
  },
  datasetType: 1,
}
