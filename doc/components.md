# 组件

## 项目中的通用组件

### PillList

> pill 列表，用于字段列表、图表数据域列表。（拖拽等功能带扩充）

- 位置：`@dashboard/components/pill/PillList.vue`

#### 使用说明

Props

```ts
{
    /**
     * pill列表
     */
    pills: Field[]
    /**
     * 是否使用背景色
     */
    bgColor?: boolean
}
```

### Canvas - 编辑器画布

> 用于可视化报表页面布局的搭建。

- 位置：`@dashboard/components/Canvas`

#### 使用说明

代码用例：

```ts
// 导入组件
import Canvas, { Draggable } from `@dashboard/components/Canvas`
```

```jsx
<!-- Draggable 组件用于包装单个物料菜单项 -->
<!-- 使其具有拖拽(到画布)、触发布局重排的能力 -->
<Draggable
  class="marterial-item"
  drag-text="新增折线图"
  :drag-size="{ w: 4, h: 4 }"
  @drop="..."
>
  <!-- 物料菜单项(拖拽到画布内可生成具体的控件元素) -->
</Draggable>

<!-- Canvas 组件用于编排页面的整体布局 -->
<!-- 通过具名插槽(menu)传入物料菜单UI -->
<Canvas
  v-model="schema"
  :containers="{ TabContainer }"
  :charts="{ LineChart, BarChart, PieChart, ... }"
  :padding="[48, 24]"
  :margin="[16, 16]"
  :detect-element-draggable="..."
  @focus="..."
  @blur="..."
>
  <template #menu>
    <!-- 物料菜单(由 “控件添加按钮” 唤出) -->
  </template>
</Canvas>
```

### GUI - 可视化配置面板

> 用于生成可视化图形配置项的 UI 面板。面板的内容通过数据定义(schema)且完全受控

- 位置：`@xhs/redbi-share-gui`

#### 使用说明

代码用例：
```ts
// 导入组件
import GUI from '@xhs/redbi-share-gui'
```

```jsx
<GUI
  v-model="guiValue"
  :schema="guiSchema"
  :components="{ MyComponent, ... }"
  @custom-event="..."
  @panel-change="..."
/>
```


### PageTabContainer & TabContainer - 支持配置化样式以及拖动的Tab容器

> 用于存在拖动需求的Tab容器，支持结合Canvas使用

- 位置：`@dashboard/components/editor/container`

#### 使用说明
#### `Props`
 
| 参数      | 说明    | 类型      | 可选值       | 默认值   |
|---------- |-------- |---------- |-------------  |-------- |
| id | 容器ID，全局唯一 | string | — | — |
| slots | 容器TabItem，格式与下面`PageItem`或`TabItem`一致 | array | — | — |
| setting | 配置选项，格式与下面`Setting`一致 | object | — | — |
| editable | 是否可编辑，容器会根据此字段控制是否支持Tab的crud | boolean | true / false | true |
| maxWidth | 右侧配置面板宽度，容器会在右侧自动预留一定控件，编辑页使用，默认340 | number | number |

```ts
interface PageItem {
  id: string
  title?: string
  index: number
  visible: boolean
  filter: filterItem
}

interface TabItem {
  id: string
  title: string
  description?: string
  visible: boolean
  grid?: number
  padding?: [number, number]
  margin?: [number, number]
}

interface Setting {
  [key: string]: Record<string, any>
}
```

#### `Events`
| 事件名称      | 说明    | 回调参数      |
|---------- |-------- |---------- |
| beforeChange | 容器选中之前 | - |
| add | 新增tab | { tabInfo, tabList }, tabInfo代表当前新增的tab信息,tabList代表新增后所有tab的信息 |
| remove | 删除tab |  { tabInfo, tabList }, tabInfo代表当前新增的tab信息,tabList代表新增后所有tab的信息  |
| change | tab信息改变时触发 | { tabInfo, tabList }, tabInfo代表当前新增的tab信息,tabList代表新增后所有tab的信息 |
| select | 当选中Tab变化时触发 | { tabInfo, tabList }, tabInfo代表当前新增的tab信息,tabList代表新增后所有tab的信息 |

代码用例：
```ts
// 导入组件
import PageTabContainer from '@dashboard/components/editor/PageTabContainer'
```

```jsx
<PageTabContainer
  id="..."
  :slots="..."
  :setting="{...}"
  :editable="false"
  @change="handlePageChange"
/>
```

### UserInfoPanel 用户信息展示模块，支持点击拉起企微聊天

> 用于展示用户署名，真名，头像等信息

- 位置：`@dashboard/components/common/UserInfoPanel`

#### 使用说明
#### `Props`
 
| 参数      | 说明    | 类型      | 可选值       | 默认值   |
|---------- |-------- |---------- |-------------  |-------- |
| userInfo | 用户信息，格式和下面IUserInfo一致 | object | — | — |

```ts
interface IUserInfo {
  avatar: string // 用户头像
  redName?: string // 署名
  username: string // 用户真实姓名
  email: string    // 用户真实姓名邮箱，用于获取企微拉起schema
}
```

代码用例：
```ts
// 导入组件
import UserInfoPanel from '@dashboard/components/common/UserInfoPanel'
```

```jsx
<UserInfoPanel :userInfo={
  avatar: '...'
  redName?: '希多'
  username: '吴云根'
  email: '<EMAIL>'
} />
/>
```
